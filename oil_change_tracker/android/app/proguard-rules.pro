# Flutter wrapper
-keep class io.flutter.app.** { *; }
-keep class io.flutter.plugin.**  { *; }
-keep class io.flutter.util.**  { *; }
-keep class io.flutter.view.**  { *; }
-keep class io.flutter.**  { *; }
-keep class io.flutter.plugins.**  { *; }
-keep class io.flutter.plugin.editing.** { *; }
-dontwarn io.flutter.embedding.**

# Firebase
-keep class com.google.firebase.** { *; }
-keep class com.google.android.gms.** { *; }
-keep class com.google.firebase.crashlytics.** { *; }

# Fix for resource ID issues
-keepattributes SourceFile,LineNumberTable,*Annotation*,InnerClasses,Signature,Exceptions,EnclosingMethod
-keep class **.R
-keep class **.R$* {
    <fields>;
}
-keepclassmembers class **.R$* {
    public static <fields>;
}

# Fix for Google Play Services security exception
-keep class com.google.android.gms.ads.identifier.** { *; }
-keep class com.google.android.gms.common.internal.** { *; }
-keep class com.google.android.gms.common.api.** { *; }
-keep class com.google.android.gms.dynamic.** { *; }
-keep public class com.google.android.gms.ads.**{
    public *;
}

# Add explicit rules to fix security exception for Google API Manager
-keep class com.google.android.gms.internal.** { *; }
-keep class com.google.android.gms.ads.MobileAds { *; }
-keep class com.google.android.gms.ads.initialization.** { *; }
-keep class com.google.android.gms.auth.api.** { *; }
-keep class com.google.android.gms.measurement.** { *; }
-keepattributes *Annotation*

# Keep specific classes related to the security exception
-keep public class com.google.android.gms.internal.ads.zzdu { *; }
-keep public class com.google.android.gms.internal.ads.zzfj { *; }
-keep public class com.google.android.gms.internal.ads.zzgq { *; }
-keep public class com.google.android.gms.internal.measurement.** { *; }
-keep class com.google.android.gms.common.api.GoogleApiClient { *; }
-keep class com.google.android.gms.common.api.Result { *; }
-keep class com.google.android.gms.common.ConnectionResult { *; }
-keep class com.google.android.gms.measurement.AppMeasurementInstallReferrerReceiver { *; }

# Fix for Google API Manager error
-keep class com.google.android.gms.phenotype.** { *; }
-keep class com.google.android.gms.dynamite.** { *; }
-keep class com.google.android.gms.internal.** { *; }

# BouncyCastle
-keep class org.bouncycastle.** { *; }
-dontwarn org.bouncycastle.**

# Fix for R8 issues with missing OkHttp classes
-keep class com.squareup.okhttp.** { *; }
-dontwarn com.squareup.okhttp.**
-dontwarn okio.**

# Fix for reflection issues
-keepclassmembers class * {
    @java.lang.reflect.* *;
}
-keep class java.lang.reflect.** { *; }
-dontwarn java.lang.reflect.**

# Gson
-keepattributes Signature
-keepattributes *Annotation*
-dontwarn sun.misc.**
-keep class com.google.gson.examples.android.model.** { <fields>; }
-keep class * implements com.google.gson.TypeAdapter
-keep class * implements com.google.gson.TypeAdapterFactory
-keep class * implements com.google.gson.JsonSerializer
-keep class * implements com.google.gson.JsonDeserializer
-keepclassmembers,allowobfuscation class * {
  @com.google.gson.annotations.SerializedName <fields>;
}

# Keep your model classes
-keep class com.maximummdeia.oil_change_tracker.models.** { *; }

# --- Explicitly keep the Main Activity referenced in the Manifest ---
-keep class com.maximummdeia.oil_change_tracker.MainActivity { *; }
# ------------------------------------------------------------------

# Crashlytics
-keepattributes SourceFile,LineNumberTable
-keep public class * extends java.lang.Exception

# Keep Kotlin Coroutines
-keepclassmembernames class kotlinx.** {
    volatile <fields>;
}

# General
-keepattributes InnerClasses
# -dontoptimize  # Commented out to allow optimization
# -dontshrink   # Commented out to allow shrinking

# Flutter specific rules
-keepattributes Signature
-keepattributes *Annotation*
-keepattributes SourceFile,LineNumberTable

# Keep Flutter entry points
-keep class io.flutter.app.** { *; }
-keep class io.flutter.plugin.**  { *; }
-keep class io.flutter.util.**  { *; }
-keep class io.flutter.view.**  { *; }
-keep class io.flutter.**  { *; }
-keep class io.flutter.plugins.**  { *; }

# Firebase rules
-keep class com.google.firebase.** { *; }
-dontwarn com.google.firebase.**

# Kotlin serialization
-keepattributes *Annotation*, InnerClasses
-dontnote kotlinx.serialization.AnnotationsKt

# Image libraries
-keep public class com.horcrux.svg.** { *; }

# Image Picker and FileProvider
-keep class androidx.core.app.CoreComponentFactory { *; }
-keep class androidx.core.content.FileProvider { *; }
-keep class androidx.core.content.FileProvider$** { *; }
-keepattributes *Annotation*

# Prevent obfuscation of FileProvider XML parsing-keep class android.content.res.XmlBlock$Parser { *; }-keep interface e3.a { *; }-keep class * implements e3.a { *; }# Additional XML parsing interface rules-keep interface org.xmlpull.v1.XmlPullParser { *; }-keep class * implements org.xmlpull.v1.XmlPullParser { *; }-keep class android.content.res.XmlResourceParser { *; }-keep interface android.content.res.XmlResourceParser { *; }# Prevent obfuscation of all XML-related classes-keep class android.content.res.** { *; }-keep interface android.content.res.** { *; }-dontwarn android.content.res.**

# Keep XML resource parsing classes
-keep class androidx.core.content.res.** { *; }
-dontwarn androidx.core.content.res.**

# Image Picker plugin specific rules
-keep class io.flutter.plugins.imagepicker.** { *; }
-keep class io.flutter.plugins.imagepicker.ImagePickerDelegate { *; }
-keep class io.flutter.plugins.imagepicker.ImagePickerDelegate$* { *; }
-dontwarn io.flutter.plugins.imagepicker.**

# Keep image picker related classes
-keep class androidx.exifinterface.media.ExifInterface { *; }
-keep class androidx.exifinterface.media.ExifInterface$* { *; }

# Additional optimization
-optimizations !code/simplification/arithmetic,!code/simplification/cast,!field/*,!class/merging/*
-optimizationpasses 5

# For native methods - if any are used
-keepclasseswithmembernames class * {
    native <methods>;
}

# Preserve the line number information for debugging stack traces
-keepattributes SourceFile,LineNumberTable

# Remove unused code from support libraries
-dontwarn androidx.**
-keep class androidx.** { *; }
-keep interface androidx.** { *; }
-dontwarn android.support.**

# Preserve enumeration classes
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

# For serialization/deserialization in Firestore
-keepclassmembers class ** implements java.io.Serializable {
    private static final java.io.ObjectStreamField[] serialPersistentFields;
    private void writeObject(java.io.ObjectOutputStream);
    private void readObject(java.io.ObjectInputStream);
    java.lang.Object writeReplace();
    java.lang.Object readResolve();
}

# Application classes that will be serialized/deserialized
-keep class com.maximummdeia.oil_change_tracker.** { *; }

# Keep custom models
-keep class com.maximummdeia.oil_change_tracker.models.** { *; }

# OkHttp
-keepattributes Signature
-keepattributes *Annotation*
-keep class okhttp3.** { *; }
-keep interface okhttp3.** { *; }
-dontwarn okhttp3.**
-dontwarn okio.**

# gRPC
-keep class io.grpc.** { *; }
-dontwarn io.grpc.**

# Models - prevent obfuscation of model classes
-keep class com.maximummdeia.oil_change_tracker.models.** { *; }

# AdMob / Play Services Ads
-keep class com.google.android.gms.ads.** { *; }
-keep class com.google.ads.** { *; }
-keep public class com.google.android.gms.ads.AdActivity { *; }
-keep public class com.google.android.gms.ads.InterstitialAd { *; }
-keep public class com.google.android.gms.ads.reward.RewardedVideoAd { *; } # Keep rewarded video even if not used directly, mediation might need it
-keep public class com.google.android.gms.ads.reward.mediation.MediationRewardedVideoAdAdapter { *; }
-keep public class com.google.android.gms.ads.formats.** { *; }
-keep public class com.google.android.gms.ads.mediation.** { *; }

# Google Sign-In specific rules
-keep class com.google.android.gms.auth.** { *; }
-keep class com.google.android.gms.signin.** { *; }
-keep class com.google.android.gms.common.** { *; }
-keep class com.google.api.client.** { *; }
-keepattributes Signature,RuntimeVisibleAnnotations,AnnotationDefault

# Keep the Google Sign-In plugin classes
-keep class io.flutter.plugins.googlesignin.** { *; }
-keep class com.google.android.gms.auth.api.identity.** { *; }
-keep class com.google.android.gms.auth.api.signin.** { *; }
-keep class com.google.android.gms.tasks.** { *; }

# Add project specific ProGuard rules here.
# By default, the flags in this file are appended to flags specified
# in /usr/local/Cellar/android-sdk/24.3.3/tools/proguard/proguard-android.txt
# You can edit the include path and order by changing the proguardFiles
# directive in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# Uncomment this to preserve the line number information for
# debugging stack traces.
-keepattributes SourceFile,LineNumberTable

# If you keep the line number information, uncomment this to
# hide the original source file name.
-renamesourcefileattribute SourceFile

# Keep Material Icon fonts
-keep class **.MaterialIcons { *; }
-keep class **.MaterialIcons-Regular { *; }
-keep class **.MaterialIcons$ { *; }

# Keep font-related classes
-keep class androidx.core.provider.FontsContractCompat { *; }
-keep class androidx.core.provider.FontRequest { *; }
-keep class androidx.core.provider.FontsContractCompat$FontInfo { *; }
-keep class androidx.core.provider.FontsContractCompat$FontRequestCallback { *; }

# Keep all font resources
-keep class **.R$font { *; }
-keep class **.R$array { *; }

# Add comprehensive Google Sign-In ProGuard rules
-keepattributes Signature,InnerClasses

# Google Sign-In
-keep class com.google.android.gms.auth.api.signin.** { *; }
-keep class com.google.android.gms.common.api.** { *; }
-keep class com.google.android.gms.common.ConnectionResult { *; }
-keep class com.google.android.gms.signin.** { *; }
-keep class com.google.android.gms.tasks.** { *; }
-keep class com.google.android.gms.auth.api.identity.** { *; }

# OAuth related classes
-keep class com.google.android.gms.auth.** { *; }
-keep class com.google.api.client.** { *; }
-keep class com.google.api.services.** { *; }

# Keep all internal classes in Google libraries
-keep class **.internal.** { *; }

# Keep Material Icon fonts
-keep class **.MaterialIcons { *; }
-keep class **.MaterialIcons-Regular { *; }
-keep class **.MaterialIcons$ { *; }

# Keep font-related classes
-keep class androidx.core.provider.FontsContractCompat { *; }
-keep class androidx.core.provider.FontRequest { *; }
-keep class androidx.core.provider.FontsContractCompat$FontInfo { *; }
-keep class androidx.core.provider.FontsContractCompat$FontRequestCallback { *; }

# Keep all font resources
-keep class **.R$font { *; }
-keep class **.R$array { *; }

# Keep Geolocator and location-related classes
-keep class com.google.android.gms.location.** { *; }
-keep class com.android.location.** { *; }
-keep class com.baseflow.geolocator.** { *; }
-keep class com.baseflow.geocoding.** { *; }
-keep class com.baseflow.location.** { *; }
-keep class com.google.android.gms.location.LocationRequest { *; }
-keep class com.google.android.gms.common.api.** { *; }
-keep class android.location.** { *; }
-keep class androidx.core.location.** { *; }

# Keep foreground service functionality
-keep class androidx.core.app.JobIntentService { *; }
-keep class androidx.core.app.NotificationCompat { *; }
-keep class android.app.Notification* { *; }
-keep class android.app.Service { *; }
-keep class android.app.ForegroundService { *; }
-keep class android.app.NotificationChannel { *; }

# Keep the location permission related classes
-keep class android.app.AppOpsManager { *; }
-keep class android.content.pm.PackageManager { *; }
-keep class androidx.core.content.ContextCompat { *; }

# Keep JSON handling classes
-keep class org.json.** { *; }

# Keep Google Play Services ads identifier
-keep class com.google.android.gms.ads.identifier.** { *; }
-keep class com.google.android.gms.ads.** { *; }
-keep class com.google.android.gms.common.** { *; }

# Keep classes that are accessed via reflection
-keepattributes *Annotation*
-keepattributes SourceFile,LineNumberTable
-keep public class * extends java.lang.Exception

# Keep the Advertising ID library
-keep class com.google.android.gms.ads.identifier.AdvertisingIdClient { 
    public *; 
}
-keep class com.google.android.gms.ads.identifier.AdvertisingIdClient$Info { 
    public *; 
}

# Fix critical security exception for Google Play Services
-keep class com.google.android.gms.dynamic.** { *; }
-keep class com.google.android.gms.internal.consent_sdk.** { *; }
-keep class com.google.android.gms.common.internal.safeparcel.SafeParcelable { *; }
-keep class com.google.android.gms.common.api.Status { *; }
-keep class com.google.android.gms.clearcut.** { *; }
-keep class com.google.android.gms.phenotype.** { *; }

# Fix Phenotype API error
-keep class com.google.android.gms.flags.** { *; }
-keep class com.google.android.gms.nearby.** { *; }
-keep class com.google.firebase.installations.** { *; }
-keep class com.google.android.gms.tasks.** { *; }

# Explicit keep for the problematic classes
-keep class com.google.android.gms.ads.identifier.AdvertisingIdClient** { *; }
-keep class com.google.android.gms.ads.identifier.internal.** { *; }
-keep class com.google.android.gms.gcm.** { *; }

# Consistency for package name instances - DO NOT CHANGE mdeia spelling
-keep class com.maximummdeia.oil_change_tracker.** { *; }
-keep class com.maximummdeia.** { *; } 