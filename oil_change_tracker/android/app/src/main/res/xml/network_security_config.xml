<?xml version="1.0" encoding="utf-8"?>
<network-security-config>
    <!-- Block cleartext traffic in production -->
    <base-config cleartextTrafficPermitted="false">
        <trust-anchors>
            <certificates src="system" />
        </trust-anchors>
    </base-config>
    
    <!-- Allow connection to standard API endpoints -->
    <domain-config cleartextTrafficPermitted="true">
        <domain includeSubdomains="true">openweathermap.org</domain>
        <domain includeSubdomains="true">googleapis.com</domain>
        <domain includeSubdomains="true">google.com</domain>
        <domain includeSubdomains="true">gstatic.com</domain>
        <domain includeSubdomains="true">googleusercontent.com</domain>
        <domain includeSubdomains="true">firebaseio.com</domain>
        <domain includeSubdomains="true">firebase.com</domain>
        <domain includeSubdomains="true">google-analytics.com</domain>
        <domain includeSubdomains="true">doubleclick.net</domain>
        <domain includeSubdomains="true">googleadservices.com</domain>
        
        <trust-anchors>
            <certificates src="system" />
            <certificates src="user" />
        </trust-anchors>
    </domain-config>

    <domain-config cleartextTrafficPermitted="true">
        <domain includeSubdomains="true">********</domain>
        <domain includeSubdomains="true">localhost</domain>
    </domain-config>
</network-security-config> 