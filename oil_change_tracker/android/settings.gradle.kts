pluginManagement {
    val flutterSdkPath = run {
        val properties = java.util.Properties()
        file("local.properties").inputStream().use { properties.load(it) }
        val flutterSdkPath = properties.getProperty("flutter.sdk")
        require(flutterSdkPath != null) { "flutter.sdk not set in local.properties" }
        flutterSdkPath
    }

    includeBuild("$flutterSdkPath/packages/flutter_tools/gradle")

    repositories {
        google()
        mavenCentral()
        gradlePluginPortal()
    }
}

plugins {
    id("dev.flutter.flutter-plugin-loader") version "1.0.0"
    id("com.android.application") version "8.3.0" apply false
    id("org.jetbrains.kotlin.android") version "2.0.0" apply false
}

// Configure all projects in the build
buildscript {
    repositories {
        google()
        mavenCentral()
    }
}

// Exclude problematic plugins
gradle.settingsEvaluated {
    pluginManagement {
        repositories {
            google()
            mavenCentral()
            gradlePluginPortal()
        }
        
        // Filter out problematic plugins
        resolutionStrategy {
            eachPlugin {
                if (requested.id.namespace == "com.google.gms" || 
                    requested.id.name == "google-mobile-ads" || 
                    requested.id.name == "google-services") {
                    useModule("com.google.gms:google-services:4.4.0")
                }
            }
        }
    }
}

include(":app")
