org.gradle.jvmargs=-Xmx4096M -XX:MaxMetaspaceSize=1024m -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8
org.gradle.parallel=true
org.gradle.daemon=true
org.gradle.caching=true
android.useAndroidX=true
android.enableJetifier=true
android.defaults.buildfeatures.buildconfig=true
android.nonTransitiveRClass=true
android.nonFinalResIds=false
android.suppressUnsupportedCompileSdk=35
org.gradle.configureondemand=true
org.gradle.workers.max=4

# Fix Windows path issues on macOS/Linux
org.gradle.daemon.jvm.options=-Dfile.encoding=UTF-8
org.gradle.jvmargs=-Dfile.encoding=UTF-8

# Disable kotlin incremental compilation to fix build issues
kotlin.incremental=false
kotlin.incremental.java=false
kotlin.incremental.useClasspathSnapshot=false
kapt.incremental.apt=false

# Disable Jetifier for BouncyCastle library to fix compatibility issues
android.jetifier.ignorelist=bcprov-jdk18on-1.78.1.jar

# Disable google_mobile_ads plugin to fix build issues
google_mobile_ads.enabled=false

# Disable icon tree-shaking to prevent font subsetting errors with CupertinoIcons
flutter.no-tree-shake-icons=true
