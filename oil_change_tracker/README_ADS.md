# Oil Change Tracker - Ad Implementation Guide

## Overview
This guide explains the implementation of mobile ads (interstitial and app open ads) in the Oil Change Tracker app, including how to test and troubleshoot them.

## Ad Implementation
The app uses Google Mobile Ads SDK (AdMob) for displaying two types of ads:

1. **App Open Ads**: Shown when the app is opened or resumed from background
2. **Interstitial Ads**: Full-screen ads that can be shown between content transitions

## Test Ad Configuration
The app automatically uses test ad units in debug mode to avoid policy violations:

- Test Ad Units IDs are defined in `lib/features/ads/models/ad_configuration.dart`
- Production Ad Unit IDs are stored in the same file
- The implementation automatically switches between test and production IDs based on build mode

## How to Test Ads
Two special test entry points are provided:

1. **Ad Test App**: Run `flutter run -t lib/test_ads.dart` to launch a dedicated test app for testing both ad types.
2. **App Check Verification**: Run `flutter run -t lib/verify_app_check.dart` to test the App Check implementation with the new token.

### Using the Ad Test App
The Ad Test App provides a simple UI for testing:

- **App Open Ads**: Use the "Load Ad" button to preload an app open ad, then "Show Ad" to display it.
- **Interstitial Ads**: Use the "Load Ad" button to preload an interstitial ad, then "Show Ad" to display it.

### Common Ad Issues & Solutions
If ads aren't showing properly, check these common issues:

1. **Connection Issues**:
   - Ensure you have a stable internet connection
   - Check if test ads are loading in debug mode
   
2. **Ad Unit ID Issues**:
   - Verify that the correct ad unit IDs are set in `AdConfiguration.dart`
   - For testing, ensure you're using official AdMob test IDs
   
3. **Initialization Problems**:
   - Ensure `MobileAds.instance.initialize()` is properly called before loading ads
   - Check for any initialization errors in logs
   
4. **Device Compatibility**:
   - Add your test device ID to `AdConfiguration.testDeviceIds`
   - Get your test device ID by looking for "Use this device as a test device" in logs

## Implementation Details

### Ad Configuration
- `lib/features/ads/models/ad_configuration.dart`: Central configuration for ad settings

### App Open Ads
- `lib/features/ads/application/app_open_ad_manager.dart`: Core implementation
- `lib/features/ads/presentation/managers/app_open_ad_manager.dart`: UI-layer adapter

### Interstitial Ads
- `lib/features/ads/application/interstitial_ad_service.dart`: Core implementation
- `lib/features/ads/presentation/managers/interstitial_ad_manager.dart`: UI-layer adapter

## Recent Updates
The following updates were made to fix ad-related issues:

1. Created `AdConfiguration` model to centralize test and production ad unit IDs
2. Updated the UI layer ad managers to correctly delegate to application implementations
3. Fixed initialization in `_initializeGoogleMobileAds()` method
4. Added proper error handling and retry mechanisms
5. Created a test app (`test_ads.dart`) for easy testing

## Troubleshooting
If ads still aren't working after following the steps above:

1. Review logs for specific error messages
2. Check if ads work in the test app but not in the main app
3. Ensure App Check isn't blocking ad requests
4. Try clearing app cache and reinstalling the app on the test device
5. Verify that the test device is not on a restricted network

## Testing Schedule
To avoid hitting request limits during testing:
- Limit ad requests during development
- Use the test app for targeted testing
- Implement reasonable retry intervals (already configured) 