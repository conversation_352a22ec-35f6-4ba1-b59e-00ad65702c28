name: oil_change_tracker
description: A new Flutter project.
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+26

environment:
  sdk: '>=3.2.6 <4.0.0'

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  intl: ^0.20.2
  go_router: ^13.2.0
  flutter_riverpod: ^2.4.10
  riverpod_annotation: ^2.3.4
  freezed_annotation: ^2.4.1
  json_annotation: ^4.8.1
  firebase_core: ^2.24.2
  firebase_auth: ^4.16.0
  cloud_firestore: ^4.14.0
  google_sign_in: ^6.2.2
  flutter_speed_dial: ^7.0.0
  flutter_secure_storage: ^9.0.0
  app_settings: ^5.1.1
  
  # Firebase
  firebase_storage: ^11.6.0
  firebase_messaging: ^14.7.10
  # firebase_app_check: ^0.2.1+8  # Temporarily disabled due to iOS dependency conflict
  firebase_crashlytics: ^3.4.9
  firebase_analytics: ^10.8.0
  firebase_remote_config: ^4.3.8
  
  # Authentication
  google_sign_in_android: ^6.1.20
  
  # State Management
  flutter_hooks: ^0.20.3
  hooks_riverpod: ^2.4.9
  percent_indicator: ^4.2.4
  
  # Local Storage
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  shared_preferences: ^2.2.2
  path_provider: ^2.1.5
  
  # UI Components
  cupertino_icons: ^1.0.2
  google_fonts: ^6.1.0
  fl_chart: ^0.65.0
  flutter_svg: ^2.0.9
  cached_network_image: ^3.4.1
  shimmer: ^3.0.0
  flutter_slidable: ^3.0.1
  settings_ui: ^2.0.2
  
  # Voice Input
  record: ^5.0.5
  flutter_sound: ^9.2.13
  permission_handler: ^11.0.1
  
  # Subscription
  in_app_purchase: ^3.1.11
  
  # Utilities
  uuid: ^4.2.1
  url_launcher: ^6.1.14
  package_info_plus: ^4.2.0
  image_picker: ^0.8.9
  connectivity_plus: ^5.0.2
  json_serializable: ^6.7.1
  http: ^1.1.0
  image: ^4.3.0
  permission_handler_platform_interface: ^4.3.0
  flutter_animate: ^4.5.0
  flutter_staggered_animations: ^1.1.1
  smooth_page_indicator: ^1.2.1
  flutter_local_notifications: ^18.0.1
  timeago: ^3.7.0
  geolocator: ^11.0.0
  cloud_functions: ^4.7.6
  logger: ^2.5.0
  timezone: ^0.9.1
  flutter_cache_manager: ^3.4.1
  google_mobile_ads: ^4.0.0  # Keep at this version for compatibility
  # webview_flutter: ^4.4.2  # Temporarily disabled due to compatibility issues
  # webview_flutter_android: 3.12.1  # Temporarily disabled due to compatibility issues
  timeline_tile: ^2.0.0
  device_info_plus: ^9.0.0
  rxdart: ^0.27.7
  google_speech: ^2.1.1
  openai_dart: ^0.5.2
  flutter_markdown: ^0.6.18

dev_dependencies:
  flutter_test:
    sdk: flutter
  integration_test:
    sdk: flutter
  build_runner: ^2.4.8
  freezed: ^2.4.6
  riverpod_generator: ^2.3.10

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml`

# The following section is for asset management
flutter:
  generate: true
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/icons/
    - assets/config.json
    - assets/privacy_policy.html
    - assets/terms_and_conditions.html

  # Add font assets for custom icon fonts
  fonts:
    - family: MaterialIcons
      fonts:
        - asset: assets/fonts/MaterialIcons-Regular.ttf
    - family: Roboto
      fonts:
        - asset: assets/fonts/Roboto-Regular.ttf
        - asset: assets/fonts/Roboto-Medium.ttf
          weight: 500
        - asset: assets/fonts/Roboto-Bold.ttf
          weight: 700
        - asset: assets/fonts/Roboto-Light.ttf
          weight: 300