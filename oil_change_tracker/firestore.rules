rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    // Common functions
    function isSignedIn() {
      return request.auth != null;
    }
    
    function isOwner(userId) {
      return isSignedIn() && request.auth.uid == userId;
    }
    
    // User collection rules
    match /users/{userId} {
      allow read: if isOwner(userId);
      
      // Allow create with validation for licenseExpiryDate
      allow create: if isSignedIn() && request.auth.uid == userId;
      
      // Simplified update rule - still validates user ownership but more permissive with data types
      allow update: if isOwner(userId);
      
      allow delete: if isOwner(userId);
      
      // Nested collections
      match /settings/{settingId} {
        allow read, write: if isOwner(userId);
      }
      
      match /notifications/{notificationId} {
        allow read, write: if isOwner(userId);
      }
      
      // Location data collection for weather functionality
      match /location_data/{locationId} {
        allow read, write: if isOwner(userId);
      }
      
      // AI usage tracking collection
      match /ai_usage/{usageId} {
        allow read, write: if isOwner(userId);
      }
    }
    
    // Weather cache collection
    match /weather_cache/{cacheId} {
      allow read: if isSignedIn() && resource.data.userId == request.auth.uid;
      allow create, update: if isSignedIn() && request.resource.data.userId == request.auth.uid;
      allow delete: if isSignedIn() && resource.data.userId == request.auth.uid;
    }
    
    // Location data collection (root level)
    match /location_data/{locationId} {
      allow read: if isSignedIn() && resource.data.userId == request.auth.uid;
      allow create, update: if isSignedIn() && request.resource.data.userId == request.auth.uid;
      allow delete: if isSignedIn() && resource.data.userId == request.auth.uid;
    }
    
    // License notifications collection for expiry reminders
    match /license_notifications/{notificationId} {
      allow read: if isSignedIn() && resource.data.userId == request.auth.uid;
      allow create: if isSignedIn() && request.resource.data.userId == request.auth.uid;
      allow update: if isSignedIn() && 
                     resource.data.userId == request.auth.uid && 
                     request.resource.data.userId == request.auth.uid;
      allow delete: if isSignedIn() && resource.data.userId == request.auth.uid;
    }
    
    // Cars collection rules
    match /cars/{carId} {
      // Allow read if user owns the car
      allow read: if isSignedIn() && (
        !exists(/databases/$(database)/documents/cars/$(carId)) || 
        resource.data.userId == request.auth.uid
      );
      
      // Simplified create rule - just check user ID
      allow create: if isSignedIn() && request.resource.data.userId == request.auth.uid;
      
      // Simplified update rule - just check user ID
      allow update: if isSignedIn() && 
                      resource.data.userId == request.auth.uid &&
                      request.resource.data.userId == request.auth.uid;
      
      // Allow delete if user owns the car
      allow delete: if isSignedIn() && resource.data.userId == request.auth.uid;
      
      // Oil changes subcollection
      match /oilChanges/{oilChangeId} {
        allow read: if isSignedIn() && resource.data.userId == request.auth.uid;
        allow create: if isSignedIn() && request.resource.data.userId == request.auth.uid;
        allow update: if isSignedIn() && 
                        resource.data.userId == request.auth.uid &&
                        request.resource.data.userId == request.auth.uid;
        allow delete: if isSignedIn() && resource.data.userId == request.auth.uid;
      }
      
      // Maintenance subcollection
      match /maintenance/{maintenanceId} {
        allow read: if isSignedIn() && resource.data.userId == request.auth.uid;
        allow create: if isSignedIn() && request.resource.data.userId == request.auth.uid;
        allow update: if isSignedIn() && 
                        resource.data.userId == request.auth.uid &&
                        request.resource.data.userId == request.auth.uid;
        allow delete: if isSignedIn() && resource.data.userId == request.auth.uid;
      }
    }
    
    // Oil changes collection rules (root level)
    match /oil_changes/{oilChangeId} {
      allow read: if isSignedIn() && resource.data.userId == request.auth.uid;
      allow create: if isSignedIn() && request.resource.data.userId == request.auth.uid;
      allow update: if isSignedIn() && 
                      resource.data.userId == request.auth.uid &&
                      request.resource.data.userId == request.auth.uid;
      allow delete: if isSignedIn() && resource.data.userId == request.auth.uid;
    }
    
    // Maintenance collection rules (root level)
    match /maintenance/{maintenanceId} {
      // Debug logging for delete operations
      allow read: if isSignedIn() && resource.data.userId == request.auth.uid;
      allow create: if isSignedIn() && request.resource.data.userId == request.auth.uid;
      allow update: if isSignedIn() && 
                      resource.data.userId == request.auth.uid &&
                      request.resource.data.userId == request.auth.uid;
                      
      // Enhanced delete rule with clear ownership check
      allow delete: if isSignedIn() && 
                     resource != null && 
                     resource.data != null && 
                     resource.data.userId == request.auth.uid;
    }
    
    // User settings collection (root level)
    match /user_settings/{settingId} {
      allow read: if isSignedIn() && resource.data.userId == request.auth.uid;
      allow create: if isSignedIn() && request.resource.data.userId == request.auth.uid;
      allow update: if isSignedIn() && 
                      resource.data.userId == request.auth.uid &&
                      request.resource.data.userId == request.auth.uid;
      allow delete: if isSignedIn() && resource.data.userId == request.auth.uid;
    }
    
    // Subscription collection rules
    match /subscriptions/{userId} {
      allow read: if isOwner(userId);
      allow create: if isSignedIn() && request.auth.uid == userId;
      allow update: if isOwner(userId);
      allow delete: if isOwner(userId);
    }
    
    // Notification logs collection (used by Firebase Functions)
    match /notification_logs/{logId} {
      allow read: if isSignedIn() && resource.data.userId == request.auth.uid;
      allow create: if isSignedIn();
      // Only functions and owners can write
      allow update, delete: if false;
    }
    
    // App Check test collection - required for App Check functionality
    match /_app_check_tests/{docId} {
      allow read: if true;
    }
    
    // Disallow all other access by default
    match /{document=**} {
      allow read, write: if false;
    }
  }
}