// First instance - line 753
final formattedTotalCost = '${l10n.currencySymbol}${NumberFormat("#,##0.00").format(totalCost)}';

// Second instance - line 786
final formattedTotalCost = '${l10n.currencySymbol}${NumberFormat("#,##0.00").format(totalCost)}';

// Display text - line 855
Text(
  '${l10n.currencySymbol}$formattedTotalCost',
  style: TextStyle(
    fontSize: 18,
    fontWeight: FontWeight.bold,
    color: context.primaryTextColor,
  ),
),
