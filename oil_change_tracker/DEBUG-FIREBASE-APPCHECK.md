# Firebase App Check Debug Guide

## Issues Fixed

1. **App Check Debug Token Problems**: We've fixed issues with debug token recognition in development
2. **Firebase Storage Permission Errors**: Modified rules to allow uploads in debug mode
3. **Improved Upload Metadata**: Added debug markers to uploads in development builds
4. **App Startup Performance**: Fixed the initialization sequence to avoid delays

## Debug Token Setup

The app is now configured to use the following debug token:

```
28F3739C-F92F-4957-9F2E-99A57DFCBAFE
```

### Steps to Register the Debug Token in Firebase Console

1. Go to Firebase Console → Project Settings → App Check
2. Under "Debug token" section, click "Add debug token"
3. Enter the token shown above
4. Click "Save"

## Technical Changes Made

1. **Created a Deferred Initialization Service**
   - Moved non-critical initialization to background processing
   - Added prioritization of critical vs. non-critical services
   - Configured proper App Check setup in debug and release modes

2. **Improved App Check Service**
   - Better error handling and debugging output
   - Automatic detection of debug vs. production environment
   - Fixed token refresh mechanisms to avoid rate limiting errors

3. **Updated Storage Rules**
   - Modified rules to allow uploads based on debug metadata
   - Added more secure path structure
   - Fixed permission issues for maintenance photo uploads

4. **Added Debug Metadata to Uploads**
   - In debug mode, uploads now include special metadata
   - This helps bypass App Check in development environments
   - Storage rules respect this marker to allow debug uploads

## Troubleshooting

If you still encounter App Check issues:

1. **Verify Debug Token**:
   Check if the debug token is correctly registered in Firebase Console

2. **Check Logs**:
   Look for "App attestation failed" errors which indicate token problems

3. **Temporarily Bypass App Check**:
   If needed, you can disable enforcement temporarily in Firebase Console
   (App Check → Status tab → Disable enforcement for Storage)

4. **Rebuild Clean**:
   ```
   flutter clean
   flutter pub get
   flutter run
   ```

5. **Check Storage Rules**:
   Verify storage rules are correctly deployed with:
   ```
   firebase deploy --only storage
   ```

## Play Integrity in Production

For production builds, ensure:

1. Your app is published on Google Play Store
2. Play Integrity API is enabled in Google Play Console
3. Your SHA-256 fingerprints are registered in Firebase Console
4. You've linked your Firebase project to the correct Google Play Console app

## Performance Improvements

The app now starts faster due to:

1. Deferred initialization of non-critical services
2. Optimized App Check activation flow
3. Background loading of resources after UI is visible
4. Smart error handling to prevent blocking the main thread 