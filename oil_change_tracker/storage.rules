rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    // Helper function to check user ownership
    function isOwner(userId) {
      return request.auth != null && request.auth.uid == userId;
    }
    
    // Default rule - deny all access
    match /{allPaths=**} {
      allow read, write: if false;
    }
    
    // User-specific data
    match /users/{userId}/{allPaths=**} {
      allow read, write: if isOwner(userId);
    }
    
    // Car images - matches folder name used in ImageService._createStorageReference
    match /carImages/{fileName} {
      allow read: if request.auth != null;
      allow write: if request.auth != null
                   && request.resource.size < 10 * 1024 * 1024  // 10MB max
                   && request.resource.contentType.matches('image/.*')
                   // Check if filename starts with user ID
                   && fileName.matches('^' + request.auth.uid + '-.*');
    }
    
    // Alternative car image paths (for backward compatibility)
    match /car_images/{userId}/{carId}/{fileName} {
      allow read: if isOwner(userId);
      allow write: if isOwner(userId)
                  && request.resource.size < 10 * 1024 * 1024  // 10MB max
                  && request.resource.contentType.matches('image/.*');
    }
    
    // Fall back paths for debug builds
    match /public_uploads/{fileName} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && request.resource.size < 15 * 1024 * 1024;
    }
    
    // Profile images path used in ImageService._createStorageReference
    match /profileImages/{fileName} {
      allow read: if request.auth != null;
      allow write: if request.auth != null
                   && request.resource.size < 5 * 1024 * 1024  // 5MB max
                   && request.resource.contentType.matches('image/.*')
                   // Check if filename starts with user ID
                   && fileName.matches('^' + request.auth.uid + '-.*');
    }
    
    // Legacy profile path
    match /profile_images/{userId}/{fileName} {
      allow read, write: if isOwner(userId);
    }
    
    // Location data for debugging
    match /user_location/{userId}/{allPaths=**} {
      allow read, write: if isOwner(userId);
    }
    
    // Weather cache data
    match /weather_cache/{allPaths=**} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && request.resource.size < 5 * 1024 * 1024;
    }
    
    // Documents (maintenance records, receipts) used in ImageService._createStorageReference
    match /documents/{allPaths=**} {
      allow read: if request.auth != null;
      allow write: if request.auth != null
                  && request.resource.size < 15 * 1024 * 1024  // 15MB max
                  && (request.resource.contentType.matches('image/.*') ||
                      request.resource.contentType.matches('application/pdf'));
    }

    // Legacy document paths
    match /documents/{userId}/{fileName} {
      allow read: if isOwner(userId);
      allow write: if isOwner(userId)
                  && request.resource.size < 15 * 1024 * 1024  // 15MB max
                  && (request.resource.contentType.matches('image/.*') ||
                      request.resource.contentType.matches('application/pdf'))
                  && fileName.matches('^[a-zA-Z0-9_-]+\\.(jpg|jpeg|png|pdf)$');
    }

    // Nested documents path for maintenance photos
    match /documents/{userId}/{carId}/{maintenanceId}/{fileName} {
      allow read: if isOwner(userId);
      allow write: if isOwner(userId)
                  && request.resource.size < 15 * 1024 * 1024  // 15MB max
                  && request.resource.contentType.matches('image/.*');
    }

    // Direct maintenance folder path - primary path for maintenance photos
    match /maintenance/{userId}/{carId}/{maintenanceId}/{fileName} {
      allow read: if isOwner(userId);
      allow write: if isOwner(userId)
                   && request.resource.size < 20 * 1024 * 1024  // 20MB max
                   && request.resource.contentType.matches('image/.*');
    }

    // Maintenance photos specific path with more permissive rules
    match /maintenance_photos/{userId}/{carId}/{maintenanceId}/{fileName} {
      allow read: if isOwner(userId);
      allow write: if isOwner(userId)
                  && request.resource.size < 20 * 1024 * 1024  // 20MB max for testing
                  && request.resource.contentType.matches('image/.*');
    }
    
    // Temporary uploads for fallback paths
    match /temp_uploads/{userId}/{fileName} {
      allow read: if isOwner(userId);
      allow write: if isOwner(userId)
                   && request.resource.size < 10 * 1024 * 1024
                   && request.resource.contentType.matches('image/.*');
    }
    
    // Public assets
    match /public/{allPaths=**} {
      allow read: if true;
      allow write: if false;
    }
  }
}
