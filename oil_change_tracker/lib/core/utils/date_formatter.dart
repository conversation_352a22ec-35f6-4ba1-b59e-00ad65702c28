import 'package:intl/intl.dart';
import 'package:timeago/timeago.dart' as timeago;

/// Utility class for date formatting operations
class DateFormatter {
  /// Formats a date to the locale-specific format
  /// Examples: 'May 15, 2023' (en), '١٥ مايو، ٢٠٢٣' (ar)
  static String formatDate(DateTime date, String locale) {
    final formatter = DateFormat.yMMMMd(locale);
    return formatter.format(date);
  }
  
  /// Formats a date with time to the locale-specific format
  /// Examples: 'May 15, 2023 at 2:30 PM' (en), '١٥ مايو، ٢٠٢٣ في ٢:٣٠ م' (ar)
  static String formatDateWithTime(DateTime dateTime, String locale) {
    final dateFormatter = DateFormat.yMMMMd(locale);
    final timeFormatter = DateFormat.jm(locale);
    
    if (locale == 'ar') {
      return '${dateFormatter.format(dateTime)} في ${timeFormatter.format(dateTime)}';
    } else {
      return '${dateFormatter.format(dateTime)} at ${timeFormatter.format(dateTime)}';
    }
  }
  
  /// Returns a human-readable string representing how long ago the date was
  /// Examples: '5 minutes ago', '2 days ago', etc.
  static String getTimeAgo(DateTime dateTime, String locale) {
    // Initialize time ago localizations
    if (locale == 'ar') {
      timeago.setLocaleMessages('ar', timeago.ArMessages());
    }
    
    return timeago.format(dateTime, locale: locale);
  }
  
  /// Formats a date using a custom pattern
  /// @param pattern The date format pattern to use
  static String formatWithPattern(DateTime date, String pattern, String locale) {
    final formatter = DateFormat(pattern, locale);
    return formatter.format(date);
  }
  
  /// Parse a date string using the given format
  /// Returns null if the parsing fails
  static DateTime? parse(String dateStr, String pattern, [String? locale]) {
    try {
      final formatter = DateFormat(pattern, locale);
      return formatter.parse(dateStr);
    } catch (e) {
      return null;
    }
  }
} 