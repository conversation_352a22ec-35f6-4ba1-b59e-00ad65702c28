import 'dart:developer' as dev;
import 'package:flutter/foundation.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';

/// A utility class for logging that ensures logs are only shown in debug mode.
/// This prevents sensitive information from being logged in production builds.
class AppLogger {
  /// Logs a message only in debug mode
  static void log(String message) {
    if (kDebugMode) {
      dev.log(message);
    }
  }

  /// Logs an error in debug mode, and reports it to Crashlytics in production
  static void error(String message, [Object? error, StackTrace? stackTrace]) {
    if (kDebugMode) {
      dev.log('ERROR: $message', error: error, stackTrace: stackTrace);
    } else {
      // In production, report to Crashlytics but don't show the complete error details
      FirebaseCrashlytics.instance.log(message);
      if (error != null) {
        FirebaseCrashlytics.instance.recordError(
          error,
          stackTrace,
          reason: message,
          fatal: false,
        );
      }
    }
  }

  /// Records a warning that's visible in debug mode and sent to Crashlytics (non-fatal) in production
  static void warning(String message) {
    if (kDebugMode) {
      dev.log('WARNING: $message');
    } else {
      FirebaseCrashlytics.instance.log('WARNING: $message');
    }
  }

  /// Log info only in debug mode
  static void info(String message) {
    dev.log(message, name: 'INFO');
  }
}
