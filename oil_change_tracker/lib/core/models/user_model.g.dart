// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$UserModelImpl _$$UserModelImplFromJson(Map<String, dynamic> json) =>
    _$UserModelImpl(
      id: json['id'] as String?,
      email: json['email'] as String,
      displayName: json['displayName'] as String,
      photoUrl: json['photoUrl'] as String?,
      localPhotoPath: json['localPhotoPath'] as String?,
      photoUpdatedAt: (json['photoUpdatedAt'] as num?)?.toInt(),
      phoneNumber: json['phoneNumber'] as String?,
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      licenseExpiryDate: json['licenseExpiryDate'] == null
          ? null
          : DateTime.parse(json['licenseExpiryDate'] as String),
      licenseExpiryNotificationEnabled:
          json['licenseExpiryNotificationEnabled'] as bool?,
      carIds: (json['carIds'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      preferences: json['preferences'] as Map<String, dynamic>? ?? const {},
      providers: json['providers'] as Map<String, dynamic>? ?? const {},
      isEmailVerified: json['isEmailVerified'] as bool? ?? false,
      fcmToken: json['fcmToken'] as String?,
      providerId: json['providerId'] as String?,
      providerData: (json['providerData'] as List<dynamic>?)
          ?.map((e) => e as Map<String, dynamic>)
          .toList(),
      subscriptionTier: $enumDecodeNullable(
              _$SubscriptionTierEnumMap, json['subscriptionTier']) ??
          SubscriptionTier.free,
      subscriptionExpiryDate: json['subscriptionExpiryDate'] == null
          ? null
          : DateTime.parse(json['subscriptionExpiryDate'] as String),
      hasActiveSubscription: json['hasActiveSubscription'] as bool? ?? false,
      isInTrialPeriod: json['isInTrialPeriod'] as bool? ?? false,
    );

Map<String, dynamic> _$$UserModelImplToJson(_$UserModelImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'email': instance.email,
      'displayName': instance.displayName,
      'photoUrl': instance.photoUrl,
      'localPhotoPath': instance.localPhotoPath,
      'photoUpdatedAt': instance.photoUpdatedAt,
      'phoneNumber': instance.phoneNumber,
      'createdAt': instance.createdAt?.toIso8601String(),
      'licenseExpiryDate': instance.licenseExpiryDate?.toIso8601String(),
      'licenseExpiryNotificationEnabled':
          instance.licenseExpiryNotificationEnabled,
      'carIds': instance.carIds,
      'preferences': instance.preferences,
      'providers': instance.providers,
      'isEmailVerified': instance.isEmailVerified,
      'fcmToken': instance.fcmToken,
      'providerId': instance.providerId,
      'providerData': instance.providerData,
      'subscriptionTier': _$SubscriptionTierEnumMap[instance.subscriptionTier]!,
      'subscriptionExpiryDate':
          instance.subscriptionExpiryDate?.toIso8601String(),
      'hasActiveSubscription': instance.hasActiveSubscription,
      'isInTrialPeriod': instance.isInTrialPeriod,
    };

const _$SubscriptionTierEnumMap = {
  SubscriptionTier.free: 'free',
  SubscriptionTier.premium: 'premium',
};
