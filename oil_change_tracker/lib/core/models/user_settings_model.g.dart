// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_settings_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$UserSettingsModelImpl _$$UserSettingsModelImplFromJson(
        Map<String, dynamic> json) =>
    _$UserSettingsModelImpl(
      id: json['id'] as String?,
      userId: json['userId'] as String,
      oilChangeReminders: json['oilChangeReminders'] as bool? ?? true,
      maintenanceReminders: json['maintenanceReminders'] as bool? ?? true,
      mileageReminders: json['mileageReminders'] as bool? ?? true,
      promotionalNotifications:
          json['promotionalNotifications'] as bool? ?? true,
      preferredLanguage: json['preferredLanguage'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$$UserSettingsModelImplToJson(
        _$UserSettingsModelImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'oilChangeReminders': instance.oilChangeReminders,
      'maintenanceReminders': instance.maintenanceReminders,
      'mileageReminders': instance.mileageReminders,
      'promotionalNotifications': instance.promotionalNotifications,
      'preferredLanguage': instance.preferredLanguage,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
    };
