// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'car_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$CarModelImpl _$$CarModelImplFromJson(Map<String, dynamic> json) =>
    _$CarModelImpl(
      id: json['id'] as String?,
      make: json['make'] as String,
      model: json['model'] as String,
      year: (json['year'] as num).toInt(),
      currentMileage: (json['currentMileage'] as num).toInt(),
      lastOilChangeDate: DateTime.parse(json['lastOilChangeDate'] as String),
      lastOilChangeMileage: (json['lastOilChangeMileage'] as num).toInt(),
      oilChangeMileageInterval:
          (json['oilChangeMileageInterval'] as num).toInt(),
      oilChangeMonthInterval:
          (json['oilChangeMonthInterval'] as num?)?.toInt() ?? 6,
      filterChanged: json['filterChanged'] as bool? ?? false,
      oilCost: (json['oilCost'] as num?)?.toDouble() ?? 0.0,
      filterCost: (json['filterCost'] as num?)?.toDouble() ?? 0.0,
      notes: json['notes'] as String?,
      imageUrl: json['imageUrl'] as String?,
      imageUrls: (json['imageUrls'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      userId: json['userId'] as String,
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
      licenseExpiryDate: json['licenseExpiryDate'] == null
          ? null
          : DateTime.parse(json['licenseExpiryDate'] as String),
    );

Map<String, dynamic> _$$CarModelImplToJson(_$CarModelImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'make': instance.make,
      'model': instance.model,
      'year': instance.year,
      'currentMileage': instance.currentMileage,
      'lastOilChangeDate': instance.lastOilChangeDate.toIso8601String(),
      'lastOilChangeMileage': instance.lastOilChangeMileage,
      'oilChangeMileageInterval': instance.oilChangeMileageInterval,
      'oilChangeMonthInterval': instance.oilChangeMonthInterval,
      'filterChanged': instance.filterChanged,
      'oilCost': instance.oilCost,
      'filterCost': instance.filterCost,
      'notes': instance.notes,
      'imageUrl': instance.imageUrl,
      'imageUrls': instance.imageUrls,
      'userId': instance.userId,
      'createdAt': instance.createdAt?.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
      'licenseExpiryDate': instance.licenseExpiryDate?.toIso8601String(),
    };
