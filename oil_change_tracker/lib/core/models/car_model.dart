import 'package:flutter/foundation.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../utils/logger.dart';

part 'car_model.freezed.dart';
part 'car_model.g.dart';

@freezed
class CarModel with _$CarModel {
  const CarModel._();

  const factory CarModel({
    String? id,
    required String make,
    required String model,
    required int year,
    required int currentMileage,
    required DateTime lastOilChangeDate,
    required int lastOilChangeMileage,
    required int oilChangeMileageInterval,
    @Default(6) int oilChangeMonthInterval,
    @Default(false) bool filterChanged,
    @Default(0.0) double oilCost,
    @Default(0.0) double filterCost,
    String? notes,
    String? imageUrl,
    List<String>? imageUrls,
    @JsonKey(name: 'userId') required String userId,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? licenseExpiryDate, // Added license expiry date
  }) = _CarModel;

  factory CarModel.fromJson(Map<String, dynamic> json) => _$CarModelFromJson(json);

  factory CarModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    
    // Convert imageUrls if present
    List<String>? imageUrls;
    if (data['imageUrls'] != null) {
      imageUrls = List<String>.from(data['imageUrls']);
    }
    // For backward compatibility - if single imageUrl exists but not imageUrls array
    else if (data['imageUrl'] != null && data['imageUrl'].toString().isNotEmpty) {
      imageUrls = [data['imageUrl'].toString()];
    }
    
    return CarModel.fromJson({
      ...data,
      'id': doc.id,
      'lastOilChangeDate': (data['lastOilChangeDate'] as Timestamp).toDate().toIso8601String(),
      'createdAt': data['createdAt'] != null ? (data['createdAt'] as Timestamp).toDate().toIso8601String() : null,
      'updatedAt': data['updatedAt'] != null ? (data['updatedAt'] as Timestamp).toDate().toIso8601String() : null,
      'licenseExpiryDate': data['licenseExpiryDate'] != null ? (data['licenseExpiryDate'] as Timestamp).toDate().toIso8601String() : null, // Added license expiry date
      'imageUrls': imageUrls,
    });
  }

  Map<String, dynamic> toFirestore() {
    // Convert the model to a Firestore-compatible map
    AppLogger.info('[CarModel] Converting to Firestore - Original model data: ${toJson()}');
    
    // Start with the basic JSON representation
    var jsonData = toJson();
    
    // Remove any null fields to save storage space
    Map<String, dynamic> data = {};
    jsonData.forEach((key, value) {
      if (value != null) {
        data[key] = value;
      }
    });
    
    // Convert DateTime fields to Firestore Timestamp objects
    if (data.containsKey('lastOilChangeDate')) {
      data['lastOilChangeDate'] = Timestamp.fromDate(lastOilChangeDate);
    }
    
    if (data.containsKey('createdAt') && data['createdAt'] != null) {
      data['createdAt'] = createdAt != null ? Timestamp.fromDate(createdAt!) : FieldValue.serverTimestamp();
    } else {
      data['createdAt'] = FieldValue.serverTimestamp();
    }
    
    if (data.containsKey('updatedAt')) {
      data['updatedAt'] = FieldValue.serverTimestamp();
    }

    if (this.licenseExpiryDate != null) {
      data['licenseExpiryDate'] = Timestamp.fromDate(this.licenseExpiryDate!);
    } else {
      // Explicitly set to null if licenseExpiryDate is null to ensure field is removed or set to null in Firestore
      data['licenseExpiryDate'] = null;
    }
    
    // Estimate document size (rough calculation for monitoring purposes)
    AppLogger.info('[CarModel] Estimated document size: ${jsonData.length} bytes');
    
    // Return the cleaned data
    AppLogger.info('[CarModel] Final Firestore data: $data');
    return data;
  }

  // Helper method to get license expiry status
  bool get isLicenseNearingExpiry {
    if (licenseExpiryDate == null) return false;
    final now = DateTime.now();
    final difference = licenseExpiryDate!.difference(now).inDays;
    return difference <= 30 && difference >= 0; // Notify if 30 days or less
  }

  int get daysUntilLicenseExpiry {
    if (licenseExpiryDate == null) return -1; // Or some other indicator for not set
    final now = DateTime.now();
    return licenseExpiryDate!.difference(now).inDays;
  }

  bool get isOilChangeDue {
    final mileageDue = lastOilChangeMileage + oilChangeMileageInterval;
    return currentMileage >= mileageDue;
  }

  int get mileageUntilDue {
    final mileageDue = lastOilChangeMileage + oilChangeMileageInterval;
    return mileageDue - currentMileage;
  }

  bool get isDueForOilChange {
    final kmSinceLastChange = currentMileage - lastOilChangeMileage;
    return kmSinceLastChange >= oilChangeMileageInterval;
  }

  double get oilChangeProgress {
    final kmSinceLastChange = currentMileage - lastOilChangeMileage;
    return kmSinceLastChange / oilChangeMileageInterval;
  }

  int get kilometersUntilNextChange {
    final kmSinceLastChange = currentMileage - lastOilChangeMileage;
    return oilChangeMileageInterval - kmSinceLastChange > 0 
        ? oilChangeMileageInterval - kmSinceLastChange 
        : 0;
  }

  /// Returns kilometers driven since last oil change
  int get kilometersSinceLastChange {
    return currentMileage - lastOilChangeMileage;
  }

  /// Creates a copy of this car with updated mileage
  CarModel updateMileage(int newMileage) {
    return copyWith(
      currentMileage: newMileage,
    );
  }

  /// Returns days until next oil change based on the last oil change date
  int get daysUntilNextChange {
    final now = DateTime.now();
    final monthsSinceLastChange = (now.year * 12 + now.month) - 
        (lastOilChangeDate.year * 12 + lastOilChangeDate.month);
    
    // Use the configurable month interval instead of hardcoded value
    final monthsInterval = oilChangeMonthInterval;
    
    // Calculate days remaining
    final daysRemaining = (monthsInterval - monthsSinceLastChange) * 30;
    
    // If mileage-based change is due sooner, return negative days
    if (isDueForOilChange) {
      return -((now.difference(lastOilChangeDate).inDays));
    }
    
    return daysRemaining;
  }
} 