// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'maintenance_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

MaintenanceModel _$MaintenanceModelFromJson(Map<String, dynamic> json) {
  return _MaintenanceModel.fromJson(json);
}

/// @nodoc
mixin _$MaintenanceModel {
  String? get id => throw _privateConstructorUsedError;
  String get carId => throw _privateConstructorUsedError;
  String get userId => throw _privateConstructorUsedError;
  String get description => throw _privateConstructorUsedError;
  String get maintenanceType => throw _privateConstructorUsedError;
  int get mileage => throw _privateConstructorUsedError;
  double get cost => throw _privateConstructorUsedError;
  String get serviceProvider => throw _privateConstructorUsedError;
  String get notes => throw _privateConstructorUsedError;
  DateTime get date => throw _privateConstructorUsedError;
  DateTime get createdAt => throw _privateConstructorUsedError;
  DateTime get updatedAt => throw _privateConstructorUsedError;
  List<String> get photoUrls => throw _privateConstructorUsedError;

  /// Serializes this MaintenanceModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of MaintenanceModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $MaintenanceModelCopyWith<MaintenanceModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MaintenanceModelCopyWith<$Res> {
  factory $MaintenanceModelCopyWith(
          MaintenanceModel value, $Res Function(MaintenanceModel) then) =
      _$MaintenanceModelCopyWithImpl<$Res, MaintenanceModel>;
  @useResult
  $Res call(
      {String? id,
      String carId,
      String userId,
      String description,
      String maintenanceType,
      int mileage,
      double cost,
      String serviceProvider,
      String notes,
      DateTime date,
      DateTime createdAt,
      DateTime updatedAt,
      List<String> photoUrls});
}

/// @nodoc
class _$MaintenanceModelCopyWithImpl<$Res, $Val extends MaintenanceModel>
    implements $MaintenanceModelCopyWith<$Res> {
  _$MaintenanceModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of MaintenanceModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? carId = null,
    Object? userId = null,
    Object? description = null,
    Object? maintenanceType = null,
    Object? mileage = null,
    Object? cost = null,
    Object? serviceProvider = null,
    Object? notes = null,
    Object? date = null,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? photoUrls = null,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      carId: null == carId
          ? _value.carId
          : carId // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      maintenanceType: null == maintenanceType
          ? _value.maintenanceType
          : maintenanceType // ignore: cast_nullable_to_non_nullable
              as String,
      mileage: null == mileage
          ? _value.mileage
          : mileage // ignore: cast_nullable_to_non_nullable
              as int,
      cost: null == cost
          ? _value.cost
          : cost // ignore: cast_nullable_to_non_nullable
              as double,
      serviceProvider: null == serviceProvider
          ? _value.serviceProvider
          : serviceProvider // ignore: cast_nullable_to_non_nullable
              as String,
      notes: null == notes
          ? _value.notes
          : notes // ignore: cast_nullable_to_non_nullable
              as String,
      date: null == date
          ? _value.date
          : date // ignore: cast_nullable_to_non_nullable
              as DateTime,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      updatedAt: null == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      photoUrls: null == photoUrls
          ? _value.photoUrls
          : photoUrls // ignore: cast_nullable_to_non_nullable
              as List<String>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$MaintenanceModelImplCopyWith<$Res>
    implements $MaintenanceModelCopyWith<$Res> {
  factory _$$MaintenanceModelImplCopyWith(_$MaintenanceModelImpl value,
          $Res Function(_$MaintenanceModelImpl) then) =
      __$$MaintenanceModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? id,
      String carId,
      String userId,
      String description,
      String maintenanceType,
      int mileage,
      double cost,
      String serviceProvider,
      String notes,
      DateTime date,
      DateTime createdAt,
      DateTime updatedAt,
      List<String> photoUrls});
}

/// @nodoc
class __$$MaintenanceModelImplCopyWithImpl<$Res>
    extends _$MaintenanceModelCopyWithImpl<$Res, _$MaintenanceModelImpl>
    implements _$$MaintenanceModelImplCopyWith<$Res> {
  __$$MaintenanceModelImplCopyWithImpl(_$MaintenanceModelImpl _value,
      $Res Function(_$MaintenanceModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of MaintenanceModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? carId = null,
    Object? userId = null,
    Object? description = null,
    Object? maintenanceType = null,
    Object? mileage = null,
    Object? cost = null,
    Object? serviceProvider = null,
    Object? notes = null,
    Object? date = null,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? photoUrls = null,
  }) {
    return _then(_$MaintenanceModelImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      carId: null == carId
          ? _value.carId
          : carId // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      maintenanceType: null == maintenanceType
          ? _value.maintenanceType
          : maintenanceType // ignore: cast_nullable_to_non_nullable
              as String,
      mileage: null == mileage
          ? _value.mileage
          : mileage // ignore: cast_nullable_to_non_nullable
              as int,
      cost: null == cost
          ? _value.cost
          : cost // ignore: cast_nullable_to_non_nullable
              as double,
      serviceProvider: null == serviceProvider
          ? _value.serviceProvider
          : serviceProvider // ignore: cast_nullable_to_non_nullable
              as String,
      notes: null == notes
          ? _value.notes
          : notes // ignore: cast_nullable_to_non_nullable
              as String,
      date: null == date
          ? _value.date
          : date // ignore: cast_nullable_to_non_nullable
              as DateTime,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      updatedAt: null == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      photoUrls: null == photoUrls
          ? _value._photoUrls
          : photoUrls // ignore: cast_nullable_to_non_nullable
              as List<String>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$MaintenanceModelImpl extends _MaintenanceModel {
  const _$MaintenanceModelImpl(
      {this.id,
      required this.carId,
      required this.userId,
      required this.description,
      required this.maintenanceType,
      required this.mileage,
      required this.cost,
      required this.serviceProvider,
      required this.notes,
      required this.date,
      required this.createdAt,
      required this.updatedAt,
      final List<String> photoUrls = const []})
      : _photoUrls = photoUrls,
        super._();

  factory _$MaintenanceModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$MaintenanceModelImplFromJson(json);

  @override
  final String? id;
  @override
  final String carId;
  @override
  final String userId;
  @override
  final String description;
  @override
  final String maintenanceType;
  @override
  final int mileage;
  @override
  final double cost;
  @override
  final String serviceProvider;
  @override
  final String notes;
  @override
  final DateTime date;
  @override
  final DateTime createdAt;
  @override
  final DateTime updatedAt;
  final List<String> _photoUrls;
  @override
  @JsonKey()
  List<String> get photoUrls {
    if (_photoUrls is EqualUnmodifiableListView) return _photoUrls;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_photoUrls);
  }

  @override
  String toString() {
    return 'MaintenanceModel(id: $id, carId: $carId, userId: $userId, description: $description, maintenanceType: $maintenanceType, mileage: $mileage, cost: $cost, serviceProvider: $serviceProvider, notes: $notes, date: $date, createdAt: $createdAt, updatedAt: $updatedAt, photoUrls: $photoUrls)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MaintenanceModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.carId, carId) || other.carId == carId) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.maintenanceType, maintenanceType) ||
                other.maintenanceType == maintenanceType) &&
            (identical(other.mileage, mileage) || other.mileage == mileage) &&
            (identical(other.cost, cost) || other.cost == cost) &&
            (identical(other.serviceProvider, serviceProvider) ||
                other.serviceProvider == serviceProvider) &&
            (identical(other.notes, notes) || other.notes == notes) &&
            (identical(other.date, date) || other.date == date) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            const DeepCollectionEquality()
                .equals(other._photoUrls, _photoUrls));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      carId,
      userId,
      description,
      maintenanceType,
      mileage,
      cost,
      serviceProvider,
      notes,
      date,
      createdAt,
      updatedAt,
      const DeepCollectionEquality().hash(_photoUrls));

  /// Create a copy of MaintenanceModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$MaintenanceModelImplCopyWith<_$MaintenanceModelImpl> get copyWith =>
      __$$MaintenanceModelImplCopyWithImpl<_$MaintenanceModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$MaintenanceModelImplToJson(
      this,
    );
  }
}

abstract class _MaintenanceModel extends MaintenanceModel {
  const factory _MaintenanceModel(
      {final String? id,
      required final String carId,
      required final String userId,
      required final String description,
      required final String maintenanceType,
      required final int mileage,
      required final double cost,
      required final String serviceProvider,
      required final String notes,
      required final DateTime date,
      required final DateTime createdAt,
      required final DateTime updatedAt,
      final List<String> photoUrls}) = _$MaintenanceModelImpl;
  const _MaintenanceModel._() : super._();

  factory _MaintenanceModel.fromJson(Map<String, dynamic> json) =
      _$MaintenanceModelImpl.fromJson;

  @override
  String? get id;
  @override
  String get carId;
  @override
  String get userId;
  @override
  String get description;
  @override
  String get maintenanceType;
  @override
  int get mileage;
  @override
  double get cost;
  @override
  String get serviceProvider;
  @override
  String get notes;
  @override
  DateTime get date;
  @override
  DateTime get createdAt;
  @override
  DateTime get updatedAt;
  @override
  List<String> get photoUrls;

  /// Create a copy of MaintenanceModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$MaintenanceModelImplCopyWith<_$MaintenanceModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
