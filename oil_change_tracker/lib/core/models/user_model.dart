import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../../features/subscription/models/subscription_tier.dart';

part 'user_model.freezed.dart';
part 'user_model.g.dart';

@freezed
class UserModel with _$UserModel {
  const factory UserModel({
    String? id,
    required String email,
    required String displayName,
    String? photoUrl,
    String? localPhotoPath,
    int? photoUpdatedAt,
    String? phoneNumber,
    DateTime? createdAt,
    DateTime? licenseExpiryDate,
    bool? licenseExpiryNotificationEnabled,
    @Default([]) List<String> carIds,
    @Default({}) Map<String, dynamic> preferences,
    @Default({}) Map<String, dynamic> providers,
    @Default(false) bool isEmailVerified,
    String? fcmToken,
    String? providerId,
    List<Map<String, dynamic>>? providerData,

    // Subscription-related fields
    @Default(SubscriptionTier.free) SubscriptionTier subscriptionTier,
    DateTime? subscriptionExpiryDate,
    @Default(false) bool hasActiveSubscription,
    @Default(false) bool isInTrialPeriod,
  }) = _UserModel;

  factory UserModel.fromJson(Map<String, dynamic> json) =>
      _$UserModelFromJson(json);

  factory UserModel.fromFirebaseUser(User user) {
    final emailPrefix = user.email?.split('@').first ?? 'User';

    // Extract provider data
    final providersData = user.providerData
        .map((provider) => {
              'providerId': provider.providerId,
              'uid': provider.uid,
              'displayName': provider.displayName,
              'email': provider.email,
              'phoneNumber': provider.phoneNumber,
              'photoURL': provider.photoURL,
            })
        .toList();

    return UserModel(
      id: user.uid,
      email: user.email ?? '',
      displayName: user.displayName ?? emailPrefix,
      photoUrl: user.photoURL,
      isEmailVerified: user.emailVerified,
      providerId: user.providerData.isNotEmpty
          ? user.providerData.first.providerId
          : 'firebase',
      providerData: providersData,
      preferences: {
        'authProvider': user.providerData.isNotEmpty
            ? user.providerData.first.providerId
            : 'unknown',
      },
      // Default subscription values
      subscriptionTier: SubscriptionTier.free,
      hasActiveSubscription: false,
      isInTrialPeriod: false,
    );
  }

  factory UserModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;

    // Prepare a map for UserModel.fromJson, starting with Firestore data
    final Map<String, dynamic> json = {...data};

    // Ensure 'id' is from the document ID
    json['id'] = doc.id;

    // Convert 'createdAt' from Timestamp to ISO8601 String if necessary
    if (data['createdAt'] is Timestamp) {
      json['createdAt'] =
          (data['createdAt'] as Timestamp).toDate().toIso8601String();
    }

    // Handle license expiry date - convert from multiple possible formats
    if (data['licenseExpiryDate'] != null) {
      try {
        if (data['licenseExpiryDate'] is Timestamp) {
          // From Timestamp (standard Firestore format)
          json['licenseExpiryDate'] = (data['licenseExpiryDate'] as Timestamp)
              .toDate()
              .toIso8601String();
        } else if (data['licenseExpiryDate'] is int) {
          // From millisecondsSinceEpoch (used in our updateLicenseExpiryDate method)
          json['licenseExpiryDate'] = DateTime.fromMillisecondsSinceEpoch(
                  data['licenseExpiryDate'] as int)
              .toIso8601String();
        } else if (data['licenseExpiryDate'] is String) {
          // Already a string - validate it's a proper ISO8601 format
          try {
            // Verify it's a valid date string by parsing and reformatting
            final dateTime =
                DateTime.parse(data['licenseExpiryDate'] as String);
            json['licenseExpiryDate'] = dateTime.toIso8601String();
          } catch (e) {
            // If parsing fails, remove the invalid value
            json.remove('licenseExpiryDate');
          }
        } else {
          // Unknown format - remove the field to prevent parsing errors
          json.remove('licenseExpiryDate');
        }
      } catch (e) {
        // Log and swallow error to ensure we don't crash parsing the whole document
        print('Error parsing licenseExpiryDate: $e');
        json.remove('licenseExpiryDate');
      }
    }

    // Handle subscription expiry date
    if (data['subscriptionExpiryDate'] != null) {
      try {
        if (data['subscriptionExpiryDate'] is Timestamp) {
          json['subscriptionExpiryDate'] =
              (data['subscriptionExpiryDate'] as Timestamp)
                  .toDate()
                  .toIso8601String();
        } else if (data['subscriptionExpiryDate'] is int) {
          json['subscriptionExpiryDate'] = DateTime.fromMillisecondsSinceEpoch(
                  data['subscriptionExpiryDate'] as int)
              .toIso8601String();
        } else if (data['subscriptionExpiryDate'] is String) {
          try {
            final dateTime =
                DateTime.parse(data['subscriptionExpiryDate'] as String);
            json['subscriptionExpiryDate'] = dateTime.toIso8601String();
          } catch (e) {
            json.remove('subscriptionExpiryDate');
          }
        } else {
          json.remove('subscriptionExpiryDate');
        }
      } catch (e) {
        print('Error parsing subscriptionExpiryDate: $e');
        json.remove('subscriptionExpiryDate');
      }
    }

    // Convert 'photoUpdatedAt' from Timestamp to millisecondsSinceEpoch (int) if necessary
    if (data['photoUpdatedAt'] is Timestamp) {
      json['photoUpdatedAt'] =
          (data['photoUpdatedAt'] as Timestamp).millisecondsSinceEpoch;
    }

    // Handle subscription tier
    if (data['subscriptionTier'] is String) {
      final tierString = data['subscriptionTier'] as String;
      try {
        json['subscriptionTier'] = SubscriptionTier.values.firstWhere(
          (tier) => tier.toString() == 'SubscriptionTier.$tierString',
          orElse: () => SubscriptionTier.free,
        );
      } catch (e) {
        json['subscriptionTier'] = SubscriptionTier.free;
      }
    }

    return UserModel.fromJson(json);
  }

  const UserModel._();

  Map<String, dynamic> toFirestore() {
    final json = toJson();
    json.remove('id');

    // Convert DateTime to millisecondsSinceEpoch for Firestore
    if (json['licenseExpiryDate'] != null) {
      // Always save as millisecondsSinceEpoch for consistency
      if (licenseExpiryDate != null) {
        json['licenseExpiryDate'] = licenseExpiryDate!.millisecondsSinceEpoch;
      }
    }

    // Handle subscription expiry date
    if (json['subscriptionExpiryDate'] != null) {
      if (subscriptionExpiryDate != null) {
        json['subscriptionExpiryDate'] =
            subscriptionExpiryDate!.millisecondsSinceEpoch;
      }
    }

    // Convert subscription tier enum to string
    if (json['subscriptionTier'] != null) {
      json['subscriptionTier'] = subscriptionTier.toString().split('.').last;
    }

    return json;
  }

  factory UserModel.fromDocument(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>?;

    if (data == null) {
      return const UserModel(email: '', displayName: '');
    }

    // Handle licenseExpiryDate conversion from multiple formats
    DateTime? licenseExpiryDate;
    if (data['licenseExpiryDate'] != null) {
      try {
        if (data['licenseExpiryDate'] is Timestamp) {
          licenseExpiryDate = (data['licenseExpiryDate'] as Timestamp).toDate();
        } else if (data['licenseExpiryDate'] is int) {
          licenseExpiryDate = DateTime.fromMillisecondsSinceEpoch(
              data['licenseExpiryDate'] as int);
        } else if (data['licenseExpiryDate'] is String) {
          try {
            licenseExpiryDate =
                DateTime.parse(data['licenseExpiryDate'] as String);
          } catch (e) {
            // Invalid date string
            licenseExpiryDate = null;
            print('Error parsing licenseExpiryDate string: $e');
          }
        }
      } catch (e) {
        // Log and handle any errors during conversion
        print('Error converting licenseExpiryDate: $e');
        licenseExpiryDate = null;
      }
    }

    // Handle subscription expiry date
    DateTime? subscriptionExpiryDate;
    if (data['subscriptionExpiryDate'] != null) {
      try {
        if (data['subscriptionExpiryDate'] is Timestamp) {
          subscriptionExpiryDate =
              (data['subscriptionExpiryDate'] as Timestamp).toDate();
        } else if (data['subscriptionExpiryDate'] is int) {
          subscriptionExpiryDate = DateTime.fromMillisecondsSinceEpoch(
              data['subscriptionExpiryDate'] as int);
        } else if (data['subscriptionExpiryDate'] is String) {
          try {
            subscriptionExpiryDate =
                DateTime.parse(data['subscriptionExpiryDate'] as String);
          } catch (e) {
            subscriptionExpiryDate = null;
          }
        }
      } catch (e) {
        print('Error converting subscriptionExpiryDate: $e');
        subscriptionExpiryDate = null;
      }
    }

    // Handle subscription tier
    SubscriptionTier subscriptionTier = SubscriptionTier.free;
    if (data['subscriptionTier'] is String) {
      final tierString = data['subscriptionTier'] as String;
      try {
        subscriptionTier = SubscriptionTier.values.firstWhere(
          (tier) =>
              tier.toString() == 'SubscriptionTier.$tierString' ||
              tier.toString().split('.').last == tierString,
          orElse: () => SubscriptionTier.free,
        );
      } catch (e) {
        subscriptionTier = SubscriptionTier.free;
      }
    }

    return UserModel(
      id: doc.id,
      displayName: (data['displayName'] as String?) ?? '',
      email: (data['email'] as String?) ?? '',
      phoneNumber: data['phoneNumber'] as String?,
      photoUrl: data['photoUrl'] as String?,
      localPhotoPath: data['localPhotoPath'] as String?,
      photoUpdatedAt: data['photoUpdatedAt'] as int?,
      licenseExpiryDate: licenseExpiryDate,
      licenseExpiryNotificationEnabled:
          data['licenseExpiryNotificationEnabled'] as bool?,
      createdAt: data['createdAt'] != null
          ? (data['createdAt'] as Timestamp).toDate()
          : null,
      providers: data['providers'] != null
          ? Map<String, dynamic>.from(data['providers'] as Map)
          : {},
      preferences: data['preferences'] != null
          ? Map<String, dynamic>.from(data['preferences'] as Map)
          : {},
      // Subscription fields
      subscriptionTier: subscriptionTier,
      subscriptionExpiryDate: subscriptionExpiryDate,
      hasActiveSubscription: data['hasActiveSubscription'] as bool? ?? false,
      isInTrialPeriod: data['isInTrialPeriod'] as bool? ?? false,
    );
  }
}
