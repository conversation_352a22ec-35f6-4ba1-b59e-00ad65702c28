// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'user_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

UserModel _$UserModelFromJson(Map<String, dynamic> json) {
  return _UserModel.fromJson(json);
}

/// @nodoc
mixin _$UserModel {
  String? get id => throw _privateConstructorUsedError;
  String get email => throw _privateConstructorUsedError;
  String get displayName => throw _privateConstructorUsedError;
  String? get photoUrl => throw _privateConstructorUsedError;
  String? get localPhotoPath => throw _privateConstructorUsedError;
  int? get photoUpdatedAt => throw _privateConstructorUsedError;
  String? get phoneNumber => throw _privateConstructorUsedError;
  DateTime? get createdAt => throw _privateConstructorUsedError;
  DateTime? get licenseExpiryDate => throw _privateConstructorUsedError;
  bool? get licenseExpiryNotificationEnabled =>
      throw _privateConstructorUsedError;
  List<String> get carIds => throw _privateConstructorUsedError;
  Map<String, dynamic> get preferences => throw _privateConstructorUsedError;
  Map<String, dynamic> get providers => throw _privateConstructorUsedError;
  bool get isEmailVerified => throw _privateConstructorUsedError;
  String? get fcmToken => throw _privateConstructorUsedError;
  String? get providerId => throw _privateConstructorUsedError;
  List<Map<String, dynamic>>? get providerData =>
      throw _privateConstructorUsedError; // Subscription-related fields
  SubscriptionTier get subscriptionTier => throw _privateConstructorUsedError;
  DateTime? get subscriptionExpiryDate => throw _privateConstructorUsedError;
  bool get hasActiveSubscription => throw _privateConstructorUsedError;
  bool get isInTrialPeriod => throw _privateConstructorUsedError;

  /// Serializes this UserModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of UserModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $UserModelCopyWith<UserModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UserModelCopyWith<$Res> {
  factory $UserModelCopyWith(UserModel value, $Res Function(UserModel) then) =
      _$UserModelCopyWithImpl<$Res, UserModel>;
  @useResult
  $Res call(
      {String? id,
      String email,
      String displayName,
      String? photoUrl,
      String? localPhotoPath,
      int? photoUpdatedAt,
      String? phoneNumber,
      DateTime? createdAt,
      DateTime? licenseExpiryDate,
      bool? licenseExpiryNotificationEnabled,
      List<String> carIds,
      Map<String, dynamic> preferences,
      Map<String, dynamic> providers,
      bool isEmailVerified,
      String? fcmToken,
      String? providerId,
      List<Map<String, dynamic>>? providerData,
      SubscriptionTier subscriptionTier,
      DateTime? subscriptionExpiryDate,
      bool hasActiveSubscription,
      bool isInTrialPeriod});
}

/// @nodoc
class _$UserModelCopyWithImpl<$Res, $Val extends UserModel>
    implements $UserModelCopyWith<$Res> {
  _$UserModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of UserModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? email = null,
    Object? displayName = null,
    Object? photoUrl = freezed,
    Object? localPhotoPath = freezed,
    Object? photoUpdatedAt = freezed,
    Object? phoneNumber = freezed,
    Object? createdAt = freezed,
    Object? licenseExpiryDate = freezed,
    Object? licenseExpiryNotificationEnabled = freezed,
    Object? carIds = null,
    Object? preferences = null,
    Object? providers = null,
    Object? isEmailVerified = null,
    Object? fcmToken = freezed,
    Object? providerId = freezed,
    Object? providerData = freezed,
    Object? subscriptionTier = null,
    Object? subscriptionExpiryDate = freezed,
    Object? hasActiveSubscription = null,
    Object? isInTrialPeriod = null,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      email: null == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String,
      displayName: null == displayName
          ? _value.displayName
          : displayName // ignore: cast_nullable_to_non_nullable
              as String,
      photoUrl: freezed == photoUrl
          ? _value.photoUrl
          : photoUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      localPhotoPath: freezed == localPhotoPath
          ? _value.localPhotoPath
          : localPhotoPath // ignore: cast_nullable_to_non_nullable
              as String?,
      photoUpdatedAt: freezed == photoUpdatedAt
          ? _value.photoUpdatedAt
          : photoUpdatedAt // ignore: cast_nullable_to_non_nullable
              as int?,
      phoneNumber: freezed == phoneNumber
          ? _value.phoneNumber
          : phoneNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      licenseExpiryDate: freezed == licenseExpiryDate
          ? _value.licenseExpiryDate
          : licenseExpiryDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      licenseExpiryNotificationEnabled: freezed ==
              licenseExpiryNotificationEnabled
          ? _value.licenseExpiryNotificationEnabled
          : licenseExpiryNotificationEnabled // ignore: cast_nullable_to_non_nullable
              as bool?,
      carIds: null == carIds
          ? _value.carIds
          : carIds // ignore: cast_nullable_to_non_nullable
              as List<String>,
      preferences: null == preferences
          ? _value.preferences
          : preferences // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
      providers: null == providers
          ? _value.providers
          : providers // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
      isEmailVerified: null == isEmailVerified
          ? _value.isEmailVerified
          : isEmailVerified // ignore: cast_nullable_to_non_nullable
              as bool,
      fcmToken: freezed == fcmToken
          ? _value.fcmToken
          : fcmToken // ignore: cast_nullable_to_non_nullable
              as String?,
      providerId: freezed == providerId
          ? _value.providerId
          : providerId // ignore: cast_nullable_to_non_nullable
              as String?,
      providerData: freezed == providerData
          ? _value.providerData
          : providerData // ignore: cast_nullable_to_non_nullable
              as List<Map<String, dynamic>>?,
      subscriptionTier: null == subscriptionTier
          ? _value.subscriptionTier
          : subscriptionTier // ignore: cast_nullable_to_non_nullable
              as SubscriptionTier,
      subscriptionExpiryDate: freezed == subscriptionExpiryDate
          ? _value.subscriptionExpiryDate
          : subscriptionExpiryDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      hasActiveSubscription: null == hasActiveSubscription
          ? _value.hasActiveSubscription
          : hasActiveSubscription // ignore: cast_nullable_to_non_nullable
              as bool,
      isInTrialPeriod: null == isInTrialPeriod
          ? _value.isInTrialPeriod
          : isInTrialPeriod // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$UserModelImplCopyWith<$Res>
    implements $UserModelCopyWith<$Res> {
  factory _$$UserModelImplCopyWith(
          _$UserModelImpl value, $Res Function(_$UserModelImpl) then) =
      __$$UserModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? id,
      String email,
      String displayName,
      String? photoUrl,
      String? localPhotoPath,
      int? photoUpdatedAt,
      String? phoneNumber,
      DateTime? createdAt,
      DateTime? licenseExpiryDate,
      bool? licenseExpiryNotificationEnabled,
      List<String> carIds,
      Map<String, dynamic> preferences,
      Map<String, dynamic> providers,
      bool isEmailVerified,
      String? fcmToken,
      String? providerId,
      List<Map<String, dynamic>>? providerData,
      SubscriptionTier subscriptionTier,
      DateTime? subscriptionExpiryDate,
      bool hasActiveSubscription,
      bool isInTrialPeriod});
}

/// @nodoc
class __$$UserModelImplCopyWithImpl<$Res>
    extends _$UserModelCopyWithImpl<$Res, _$UserModelImpl>
    implements _$$UserModelImplCopyWith<$Res> {
  __$$UserModelImplCopyWithImpl(
      _$UserModelImpl _value, $Res Function(_$UserModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of UserModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? email = null,
    Object? displayName = null,
    Object? photoUrl = freezed,
    Object? localPhotoPath = freezed,
    Object? photoUpdatedAt = freezed,
    Object? phoneNumber = freezed,
    Object? createdAt = freezed,
    Object? licenseExpiryDate = freezed,
    Object? licenseExpiryNotificationEnabled = freezed,
    Object? carIds = null,
    Object? preferences = null,
    Object? providers = null,
    Object? isEmailVerified = null,
    Object? fcmToken = freezed,
    Object? providerId = freezed,
    Object? providerData = freezed,
    Object? subscriptionTier = null,
    Object? subscriptionExpiryDate = freezed,
    Object? hasActiveSubscription = null,
    Object? isInTrialPeriod = null,
  }) {
    return _then(_$UserModelImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      email: null == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String,
      displayName: null == displayName
          ? _value.displayName
          : displayName // ignore: cast_nullable_to_non_nullable
              as String,
      photoUrl: freezed == photoUrl
          ? _value.photoUrl
          : photoUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      localPhotoPath: freezed == localPhotoPath
          ? _value.localPhotoPath
          : localPhotoPath // ignore: cast_nullable_to_non_nullable
              as String?,
      photoUpdatedAt: freezed == photoUpdatedAt
          ? _value.photoUpdatedAt
          : photoUpdatedAt // ignore: cast_nullable_to_non_nullable
              as int?,
      phoneNumber: freezed == phoneNumber
          ? _value.phoneNumber
          : phoneNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      licenseExpiryDate: freezed == licenseExpiryDate
          ? _value.licenseExpiryDate
          : licenseExpiryDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      licenseExpiryNotificationEnabled: freezed ==
              licenseExpiryNotificationEnabled
          ? _value.licenseExpiryNotificationEnabled
          : licenseExpiryNotificationEnabled // ignore: cast_nullable_to_non_nullable
              as bool?,
      carIds: null == carIds
          ? _value._carIds
          : carIds // ignore: cast_nullable_to_non_nullable
              as List<String>,
      preferences: null == preferences
          ? _value._preferences
          : preferences // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
      providers: null == providers
          ? _value._providers
          : providers // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
      isEmailVerified: null == isEmailVerified
          ? _value.isEmailVerified
          : isEmailVerified // ignore: cast_nullable_to_non_nullable
              as bool,
      fcmToken: freezed == fcmToken
          ? _value.fcmToken
          : fcmToken // ignore: cast_nullable_to_non_nullable
              as String?,
      providerId: freezed == providerId
          ? _value.providerId
          : providerId // ignore: cast_nullable_to_non_nullable
              as String?,
      providerData: freezed == providerData
          ? _value._providerData
          : providerData // ignore: cast_nullable_to_non_nullable
              as List<Map<String, dynamic>>?,
      subscriptionTier: null == subscriptionTier
          ? _value.subscriptionTier
          : subscriptionTier // ignore: cast_nullable_to_non_nullable
              as SubscriptionTier,
      subscriptionExpiryDate: freezed == subscriptionExpiryDate
          ? _value.subscriptionExpiryDate
          : subscriptionExpiryDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      hasActiveSubscription: null == hasActiveSubscription
          ? _value.hasActiveSubscription
          : hasActiveSubscription // ignore: cast_nullable_to_non_nullable
              as bool,
      isInTrialPeriod: null == isInTrialPeriod
          ? _value.isInTrialPeriod
          : isInTrialPeriod // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$UserModelImpl extends _UserModel {
  const _$UserModelImpl(
      {this.id,
      required this.email,
      required this.displayName,
      this.photoUrl,
      this.localPhotoPath,
      this.photoUpdatedAt,
      this.phoneNumber,
      this.createdAt,
      this.licenseExpiryDate,
      this.licenseExpiryNotificationEnabled,
      final List<String> carIds = const [],
      final Map<String, dynamic> preferences = const {},
      final Map<String, dynamic> providers = const {},
      this.isEmailVerified = false,
      this.fcmToken,
      this.providerId,
      final List<Map<String, dynamic>>? providerData,
      this.subscriptionTier = SubscriptionTier.free,
      this.subscriptionExpiryDate,
      this.hasActiveSubscription = false,
      this.isInTrialPeriod = false})
      : _carIds = carIds,
        _preferences = preferences,
        _providers = providers,
        _providerData = providerData,
        super._();

  factory _$UserModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$UserModelImplFromJson(json);

  @override
  final String? id;
  @override
  final String email;
  @override
  final String displayName;
  @override
  final String? photoUrl;
  @override
  final String? localPhotoPath;
  @override
  final int? photoUpdatedAt;
  @override
  final String? phoneNumber;
  @override
  final DateTime? createdAt;
  @override
  final DateTime? licenseExpiryDate;
  @override
  final bool? licenseExpiryNotificationEnabled;
  final List<String> _carIds;
  @override
  @JsonKey()
  List<String> get carIds {
    if (_carIds is EqualUnmodifiableListView) return _carIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_carIds);
  }

  final Map<String, dynamic> _preferences;
  @override
  @JsonKey()
  Map<String, dynamic> get preferences {
    if (_preferences is EqualUnmodifiableMapView) return _preferences;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_preferences);
  }

  final Map<String, dynamic> _providers;
  @override
  @JsonKey()
  Map<String, dynamic> get providers {
    if (_providers is EqualUnmodifiableMapView) return _providers;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_providers);
  }

  @override
  @JsonKey()
  final bool isEmailVerified;
  @override
  final String? fcmToken;
  @override
  final String? providerId;
  final List<Map<String, dynamic>>? _providerData;
  @override
  List<Map<String, dynamic>>? get providerData {
    final value = _providerData;
    if (value == null) return null;
    if (_providerData is EqualUnmodifiableListView) return _providerData;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

// Subscription-related fields
  @override
  @JsonKey()
  final SubscriptionTier subscriptionTier;
  @override
  final DateTime? subscriptionExpiryDate;
  @override
  @JsonKey()
  final bool hasActiveSubscription;
  @override
  @JsonKey()
  final bool isInTrialPeriod;

  @override
  String toString() {
    return 'UserModel(id: $id, email: $email, displayName: $displayName, photoUrl: $photoUrl, localPhotoPath: $localPhotoPath, photoUpdatedAt: $photoUpdatedAt, phoneNumber: $phoneNumber, createdAt: $createdAt, licenseExpiryDate: $licenseExpiryDate, licenseExpiryNotificationEnabled: $licenseExpiryNotificationEnabled, carIds: $carIds, preferences: $preferences, providers: $providers, isEmailVerified: $isEmailVerified, fcmToken: $fcmToken, providerId: $providerId, providerData: $providerData, subscriptionTier: $subscriptionTier, subscriptionExpiryDate: $subscriptionExpiryDate, hasActiveSubscription: $hasActiveSubscription, isInTrialPeriod: $isInTrialPeriod)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UserModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.displayName, displayName) ||
                other.displayName == displayName) &&
            (identical(other.photoUrl, photoUrl) ||
                other.photoUrl == photoUrl) &&
            (identical(other.localPhotoPath, localPhotoPath) ||
                other.localPhotoPath == localPhotoPath) &&
            (identical(other.photoUpdatedAt, photoUpdatedAt) ||
                other.photoUpdatedAt == photoUpdatedAt) &&
            (identical(other.phoneNumber, phoneNumber) ||
                other.phoneNumber == phoneNumber) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.licenseExpiryDate, licenseExpiryDate) ||
                other.licenseExpiryDate == licenseExpiryDate) &&
            (identical(other.licenseExpiryNotificationEnabled,
                    licenseExpiryNotificationEnabled) ||
                other.licenseExpiryNotificationEnabled ==
                    licenseExpiryNotificationEnabled) &&
            const DeepCollectionEquality().equals(other._carIds, _carIds) &&
            const DeepCollectionEquality()
                .equals(other._preferences, _preferences) &&
            const DeepCollectionEquality()
                .equals(other._providers, _providers) &&
            (identical(other.isEmailVerified, isEmailVerified) ||
                other.isEmailVerified == isEmailVerified) &&
            (identical(other.fcmToken, fcmToken) ||
                other.fcmToken == fcmToken) &&
            (identical(other.providerId, providerId) ||
                other.providerId == providerId) &&
            const DeepCollectionEquality()
                .equals(other._providerData, _providerData) &&
            (identical(other.subscriptionTier, subscriptionTier) ||
                other.subscriptionTier == subscriptionTier) &&
            (identical(other.subscriptionExpiryDate, subscriptionExpiryDate) ||
                other.subscriptionExpiryDate == subscriptionExpiryDate) &&
            (identical(other.hasActiveSubscription, hasActiveSubscription) ||
                other.hasActiveSubscription == hasActiveSubscription) &&
            (identical(other.isInTrialPeriod, isInTrialPeriod) ||
                other.isInTrialPeriod == isInTrialPeriod));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        id,
        email,
        displayName,
        photoUrl,
        localPhotoPath,
        photoUpdatedAt,
        phoneNumber,
        createdAt,
        licenseExpiryDate,
        licenseExpiryNotificationEnabled,
        const DeepCollectionEquality().hash(_carIds),
        const DeepCollectionEquality().hash(_preferences),
        const DeepCollectionEquality().hash(_providers),
        isEmailVerified,
        fcmToken,
        providerId,
        const DeepCollectionEquality().hash(_providerData),
        subscriptionTier,
        subscriptionExpiryDate,
        hasActiveSubscription,
        isInTrialPeriod
      ]);

  /// Create a copy of UserModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UserModelImplCopyWith<_$UserModelImpl> get copyWith =>
      __$$UserModelImplCopyWithImpl<_$UserModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$UserModelImplToJson(
      this,
    );
  }
}

abstract class _UserModel extends UserModel {
  const factory _UserModel(
      {final String? id,
      required final String email,
      required final String displayName,
      final String? photoUrl,
      final String? localPhotoPath,
      final int? photoUpdatedAt,
      final String? phoneNumber,
      final DateTime? createdAt,
      final DateTime? licenseExpiryDate,
      final bool? licenseExpiryNotificationEnabled,
      final List<String> carIds,
      final Map<String, dynamic> preferences,
      final Map<String, dynamic> providers,
      final bool isEmailVerified,
      final String? fcmToken,
      final String? providerId,
      final List<Map<String, dynamic>>? providerData,
      final SubscriptionTier subscriptionTier,
      final DateTime? subscriptionExpiryDate,
      final bool hasActiveSubscription,
      final bool isInTrialPeriod}) = _$UserModelImpl;
  const _UserModel._() : super._();

  factory _UserModel.fromJson(Map<String, dynamic> json) =
      _$UserModelImpl.fromJson;

  @override
  String? get id;
  @override
  String get email;
  @override
  String get displayName;
  @override
  String? get photoUrl;
  @override
  String? get localPhotoPath;
  @override
  int? get photoUpdatedAt;
  @override
  String? get phoneNumber;
  @override
  DateTime? get createdAt;
  @override
  DateTime? get licenseExpiryDate;
  @override
  bool? get licenseExpiryNotificationEnabled;
  @override
  List<String> get carIds;
  @override
  Map<String, dynamic> get preferences;
  @override
  Map<String, dynamic> get providers;
  @override
  bool get isEmailVerified;
  @override
  String? get fcmToken;
  @override
  String? get providerId;
  @override
  List<Map<String, dynamic>>? get providerData; // Subscription-related fields
  @override
  SubscriptionTier get subscriptionTier;
  @override
  DateTime? get subscriptionExpiryDate;
  @override
  bool get hasActiveSubscription;
  @override
  bool get isInTrialPeriod;

  /// Create a copy of UserModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UserModelImplCopyWith<_$UserModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
