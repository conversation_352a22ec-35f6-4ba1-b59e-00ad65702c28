// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'weather_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$WeatherModelImpl _$$WeatherModelImplFromJson(Map<String, dynamic> json) =>
    _$WeatherModelImpl(
      location: Location.fromJson(json['location'] as Map<String, dynamic>),
      current: Current.fromJson(json['current'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$WeatherModelImplToJson(_$WeatherModelImpl instance) =>
    <String, dynamic>{
      'location': instance.location,
      'current': instance.current,
    };

_$LocationImpl _$$LocationImplFromJson(Map<String, dynamic> json) =>
    _$LocationImpl(
      name: json['name'] as String,
      region: json['region'] as String?,
      country: json['country'] as String,
      lat: const DoubleConverter().fromJson(json['lat']),
      lon: const DoubleConverter().fromJson(json['lon']),
      tzId: json['tz_id'] as String?,
      localtimeEpoch: (json['localtime_epoch'] as num?)?.toInt(),
      localtime: json['localtime'] as String?,
    );

Map<String, dynamic> _$$LocationImplToJson(_$LocationImpl instance) =>
    <String, dynamic>{
      'name': instance.name,
      'region': instance.region,
      'country': instance.country,
      'lat': const DoubleConverter().toJson(instance.lat),
      'lon': const DoubleConverter().toJson(instance.lon),
      'tz_id': instance.tzId,
      'localtime_epoch': instance.localtimeEpoch,
      'localtime': instance.localtime,
    };

_$CurrentImpl _$$CurrentImplFromJson(Map<String, dynamic> json) =>
    _$CurrentImpl(
      lastUpdatedEpoch:
          const IntConverter().fromJson(json['last_updated_epoch']),
      lastUpdated: json['last_updated'] as String,
      tempC: const DoubleConverter().fromJson(json['temp_c']),
      tempF: const DoubleConverter().fromJson(json['temp_f']),
      isDay: const IntConverter().fromJson(json['is_day']),
      condition: Condition.fromJson(json['condition'] as Map<String, dynamic>),
      windMph: const DoubleConverter().fromJson(json['wind_mph']),
      windKph: const DoubleConverter().fromJson(json['wind_kph']),
      windDegree: const IntConverter().fromJson(json['wind_degree']),
      windDir: json['wind_dir'] as String,
      pressureMb: const DoubleConverter().fromJson(json['pressure_mb']),
      pressureIn: const DoubleConverter().fromJson(json['pressure_in']),
      precipMm: const DoubleConverter().fromJson(json['precip_mm']),
      precipIn: const DoubleConverter().fromJson(json['precip_in']),
      humidity: const IntConverter().fromJson(json['humidity']),
      cloud: const IntConverter().fromJson(json['cloud']),
      feelslikeC: const DoubleConverter().fromJson(json['feelslike_c']),
      feelslikeF: const DoubleConverter().fromJson(json['feelslike_f']),
      visKm: const DoubleConverter().fromJson(json['vis_km']),
      visMiles: const DoubleConverter().fromJson(json['vis_miles']),
      uv: const NullableDoubleConverter().fromJson(json['uv']),
      gustMph: const DoubleConverter().fromJson(json['gust_mph']),
      gustKph: const DoubleConverter().fromJson(json['gust_kph']),
      windchillC: const NullableDoubleConverter().fromJson(json['windchill_c']),
      windchillF: const NullableDoubleConverter().fromJson(json['windchill_f']),
      heatindexC: const NullableDoubleConverter().fromJson(json['heatindex_c']),
      heatindexF: const NullableDoubleConverter().fromJson(json['heatindex_f']),
      dewpointC: const NullableDoubleConverter().fromJson(json['dewpoint_c']),
      dewpointF: const NullableDoubleConverter().fromJson(json['dewpoint_f']),
      willItRain: (json['will_it_rain'] as num?)?.toInt(),
      willItSnow: (json['will_it_snow'] as num?)?.toInt(),
      chanceOfRain: (json['chance_of_rain'] as num?)?.toInt(),
      chanceOfSnow: (json['chance_of_snow'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$CurrentImplToJson(_$CurrentImpl instance) =>
    <String, dynamic>{
      'last_updated_epoch':
          const IntConverter().toJson(instance.lastUpdatedEpoch),
      'last_updated': instance.lastUpdated,
      'temp_c': const DoubleConverter().toJson(instance.tempC),
      'temp_f': const DoubleConverter().toJson(instance.tempF),
      'is_day': const IntConverter().toJson(instance.isDay),
      'condition': instance.condition,
      'wind_mph': const DoubleConverter().toJson(instance.windMph),
      'wind_kph': const DoubleConverter().toJson(instance.windKph),
      'wind_degree': const IntConverter().toJson(instance.windDegree),
      'wind_dir': instance.windDir,
      'pressure_mb': const DoubleConverter().toJson(instance.pressureMb),
      'pressure_in': const DoubleConverter().toJson(instance.pressureIn),
      'precip_mm': const DoubleConverter().toJson(instance.precipMm),
      'precip_in': const DoubleConverter().toJson(instance.precipIn),
      'humidity': const IntConverter().toJson(instance.humidity),
      'cloud': const IntConverter().toJson(instance.cloud),
      'feelslike_c': const DoubleConverter().toJson(instance.feelslikeC),
      'feelslike_f': const DoubleConverter().toJson(instance.feelslikeF),
      'vis_km': const DoubleConverter().toJson(instance.visKm),
      'vis_miles': const DoubleConverter().toJson(instance.visMiles),
      'uv': const NullableDoubleConverter().toJson(instance.uv),
      'gust_mph': const DoubleConverter().toJson(instance.gustMph),
      'gust_kph': const DoubleConverter().toJson(instance.gustKph),
      'windchill_c':
          const NullableDoubleConverter().toJson(instance.windchillC),
      'windchill_f':
          const NullableDoubleConverter().toJson(instance.windchillF),
      'heatindex_c':
          const NullableDoubleConverter().toJson(instance.heatindexC),
      'heatindex_f':
          const NullableDoubleConverter().toJson(instance.heatindexF),
      'dewpoint_c': const NullableDoubleConverter().toJson(instance.dewpointC),
      'dewpoint_f': const NullableDoubleConverter().toJson(instance.dewpointF),
      'will_it_rain': instance.willItRain,
      'will_it_snow': instance.willItSnow,
      'chance_of_rain': instance.chanceOfRain,
      'chance_of_snow': instance.chanceOfSnow,
    };

_$ConditionImpl _$$ConditionImplFromJson(Map<String, dynamic> json) =>
    _$ConditionImpl(
      text: json['text'] as String,
      icon: json['icon'] as String,
      code: const IntConverter().fromJson(json['code']),
    );

Map<String, dynamic> _$$ConditionImplToJson(_$ConditionImpl instance) =>
    <String, dynamic>{
      'text': instance.text,
      'icon': instance.icon,
      'code': const IntConverter().toJson(instance.code),
    };
