// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'car_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

CarModel _$CarModelFromJson(Map<String, dynamic> json) {
  return _CarModel.fromJson(json);
}

/// @nodoc
mixin _$CarModel {
  String? get id => throw _privateConstructorUsedError;
  String get make => throw _privateConstructorUsedError;
  String get model => throw _privateConstructorUsedError;
  int get year => throw _privateConstructorUsedError;
  int get currentMileage => throw _privateConstructorUsedError;
  DateTime get lastOilChangeDate => throw _privateConstructorUsedError;
  int get lastOilChangeMileage => throw _privateConstructorUsedError;
  int get oilChangeMileageInterval => throw _privateConstructorUsedError;
  int get oilChangeMonthInterval => throw _privateConstructorUsedError;
  bool get filterChanged => throw _privateConstructorUsedError;
  double get oilCost => throw _privateConstructorUsedError;
  double get filterCost => throw _privateConstructorUsedError;
  String? get notes => throw _privateConstructorUsedError;
  String? get imageUrl => throw _privateConstructorUsedError;
  List<String>? get imageUrls => throw _privateConstructorUsedError;
  @JsonKey(name: 'userId')
  String get userId => throw _privateConstructorUsedError;
  DateTime? get createdAt => throw _privateConstructorUsedError;
  DateTime? get updatedAt => throw _privateConstructorUsedError;
  DateTime? get licenseExpiryDate => throw _privateConstructorUsedError;

  /// Serializes this CarModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CarModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CarModelCopyWith<CarModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CarModelCopyWith<$Res> {
  factory $CarModelCopyWith(CarModel value, $Res Function(CarModel) then) =
      _$CarModelCopyWithImpl<$Res, CarModel>;
  @useResult
  $Res call(
      {String? id,
      String make,
      String model,
      int year,
      int currentMileage,
      DateTime lastOilChangeDate,
      int lastOilChangeMileage,
      int oilChangeMileageInterval,
      int oilChangeMonthInterval,
      bool filterChanged,
      double oilCost,
      double filterCost,
      String? notes,
      String? imageUrl,
      List<String>? imageUrls,
      @JsonKey(name: 'userId') String userId,
      DateTime? createdAt,
      DateTime? updatedAt,
      DateTime? licenseExpiryDate});
}

/// @nodoc
class _$CarModelCopyWithImpl<$Res, $Val extends CarModel>
    implements $CarModelCopyWith<$Res> {
  _$CarModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CarModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? make = null,
    Object? model = null,
    Object? year = null,
    Object? currentMileage = null,
    Object? lastOilChangeDate = null,
    Object? lastOilChangeMileage = null,
    Object? oilChangeMileageInterval = null,
    Object? oilChangeMonthInterval = null,
    Object? filterChanged = null,
    Object? oilCost = null,
    Object? filterCost = null,
    Object? notes = freezed,
    Object? imageUrl = freezed,
    Object? imageUrls = freezed,
    Object? userId = null,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
    Object? licenseExpiryDate = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      make: null == make
          ? _value.make
          : make // ignore: cast_nullable_to_non_nullable
              as String,
      model: null == model
          ? _value.model
          : model // ignore: cast_nullable_to_non_nullable
              as String,
      year: null == year
          ? _value.year
          : year // ignore: cast_nullable_to_non_nullable
              as int,
      currentMileage: null == currentMileage
          ? _value.currentMileage
          : currentMileage // ignore: cast_nullable_to_non_nullable
              as int,
      lastOilChangeDate: null == lastOilChangeDate
          ? _value.lastOilChangeDate
          : lastOilChangeDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      lastOilChangeMileage: null == lastOilChangeMileage
          ? _value.lastOilChangeMileage
          : lastOilChangeMileage // ignore: cast_nullable_to_non_nullable
              as int,
      oilChangeMileageInterval: null == oilChangeMileageInterval
          ? _value.oilChangeMileageInterval
          : oilChangeMileageInterval // ignore: cast_nullable_to_non_nullable
              as int,
      oilChangeMonthInterval: null == oilChangeMonthInterval
          ? _value.oilChangeMonthInterval
          : oilChangeMonthInterval // ignore: cast_nullable_to_non_nullable
              as int,
      filterChanged: null == filterChanged
          ? _value.filterChanged
          : filterChanged // ignore: cast_nullable_to_non_nullable
              as bool,
      oilCost: null == oilCost
          ? _value.oilCost
          : oilCost // ignore: cast_nullable_to_non_nullable
              as double,
      filterCost: null == filterCost
          ? _value.filterCost
          : filterCost // ignore: cast_nullable_to_non_nullable
              as double,
      notes: freezed == notes
          ? _value.notes
          : notes // ignore: cast_nullable_to_non_nullable
              as String?,
      imageUrl: freezed == imageUrl
          ? _value.imageUrl
          : imageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      imageUrls: freezed == imageUrls
          ? _value.imageUrls
          : imageUrls // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      licenseExpiryDate: freezed == licenseExpiryDate
          ? _value.licenseExpiryDate
          : licenseExpiryDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CarModelImplCopyWith<$Res>
    implements $CarModelCopyWith<$Res> {
  factory _$$CarModelImplCopyWith(
          _$CarModelImpl value, $Res Function(_$CarModelImpl) then) =
      __$$CarModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? id,
      String make,
      String model,
      int year,
      int currentMileage,
      DateTime lastOilChangeDate,
      int lastOilChangeMileage,
      int oilChangeMileageInterval,
      int oilChangeMonthInterval,
      bool filterChanged,
      double oilCost,
      double filterCost,
      String? notes,
      String? imageUrl,
      List<String>? imageUrls,
      @JsonKey(name: 'userId') String userId,
      DateTime? createdAt,
      DateTime? updatedAt,
      DateTime? licenseExpiryDate});
}

/// @nodoc
class __$$CarModelImplCopyWithImpl<$Res>
    extends _$CarModelCopyWithImpl<$Res, _$CarModelImpl>
    implements _$$CarModelImplCopyWith<$Res> {
  __$$CarModelImplCopyWithImpl(
      _$CarModelImpl _value, $Res Function(_$CarModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of CarModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? make = null,
    Object? model = null,
    Object? year = null,
    Object? currentMileage = null,
    Object? lastOilChangeDate = null,
    Object? lastOilChangeMileage = null,
    Object? oilChangeMileageInterval = null,
    Object? oilChangeMonthInterval = null,
    Object? filterChanged = null,
    Object? oilCost = null,
    Object? filterCost = null,
    Object? notes = freezed,
    Object? imageUrl = freezed,
    Object? imageUrls = freezed,
    Object? userId = null,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
    Object? licenseExpiryDate = freezed,
  }) {
    return _then(_$CarModelImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      make: null == make
          ? _value.make
          : make // ignore: cast_nullable_to_non_nullable
              as String,
      model: null == model
          ? _value.model
          : model // ignore: cast_nullable_to_non_nullable
              as String,
      year: null == year
          ? _value.year
          : year // ignore: cast_nullable_to_non_nullable
              as int,
      currentMileage: null == currentMileage
          ? _value.currentMileage
          : currentMileage // ignore: cast_nullable_to_non_nullable
              as int,
      lastOilChangeDate: null == lastOilChangeDate
          ? _value.lastOilChangeDate
          : lastOilChangeDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      lastOilChangeMileage: null == lastOilChangeMileage
          ? _value.lastOilChangeMileage
          : lastOilChangeMileage // ignore: cast_nullable_to_non_nullable
              as int,
      oilChangeMileageInterval: null == oilChangeMileageInterval
          ? _value.oilChangeMileageInterval
          : oilChangeMileageInterval // ignore: cast_nullable_to_non_nullable
              as int,
      oilChangeMonthInterval: null == oilChangeMonthInterval
          ? _value.oilChangeMonthInterval
          : oilChangeMonthInterval // ignore: cast_nullable_to_non_nullable
              as int,
      filterChanged: null == filterChanged
          ? _value.filterChanged
          : filterChanged // ignore: cast_nullable_to_non_nullable
              as bool,
      oilCost: null == oilCost
          ? _value.oilCost
          : oilCost // ignore: cast_nullable_to_non_nullable
              as double,
      filterCost: null == filterCost
          ? _value.filterCost
          : filterCost // ignore: cast_nullable_to_non_nullable
              as double,
      notes: freezed == notes
          ? _value.notes
          : notes // ignore: cast_nullable_to_non_nullable
              as String?,
      imageUrl: freezed == imageUrl
          ? _value.imageUrl
          : imageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      imageUrls: freezed == imageUrls
          ? _value._imageUrls
          : imageUrls // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      licenseExpiryDate: freezed == licenseExpiryDate
          ? _value.licenseExpiryDate
          : licenseExpiryDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CarModelImpl extends _CarModel with DiagnosticableTreeMixin {
  const _$CarModelImpl(
      {this.id,
      required this.make,
      required this.model,
      required this.year,
      required this.currentMileage,
      required this.lastOilChangeDate,
      required this.lastOilChangeMileage,
      required this.oilChangeMileageInterval,
      this.oilChangeMonthInterval = 6,
      this.filterChanged = false,
      this.oilCost = 0.0,
      this.filterCost = 0.0,
      this.notes,
      this.imageUrl,
      final List<String>? imageUrls,
      @JsonKey(name: 'userId') required this.userId,
      this.createdAt,
      this.updatedAt,
      this.licenseExpiryDate})
      : _imageUrls = imageUrls,
        super._();

  factory _$CarModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$CarModelImplFromJson(json);

  @override
  final String? id;
  @override
  final String make;
  @override
  final String model;
  @override
  final int year;
  @override
  final int currentMileage;
  @override
  final DateTime lastOilChangeDate;
  @override
  final int lastOilChangeMileage;
  @override
  final int oilChangeMileageInterval;
  @override
  @JsonKey()
  final int oilChangeMonthInterval;
  @override
  @JsonKey()
  final bool filterChanged;
  @override
  @JsonKey()
  final double oilCost;
  @override
  @JsonKey()
  final double filterCost;
  @override
  final String? notes;
  @override
  final String? imageUrl;
  final List<String>? _imageUrls;
  @override
  List<String>? get imageUrls {
    final value = _imageUrls;
    if (value == null) return null;
    if (_imageUrls is EqualUnmodifiableListView) return _imageUrls;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  @JsonKey(name: 'userId')
  final String userId;
  @override
  final DateTime? createdAt;
  @override
  final DateTime? updatedAt;
  @override
  final DateTime? licenseExpiryDate;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'CarModel(id: $id, make: $make, model: $model, year: $year, currentMileage: $currentMileage, lastOilChangeDate: $lastOilChangeDate, lastOilChangeMileage: $lastOilChangeMileage, oilChangeMileageInterval: $oilChangeMileageInterval, oilChangeMonthInterval: $oilChangeMonthInterval, filterChanged: $filterChanged, oilCost: $oilCost, filterCost: $filterCost, notes: $notes, imageUrl: $imageUrl, imageUrls: $imageUrls, userId: $userId, createdAt: $createdAt, updatedAt: $updatedAt, licenseExpiryDate: $licenseExpiryDate)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'CarModel'))
      ..add(DiagnosticsProperty('id', id))
      ..add(DiagnosticsProperty('make', make))
      ..add(DiagnosticsProperty('model', model))
      ..add(DiagnosticsProperty('year', year))
      ..add(DiagnosticsProperty('currentMileage', currentMileage))
      ..add(DiagnosticsProperty('lastOilChangeDate', lastOilChangeDate))
      ..add(DiagnosticsProperty('lastOilChangeMileage', lastOilChangeMileage))
      ..add(DiagnosticsProperty(
          'oilChangeMileageInterval', oilChangeMileageInterval))
      ..add(
          DiagnosticsProperty('oilChangeMonthInterval', oilChangeMonthInterval))
      ..add(DiagnosticsProperty('filterChanged', filterChanged))
      ..add(DiagnosticsProperty('oilCost', oilCost))
      ..add(DiagnosticsProperty('filterCost', filterCost))
      ..add(DiagnosticsProperty('notes', notes))
      ..add(DiagnosticsProperty('imageUrl', imageUrl))
      ..add(DiagnosticsProperty('imageUrls', imageUrls))
      ..add(DiagnosticsProperty('userId', userId))
      ..add(DiagnosticsProperty('createdAt', createdAt))
      ..add(DiagnosticsProperty('updatedAt', updatedAt))
      ..add(DiagnosticsProperty('licenseExpiryDate', licenseExpiryDate));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CarModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.make, make) || other.make == make) &&
            (identical(other.model, model) || other.model == model) &&
            (identical(other.year, year) || other.year == year) &&
            (identical(other.currentMileage, currentMileage) ||
                other.currentMileage == currentMileage) &&
            (identical(other.lastOilChangeDate, lastOilChangeDate) ||
                other.lastOilChangeDate == lastOilChangeDate) &&
            (identical(other.lastOilChangeMileage, lastOilChangeMileage) ||
                other.lastOilChangeMileage == lastOilChangeMileage) &&
            (identical(
                    other.oilChangeMileageInterval, oilChangeMileageInterval) ||
                other.oilChangeMileageInterval == oilChangeMileageInterval) &&
            (identical(other.oilChangeMonthInterval, oilChangeMonthInterval) ||
                other.oilChangeMonthInterval == oilChangeMonthInterval) &&
            (identical(other.filterChanged, filterChanged) ||
                other.filterChanged == filterChanged) &&
            (identical(other.oilCost, oilCost) || other.oilCost == oilCost) &&
            (identical(other.filterCost, filterCost) ||
                other.filterCost == filterCost) &&
            (identical(other.notes, notes) || other.notes == notes) &&
            (identical(other.imageUrl, imageUrl) ||
                other.imageUrl == imageUrl) &&
            const DeepCollectionEquality()
                .equals(other._imageUrls, _imageUrls) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            (identical(other.licenseExpiryDate, licenseExpiryDate) ||
                other.licenseExpiryDate == licenseExpiryDate));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        id,
        make,
        model,
        year,
        currentMileage,
        lastOilChangeDate,
        lastOilChangeMileage,
        oilChangeMileageInterval,
        oilChangeMonthInterval,
        filterChanged,
        oilCost,
        filterCost,
        notes,
        imageUrl,
        const DeepCollectionEquality().hash(_imageUrls),
        userId,
        createdAt,
        updatedAt,
        licenseExpiryDate
      ]);

  /// Create a copy of CarModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CarModelImplCopyWith<_$CarModelImpl> get copyWith =>
      __$$CarModelImplCopyWithImpl<_$CarModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CarModelImplToJson(
      this,
    );
  }
}

abstract class _CarModel extends CarModel {
  const factory _CarModel(
      {final String? id,
      required final String make,
      required final String model,
      required final int year,
      required final int currentMileage,
      required final DateTime lastOilChangeDate,
      required final int lastOilChangeMileage,
      required final int oilChangeMileageInterval,
      final int oilChangeMonthInterval,
      final bool filterChanged,
      final double oilCost,
      final double filterCost,
      final String? notes,
      final String? imageUrl,
      final List<String>? imageUrls,
      @JsonKey(name: 'userId') required final String userId,
      final DateTime? createdAt,
      final DateTime? updatedAt,
      final DateTime? licenseExpiryDate}) = _$CarModelImpl;
  const _CarModel._() : super._();

  factory _CarModel.fromJson(Map<String, dynamic> json) =
      _$CarModelImpl.fromJson;

  @override
  String? get id;
  @override
  String get make;
  @override
  String get model;
  @override
  int get year;
  @override
  int get currentMileage;
  @override
  DateTime get lastOilChangeDate;
  @override
  int get lastOilChangeMileage;
  @override
  int get oilChangeMileageInterval;
  @override
  int get oilChangeMonthInterval;
  @override
  bool get filterChanged;
  @override
  double get oilCost;
  @override
  double get filterCost;
  @override
  String? get notes;
  @override
  String? get imageUrl;
  @override
  List<String>? get imageUrls;
  @override
  @JsonKey(name: 'userId')
  String get userId;
  @override
  DateTime? get createdAt;
  @override
  DateTime? get updatedAt;
  @override
  DateTime? get licenseExpiryDate;

  /// Create a copy of CarModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CarModelImplCopyWith<_$CarModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
