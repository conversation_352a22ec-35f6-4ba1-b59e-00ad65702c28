// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'maintenance_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$MaintenanceModelImpl _$$MaintenanceModelImplFromJson(
        Map<String, dynamic> json) =>
    _$MaintenanceModelImpl(
      id: json['id'] as String?,
      carId: json['carId'] as String,
      userId: json['userId'] as String,
      description: json['description'] as String,
      maintenanceType: json['maintenanceType'] as String,
      mileage: (json['mileage'] as num).toInt(),
      cost: (json['cost'] as num).toDouble(),
      serviceProvider: json['serviceProvider'] as String,
      notes: json['notes'] as String,
      date: DateTime.parse(json['date'] as String),
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      photoUrls: (json['photoUrls'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
    );

Map<String, dynamic> _$$MaintenanceModelImplToJson(
        _$MaintenanceModelImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'carId': instance.carId,
      'userId': instance.userId,
      'description': instance.description,
      'maintenanceType': instance.maintenanceType,
      'mileage': instance.mileage,
      'cost': instance.cost,
      'serviceProvider': instance.serviceProvider,
      'notes': instance.notes,
      'date': instance.date.toIso8601String(),
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'photoUrls': instance.photoUrls,
    };
