import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:flutter/foundation.dart';

part 'weather_model.freezed.dart';
part 'weather_model.g.dart';

/// Custom converter for handling nullable double values
class NullableDoubleConverter implements JsonConverter<double?, dynamic> {
  const NullableDoubleConverter();

  @override
  double? fromJson(dynamic json) {
    if (json == null) return null;
    return json is int ? json.toDouble() : json as double;
  }

  @override
  dynamic toJson(double? object) => object;
}

/// Custom converter for handling double values with null fallback
class DoubleConverter implements JsonConverter<double, dynamic> {
  const DoubleConverter();

  @override
  double fromJson(dynamic json) {
    if (json == null) return 0.0;
    return json is int ? json.toDouble() : json as double;
  }

  @override
  dynamic toJson(double object) => object;
}

/// Custom converter for handling integer values with null fallback
class IntConverter implements JsonConverter<int, dynamic> {
  const IntConverter();

  @override
  int fromJson(dynamic json) {
    if (json == null) return 0;
    return json as int;
  }

  @override
  dynamic toJson(int object) => object;
}

@freezed
class WeatherModel with _$WeatherModel {
  const factory WeatherModel({
    required Location location,
    required Current current,
  }) = _WeatherModel;

  factory WeatherModel.fromJson(Map<String, dynamic> json) => 
      _$WeatherModelFromJson(json);
}

@freezed
class Location with _$Location {
  const factory Location({
    required String name,
    String? region,
    required String country,
    @DoubleConverter() required double lat,
    @DoubleConverter() required double lon,
    @JsonKey(name: 'tz_id') String? tzId,
    @JsonKey(name: 'localtime_epoch') int? localtimeEpoch,
    String? localtime,
  }) = _Location;

  factory Location.fromJson(Map<String, dynamic> json) => 
      _$LocationFromJson(json);
}

@freezed
class Current with _$Current {
  const factory Current({
    @JsonKey(name: 'last_updated_epoch') 
    @IntConverter() required int lastUpdatedEpoch,
    
    @JsonKey(name: 'last_updated') 
    required String lastUpdated,
    
    @JsonKey(name: 'temp_c') 
    @DoubleConverter() required double tempC,
    
    @JsonKey(name: 'temp_f') 
    @DoubleConverter() required double tempF,
    
    @JsonKey(name: 'is_day') 
    @IntConverter() required int isDay,
    
    required Condition condition,
    
    @JsonKey(name: 'wind_mph') 
    @DoubleConverter() required double windMph,
    
    @JsonKey(name: 'wind_kph') 
    @DoubleConverter() required double windKph,
    
    @JsonKey(name: 'wind_degree') 
    @IntConverter() required int windDegree,
    
    @JsonKey(name: 'wind_dir') 
    required String windDir,
    
    @JsonKey(name: 'pressure_mb') 
    @DoubleConverter() required double pressureMb,
    
    @JsonKey(name: 'pressure_in') 
    @DoubleConverter() required double pressureIn,
    
    @JsonKey(name: 'precip_mm') 
    @DoubleConverter() required double precipMm,
    
    @JsonKey(name: 'precip_in') 
    @DoubleConverter() required double precipIn,
    
    @IntConverter() required int humidity,
    
    @IntConverter() required int cloud,
    
    @JsonKey(name: 'feelslike_c') 
    @DoubleConverter() required double feelslikeC,
    
    @JsonKey(name: 'feelslike_f') 
    @DoubleConverter() required double feelslikeF,
    
    @JsonKey(name: 'vis_km') 
    @DoubleConverter() required double visKm,
    
    @JsonKey(name: 'vis_miles') 
    @DoubleConverter() required double visMiles,
    
    @NullableDoubleConverter() double? uv,
    
    @JsonKey(name: 'gust_mph') 
    @DoubleConverter() required double gustMph,
    
    @JsonKey(name: 'gust_kph') 
    @DoubleConverter() required double gustKph,
    
    @JsonKey(name: 'windchill_c') 
    @NullableDoubleConverter() double? windchillC,
    
    @JsonKey(name: 'windchill_f') 
    @NullableDoubleConverter() double? windchillF,
    
    @JsonKey(name: 'heatindex_c') 
    @NullableDoubleConverter() double? heatindexC,
    
    @JsonKey(name: 'heatindex_f') 
    @NullableDoubleConverter() double? heatindexF,
    
    @JsonKey(name: 'dewpoint_c') 
    @NullableDoubleConverter() double? dewpointC,
    
    @JsonKey(name: 'dewpoint_f') 
    @NullableDoubleConverter() double? dewpointF,
    
    @JsonKey(name: 'will_it_rain') 
    int? willItRain,
    
    @JsonKey(name: 'will_it_snow') 
    int? willItSnow,
    
    @JsonKey(name: 'chance_of_rain') 
    int? chanceOfRain,
    
    @JsonKey(name: 'chance_of_snow') 
    int? chanceOfSnow,
  }) = _Current;

  factory Current.fromJson(Map<String, dynamic> json) => 
      _$CurrentFromJson(json);
}

@freezed
class Condition with _$Condition {
  const factory Condition({
    required String text,
    required String icon,
    @IntConverter() required int code,
  }) = _Condition;

  factory Condition.fromJson(Map<String, dynamic> json) => 
      _$ConditionFromJson(json);
} 