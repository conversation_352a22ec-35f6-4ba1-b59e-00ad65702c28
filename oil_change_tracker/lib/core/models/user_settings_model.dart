import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

part 'user_settings_model.freezed.dart';
part 'user_settings_model.g.dart';

@freezed
class UserSettingsModel with _$UserSettingsModel {
  const UserSettingsModel._();

  const factory UserSettingsModel({
    String? id,
    required String userId,
    @Default(true) bool oilChangeReminders,
    @Default(true) bool maintenanceReminders,
    @Default(true) bool mileageReminders,
    @Default(true) bool promotionalNotifications,
    required String preferredLanguage,
    required DateTime createdAt,
    required DateTime updatedAt,
  }) = _UserSettingsModel;

  factory UserSettingsModel.fromJson(Map<String, dynamic> json) =>
      _$UserSettingsModelFromJson(json);

  factory UserSettingsModel.fromFirestore(
    DocumentSnapshot<Map<String, dynamic>> snapshot,
    SnapshotOptions? options,
  ) {
    final data = snapshot.data()!;
    return UserSettingsModel(
      id: snapshot.id,
      userId: data['userId'] as String,
      oilChangeReminders: data['oilChangeReminders'] as bool? ?? true,
      maintenanceReminders: data['maintenanceReminders'] as bool? ?? true,
      mileageReminders: data['mileageReminders'] as bool? ?? true,
      promotionalNotifications: data['promotionalNotifications'] as bool? ?? true,
      preferredLanguage: data['preferredLanguage'] as String,
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      updatedAt: (data['updatedAt'] as Timestamp).toDate(),
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'userId': userId,
      'oilChangeReminders': oilChangeReminders,
      'maintenanceReminders': maintenanceReminders,
      'mileageReminders': mileageReminders,
      'promotionalNotifications': promotionalNotifications,
      'preferredLanguage': preferredLanguage,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
    };
  }
} 