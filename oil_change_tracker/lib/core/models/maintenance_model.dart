import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

part 'maintenance_model.freezed.dart';
part 'maintenance_model.g.dart';

@freezed
class MaintenanceModel with _$MaintenanceModel {
  const MaintenanceModel._();

  const factory MaintenanceModel({
    String? id,
    required String carId,
    required String userId,
    required String description,
    required String maintenanceType,
    required int mileage,
    required double cost,
    required String serviceProvider,
    required String notes,
    required DateTime date,
    required DateTime createdAt,
    required DateTime updatedAt,
    @Default([]) List<String> photoUrls,
  }) = _MaintenanceModel;

  factory MaintenanceModel.fromJson(Map<String, dynamic> json) =>
      _$MaintenanceModelFromJson(json);

  factory MaintenanceModel.fromFirestore(
    DocumentSnapshot<Map<String, dynamic>> snapshot,
    SnapshotOptions? options,
  ) {
    final data = snapshot.data()!;
    return MaintenanceModel(
      id: snapshot.id,
      carId: data['carId'] as String? ?? '',
      userId: data['userId'] as String? ?? '',
      description: data['description'] as String? ?? '',
      maintenanceType: data['maintenanceType'] as String? ?? 'general',
      mileage: data['mileage'] as int? ?? 0,
      cost: (data['cost'] as num?)?.toDouble() ?? 0.0,
      serviceProvider: data['serviceProvider'] as String? ?? '',
      notes: data['notes'] as String? ?? '',
      date: (data['date'] as Timestamp?)?.toDate() ?? DateTime.now(),
      createdAt: (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (data['updatedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      photoUrls: (data['photoUrls'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ?? [],
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'carId': carId,
      'userId': userId,
      'description': description,
      'maintenanceType': maintenanceType,
      'mileage': mileage,
      'cost': cost,
      'serviceProvider': serviceProvider,
      'notes': notes,
      'date': Timestamp.fromDate(date),
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'photoUrls': photoUrls,
    };
  }
} 