// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'user_settings_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

UserSettingsModel _$UserSettingsModelFromJson(Map<String, dynamic> json) {
  return _UserSettingsModel.fromJson(json);
}

/// @nodoc
mixin _$UserSettingsModel {
  String? get id => throw _privateConstructorUsedError;
  String get userId => throw _privateConstructorUsedError;
  bool get oilChangeReminders => throw _privateConstructorUsedError;
  bool get maintenanceReminders => throw _privateConstructorUsedError;
  bool get mileageReminders => throw _privateConstructorUsedError;
  bool get promotionalNotifications => throw _privateConstructorUsedError;
  String get preferredLanguage => throw _privateConstructorUsedError;
  DateTime get createdAt => throw _privateConstructorUsedError;
  DateTime get updatedAt => throw _privateConstructorUsedError;

  /// Serializes this UserSettingsModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of UserSettingsModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $UserSettingsModelCopyWith<UserSettingsModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UserSettingsModelCopyWith<$Res> {
  factory $UserSettingsModelCopyWith(
          UserSettingsModel value, $Res Function(UserSettingsModel) then) =
      _$UserSettingsModelCopyWithImpl<$Res, UserSettingsModel>;
  @useResult
  $Res call(
      {String? id,
      String userId,
      bool oilChangeReminders,
      bool maintenanceReminders,
      bool mileageReminders,
      bool promotionalNotifications,
      String preferredLanguage,
      DateTime createdAt,
      DateTime updatedAt});
}

/// @nodoc
class _$UserSettingsModelCopyWithImpl<$Res, $Val extends UserSettingsModel>
    implements $UserSettingsModelCopyWith<$Res> {
  _$UserSettingsModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of UserSettingsModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? userId = null,
    Object? oilChangeReminders = null,
    Object? maintenanceReminders = null,
    Object? mileageReminders = null,
    Object? promotionalNotifications = null,
    Object? preferredLanguage = null,
    Object? createdAt = null,
    Object? updatedAt = null,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      oilChangeReminders: null == oilChangeReminders
          ? _value.oilChangeReminders
          : oilChangeReminders // ignore: cast_nullable_to_non_nullable
              as bool,
      maintenanceReminders: null == maintenanceReminders
          ? _value.maintenanceReminders
          : maintenanceReminders // ignore: cast_nullable_to_non_nullable
              as bool,
      mileageReminders: null == mileageReminders
          ? _value.mileageReminders
          : mileageReminders // ignore: cast_nullable_to_non_nullable
              as bool,
      promotionalNotifications: null == promotionalNotifications
          ? _value.promotionalNotifications
          : promotionalNotifications // ignore: cast_nullable_to_non_nullable
              as bool,
      preferredLanguage: null == preferredLanguage
          ? _value.preferredLanguage
          : preferredLanguage // ignore: cast_nullable_to_non_nullable
              as String,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      updatedAt: null == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$UserSettingsModelImplCopyWith<$Res>
    implements $UserSettingsModelCopyWith<$Res> {
  factory _$$UserSettingsModelImplCopyWith(_$UserSettingsModelImpl value,
          $Res Function(_$UserSettingsModelImpl) then) =
      __$$UserSettingsModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? id,
      String userId,
      bool oilChangeReminders,
      bool maintenanceReminders,
      bool mileageReminders,
      bool promotionalNotifications,
      String preferredLanguage,
      DateTime createdAt,
      DateTime updatedAt});
}

/// @nodoc
class __$$UserSettingsModelImplCopyWithImpl<$Res>
    extends _$UserSettingsModelCopyWithImpl<$Res, _$UserSettingsModelImpl>
    implements _$$UserSettingsModelImplCopyWith<$Res> {
  __$$UserSettingsModelImplCopyWithImpl(_$UserSettingsModelImpl _value,
      $Res Function(_$UserSettingsModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of UserSettingsModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? userId = null,
    Object? oilChangeReminders = null,
    Object? maintenanceReminders = null,
    Object? mileageReminders = null,
    Object? promotionalNotifications = null,
    Object? preferredLanguage = null,
    Object? createdAt = null,
    Object? updatedAt = null,
  }) {
    return _then(_$UserSettingsModelImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      oilChangeReminders: null == oilChangeReminders
          ? _value.oilChangeReminders
          : oilChangeReminders // ignore: cast_nullable_to_non_nullable
              as bool,
      maintenanceReminders: null == maintenanceReminders
          ? _value.maintenanceReminders
          : maintenanceReminders // ignore: cast_nullable_to_non_nullable
              as bool,
      mileageReminders: null == mileageReminders
          ? _value.mileageReminders
          : mileageReminders // ignore: cast_nullable_to_non_nullable
              as bool,
      promotionalNotifications: null == promotionalNotifications
          ? _value.promotionalNotifications
          : promotionalNotifications // ignore: cast_nullable_to_non_nullable
              as bool,
      preferredLanguage: null == preferredLanguage
          ? _value.preferredLanguage
          : preferredLanguage // ignore: cast_nullable_to_non_nullable
              as String,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      updatedAt: null == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$UserSettingsModelImpl extends _UserSettingsModel {
  const _$UserSettingsModelImpl(
      {this.id,
      required this.userId,
      this.oilChangeReminders = true,
      this.maintenanceReminders = true,
      this.mileageReminders = true,
      this.promotionalNotifications = true,
      required this.preferredLanguage,
      required this.createdAt,
      required this.updatedAt})
      : super._();

  factory _$UserSettingsModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$UserSettingsModelImplFromJson(json);

  @override
  final String? id;
  @override
  final String userId;
  @override
  @JsonKey()
  final bool oilChangeReminders;
  @override
  @JsonKey()
  final bool maintenanceReminders;
  @override
  @JsonKey()
  final bool mileageReminders;
  @override
  @JsonKey()
  final bool promotionalNotifications;
  @override
  final String preferredLanguage;
  @override
  final DateTime createdAt;
  @override
  final DateTime updatedAt;

  @override
  String toString() {
    return 'UserSettingsModel(id: $id, userId: $userId, oilChangeReminders: $oilChangeReminders, maintenanceReminders: $maintenanceReminders, mileageReminders: $mileageReminders, promotionalNotifications: $promotionalNotifications, preferredLanguage: $preferredLanguage, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UserSettingsModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.oilChangeReminders, oilChangeReminders) ||
                other.oilChangeReminders == oilChangeReminders) &&
            (identical(other.maintenanceReminders, maintenanceReminders) ||
                other.maintenanceReminders == maintenanceReminders) &&
            (identical(other.mileageReminders, mileageReminders) ||
                other.mileageReminders == mileageReminders) &&
            (identical(
                    other.promotionalNotifications, promotionalNotifications) ||
                other.promotionalNotifications == promotionalNotifications) &&
            (identical(other.preferredLanguage, preferredLanguage) ||
                other.preferredLanguage == preferredLanguage) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      userId,
      oilChangeReminders,
      maintenanceReminders,
      mileageReminders,
      promotionalNotifications,
      preferredLanguage,
      createdAt,
      updatedAt);

  /// Create a copy of UserSettingsModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UserSettingsModelImplCopyWith<_$UserSettingsModelImpl> get copyWith =>
      __$$UserSettingsModelImplCopyWithImpl<_$UserSettingsModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$UserSettingsModelImplToJson(
      this,
    );
  }
}

abstract class _UserSettingsModel extends UserSettingsModel {
  const factory _UserSettingsModel(
      {final String? id,
      required final String userId,
      final bool oilChangeReminders,
      final bool maintenanceReminders,
      final bool mileageReminders,
      final bool promotionalNotifications,
      required final String preferredLanguage,
      required final DateTime createdAt,
      required final DateTime updatedAt}) = _$UserSettingsModelImpl;
  const _UserSettingsModel._() : super._();

  factory _UserSettingsModel.fromJson(Map<String, dynamic> json) =
      _$UserSettingsModelImpl.fromJson;

  @override
  String? get id;
  @override
  String get userId;
  @override
  bool get oilChangeReminders;
  @override
  bool get maintenanceReminders;
  @override
  bool get mileageReminders;
  @override
  bool get promotionalNotifications;
  @override
  String get preferredLanguage;
  @override
  DateTime get createdAt;
  @override
  DateTime get updatedAt;

  /// Create a copy of UserSettingsModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UserSettingsModelImplCopyWith<_$UserSettingsModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
