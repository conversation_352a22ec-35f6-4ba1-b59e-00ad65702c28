import 'package:flutter/material.dart';
import '../theme/theme_extensions.dart';

/// A reusable empty state widget with clean, flat design
class EmptyStateWidget extends StatelessWidget {
  final IconData icon;
  final String title;
  final String description;
  final String? buttonText;
  final VoidCallback? onButtonPressed;
  final Color? iconColor;
  final double iconSize;
  final bool showButton;

  const EmptyStateWidget({
    super.key,
    required this.icon,
    required this.title,
    required this.description,
    this.buttonText,
    this.onButtonPressed,
    this.iconColor,
    this.iconSize = 80,
    this.showButton = true,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Icon container with clean background
            Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: (iconColor ?? context.accentColor).withOpacity(0.1),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Icon(
                icon,
                size: iconSize,
                color: iconColor ?? context.accentColor,
              ),
            ),
            const SizedBox(height: 24),
            
            // Title
            Text(
              title,
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: context.primaryTextColor,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 12),
            
            // Description
            Text(
              description,
              style: TextStyle(
                fontSize: 16,
                color: context.secondaryTextColor,
                height: 1.5,
              ),
              textAlign: TextAlign.center,
            ),
            
            if (showButton && buttonText != null && onButtonPressed != null) ...[
              const SizedBox(height: 32),
              
              // Action button with clean design
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: onButtonPressed,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: context.accentColor,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    elevation: 0,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: Text(
                    buttonText!,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

/// Specific empty state for cars list
class CarsEmptyState extends StatelessWidget {
  final VoidCallback? onAddCar;
  final bool isOffline;

  const CarsEmptyState({
    super.key,
    this.onAddCar,
    this.isOffline = false,
  });

  @override
  Widget build(BuildContext context) {
    return EmptyStateWidget(
      icon: Icons.directions_car_rounded,
      title: isOffline ? 'Offline Mode' : 'No Cars Added Yet',
      description: isOffline 
          ? 'You\'re currently offline. Please check your internet connection to add cars and sync your data.'
          : 'Start tracking your vehicle maintenance by adding your first car. Keep all your oil changes and maintenance records in one place.',
      buttonText: isOffline ? null : 'Add Your First Car',
      onButtonPressed: isOffline ? null : onAddCar,
      showButton: !isOffline,
    );
  }
}

/// Specific empty state for dashboard
class DashboardEmptyState extends StatelessWidget {
  final VoidCallback? onAddCar;

  const DashboardEmptyState({
    super.key,
    this.onAddCar,
  });

  @override
  Widget build(BuildContext context) {
    return EmptyStateWidget(
      icon: Icons.dashboard_rounded,
      title: 'Welcome to Oil Change Tracker',
      description: 'Get started by adding your first vehicle. Track oil changes, maintenance records, and never miss another service again.',
      buttonText: 'Add Your First Car',
      onButtonPressed: onAddCar,
    );
  }
}
