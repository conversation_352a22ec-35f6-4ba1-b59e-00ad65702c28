import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import '../../generated/app_localizations.dart';
import 'dart:developer' as dev;

final sharedPreferencesProvider = Provider<SharedPreferences>((ref) {
  throw UnimplementedError('Initialize this provider in your main.dart');
});

final localeProvider = StateNotifierProvider<LocaleNotifier, Locale>((ref) {
  final prefs = ref.watch(sharedPreferencesProvider);
  return LocaleNotifier(prefs);
}, dependencies: [sharedPreferencesProvider]);

class LocaleNotifier extends StateNotifier<Locale> {
  final SharedPreferences _prefs;
  final _analytics = FirebaseAnalytics.instance;

  LocaleNotifier(this._prefs) : super(_loadInitialLocale(_prefs));

  static Locale _loadInitialLocale(SharedPreferences prefs) {
    try {
      final isFirstLaunch = !prefs.containsKey('locale');
      if (isFirstLaunch) {
        // Get device locale
        final deviceLocale = WidgetsBinding.instance.window.locale;
        final deviceLanguageCode = deviceLocale.languageCode;
        
        // Check if device locale is supported
        final isSupported = S.supportedLocales
            .map((locale) => locale.languageCode)
            .contains(deviceLanguageCode);
        
        // Use device locale if supported, otherwise fallback to English
        final localeToUse = isSupported ? deviceLanguageCode : 'en';
        prefs.setString('locale', localeToUse);
        return Locale(localeToUse);
      }
      
      final savedLocale = prefs.getString('locale');
      return savedLocale != null ? Locale(savedLocale) : const Locale('en');
    } catch (e) {
      dev.log('Error loading initial locale: $e');
      return const Locale('en');
    }
  }

  Future<void> toggleLocale() async {
    try {
      final currentLocale = state;
      final newLocale = (currentLocale.languageCode == 'en') ? 
          const Locale('ar') : const Locale('en');
          
      await _saveLocale(newLocale.languageCode);
      state = newLocale;
    } catch (e) {
      dev.log('Error toggling locale: $e');
    }
  }

  Future<void> setLocale(Locale locale) async {
    try {
      await _saveLocale(locale.languageCode);
      state = locale;
    } catch (e) {
      dev.log('Error setting locale: $e');
    }
  }

  Future<void> _saveLocale(String languageCode) async {
    try {
      // Validate locale is supported
      final isSupported = S.supportedLocales
          .map((l) => l.languageCode)
          .contains(languageCode);
      
      if (!isSupported) {
        throw UnsupportedError('Locale $languageCode is not supported');
      }

      // Log locale change
      await _analytics.logEvent(
        name: 'change_language',
        parameters: {
          'from': state.languageCode,
          'to': languageCode,
        },
      );

      await _prefs.setString('locale', languageCode);
    } catch (e) {
      dev.log('Error setting locale: $e');
      rethrow;
    }
  }

  bool isLocaleSupported(String languageCode) {
    return S.supportedLocales
        .map((locale) => locale.languageCode)
        .contains(languageCode);
  }

  List<Locale> get supportedLocales => S.supportedLocales.toList();
} 