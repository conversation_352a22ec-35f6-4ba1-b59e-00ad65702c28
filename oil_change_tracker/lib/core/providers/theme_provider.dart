import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/storage_service.dart';

/// Enum representing the available theme modes in the app
enum AppThemeMode {
  dark,
  light,
  system
}

/// Provider for the current theme mode
final themeModeProvider = StateNotifierProvider<ThemeModeNotifier, ThemeMode>((ref) {
  final storageService = ref.watch(storageServiceProvider);
  return ThemeModeNotifier(storageService);
}, dependencies: [storageServiceProvider]);

/// Notifier for managing theme mode changes
class ThemeModeNotifier extends StateNotifier<ThemeMode> {
  static const _themePreferenceKey = 'theme_mode';
  
  final StorageService _storageService;

  ThemeModeNotifier(this._storageService) : super(_loadThemeMode(_storageService));

  /// Load theme mode from storage
  static ThemeMode _loadThemeMode(StorageService storageService) {
    final storedThemeIndex = storageService.getInt(_themePreferenceKey);
    if (storedThemeIndex == null) return ThemeMode.dark;
    
    switch (AppThemeMode.values[storedThemeIndex]) {
      case AppThemeMode.light:
        return ThemeMode.light;
      case AppThemeMode.dark:
        return ThemeMode.dark;
      case AppThemeMode.system:
        return ThemeMode.system;
    }
  }

  /// Updates the theme mode and persists the selection
  Future<void> setThemeMode(AppThemeMode themeMode) async {
    await _storageService.setInt(_themePreferenceKey, themeMode.index);
    
    switch (themeMode) {
      case AppThemeMode.light:
        state = ThemeMode.light;
        break;
      case AppThemeMode.dark:
        state = ThemeMode.dark;
        break;
      case AppThemeMode.system:
        state = ThemeMode.system;
        break;
    }
  }
} 