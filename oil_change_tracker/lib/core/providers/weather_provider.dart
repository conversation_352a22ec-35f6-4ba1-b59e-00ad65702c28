import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:oil_change_tracker/core/models/weather_model.dart';
import 'package:oil_change_tracker/core/services/weather_service.dart';
import 'package:oil_change_tracker/core/providers/locale_provider.dart';
import 'dart:developer' as dev;

// Provider to help with refreshing weather data manually
final weatherRefreshProvider = StateProvider<int>((ref) => 0);

// Provider to track whether user explicitly wants to use location-based weather
final useLocationForWeatherProvider = StateProvider<bool>((ref) => false);

final weatherProvider = FutureProvider.autoDispose<WeatherModel?>((ref) async {
  final weatherService = ref.watch(weatherServiceProvider);
  // Watch the refresh provider to enable manual refresh
  ref.watch(weatherRefreshProvider);
  
  // Get the current locale to pass to the weather service
  final locale = ref.watch(localeProvider);
  
  // Check if user has explicitly requested to use location
  final useLocation = ref.watch(useLocationForWeatherProvider);
  
  dev.log('Weather provider activated - attempting to fetch weather data (useLocation: $useLocation)');
  
  try {
    if (useLocation) {
      // Only try with device location if explicitly requested by user
      final weather = await weatherService.getWeatherByLocation(locale.languageCode);
      if (weather != null) {
        dev.log('Weather data successfully retrieved via location');
        return weather;
      }
    }
    
    // Use IP-based as primary method or fallback
    dev.log('Using IP-based weather retrieval');
    final ipWeather = await weatherService.getWeatherByIp(locale.languageCode);
    if (ipWeather != null) {
      dev.log('Weather data successfully retrieved via IP');
      return ipWeather;
    }
    
    // If all methods fail, return null
    dev.log('All weather retrieval methods failed');
    return null;
  } catch (e) {
    dev.log('Error in weather provider', error: e.toString());
    dev.log('Stack trace: ${StackTrace.current}');
    return null;
  }
}, dependencies: [localeProvider, weatherServiceProvider, weatherRefreshProvider, useLocationForWeatherProvider]); 