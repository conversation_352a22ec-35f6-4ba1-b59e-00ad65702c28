import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'locale_provider.dart';

/// Provider that gives access to the SharedPreferences instance
/// This is imported from locale_provider.dart, so we don't define it here
// final sharedPreferencesProvider = Provider<SharedPreferences>((ref) {
//   throw UnimplementedError('Should be overridden in main.dart');
// });

/// Provider for accessing values from SharedPreferences 
final prefsProvider = Provider.family<dynamic, String>((ref, key) {
  final prefs = ref.watch(sharedPreferencesProvider);
  return prefs.get(key);
}, dependencies: [sharedPreferencesProvider]);

/// Provider for storing a boolean value in SharedPreferences
final boolPrefsProvider = StateProvider.family<bool, String>((ref, key) {
  final prefs = ref.watch(sharedPreferencesProvider);
  return prefs.getBool(key) ?? false;
}, dependencies: [sharedPreferencesProvider]);

/// Provider for storing a string value in SharedPreferences
final stringPrefsProvider = StateProvider.family<String?, String>((ref, key) {
  final prefs = ref.watch(sharedPreferencesProvider);
  return prefs.getString(key);
}, dependencies: [sharedPreferencesProvider]);

/// Provider for storing an int value in SharedPreferences
final intPrefsProvider = StateProvider.family<int?, String>((ref, key) {
  final prefs = ref.watch(sharedPreferencesProvider);
  return prefs.getInt(key);
}, dependencies: [sharedPreferencesProvider]); 