import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'locale_provider.dart';

/// Provider to track whether onboarding has been completed
final onboardingCompletedProvider = StateNotifierProvider<OnboardingNotifier, bool>((ref) {
  final prefs = ref.watch(sharedPreferencesProvider);
  return OnboardingNotifier(prefs);
}, dependencies: [sharedPreferencesProvider]);

class OnboardingNotifier extends StateNotifier<bool> {
  final SharedPreferences _prefs;
  static const String _key = 'onboarding_completed';

  OnboardingNotifier(this._prefs) : super(_loadInitialState(_prefs));

  static bool _loadInitialState(SharedPreferences prefs) {
    return prefs.getBool(_key) ?? false;
  }

  /// Mark onboarding as completed
  Future<void> completeOnboarding() async {
    await _prefs.setBool(_key, true);
    state = true;
  }

  /// Reset onboarding status (for testing purposes)
  Future<void> resetOnboarding() async {
    await _prefs.setBool(_key, false);
    state = false;
  }
} 