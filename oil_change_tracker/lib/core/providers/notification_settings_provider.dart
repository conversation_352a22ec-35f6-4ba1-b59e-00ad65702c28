import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';

part 'notification_settings_provider.g.dart';

class NotificationSettings {
  final bool promotionalNotifications;
  final bool oilChangeReminders;
  final bool maintenanceReminders;
  final bool mileageReminders;
  final String? error;

  NotificationSettings({
    this.promotionalNotifications = false,
    this.oilChangeReminders = true,
    this.maintenanceReminders = true,
    this.mileageReminders = true,
    this.error,
  });
}

@riverpod
class NotificationSettingsNotifier extends _$NotificationSettingsNotifier {
  @override
  NotificationSettings build() {
    return NotificationSettings();
  }

  void togglePromotionalNotifications() {
    state = NotificationSettings(
      promotionalNotifications: !state.promotionalNotifications,
      oilChangeReminders: state.oilChangeReminders,
      maintenanceReminders: state.maintenanceReminders,
      mileageReminders: state.mileageReminders,
    );
  }

  void toggleOilChangeReminders() {
    state = NotificationSettings(
      promotionalNotifications: state.promotionalNotifications,
      oilChangeReminders: !state.oilChangeReminders,
      maintenanceReminders: state.maintenanceReminders,
      mileageReminders: state.mileageReminders,
    );
  }

  void toggleMaintenanceReminders() {
    state = NotificationSettings(
      promotionalNotifications: state.promotionalNotifications,
      oilChangeReminders: state.oilChangeReminders,
      maintenanceReminders: !state.maintenanceReminders,
      mileageReminders: state.mileageReminders,
    );
  }

  void toggleMileageReminders() {
    state = NotificationSettings(
      promotionalNotifications: state.promotionalNotifications,
      oilChangeReminders: state.oilChangeReminders,
      maintenanceReminders: state.maintenanceReminders,
      mileageReminders: !state.mileageReminders,
    );
  }

  Future<void> refreshFcmToken() async {
    try {
      // Get Firebase Messaging instance
      final messaging = FirebaseMessaging.instance;
      
      // Delete the existing token to force refresh
      await messaging.deleteToken();
      
      // Get a new token
      final newToken = await messaging.getToken();
      
      if (newToken == null) {
        state = NotificationSettings(
          promotionalNotifications: state.promotionalNotifications,
          oilChangeReminders: state.oilChangeReminders,
          maintenanceReminders: state.maintenanceReminders,
          mileageReminders: state.mileageReminders,
          error: 'Failed to refresh notification token',
        );
        return;
      }
      
      // Save the new token to the database
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        state = NotificationSettings(
          promotionalNotifications: state.promotionalNotifications,
          oilChangeReminders: state.oilChangeReminders,
          maintenanceReminders: state.maintenanceReminders,
          mileageReminders: state.mileageReminders,
          error: 'User not authenticated',
        );
        return;
      }
      
      // Update in Firestore
      await FirebaseFirestore.instance.collection('users').doc(user.uid).set({
        'fcmToken': newToken,
        'lastTokenUpdate': FieldValue.serverTimestamp(),
      }, SetOptions(merge: true));
      
      // Also update in dedicated token collection
      await FirebaseFirestore.instance.collection('fcm_tokens').doc(user.uid).set({
        'token': newToken,
        'userId': user.uid,
        'deviceInfo': {
          'platform': defaultTargetPlatform.toString(),
          'updatedAt': FieldValue.serverTimestamp(),
        }
      }, SetOptions(merge: true));
      
      // Set success state
      state = NotificationSettings(
        promotionalNotifications: state.promotionalNotifications,
        oilChangeReminders: state.oilChangeReminders,
        maintenanceReminders: state.maintenanceReminders,
        mileageReminders: state.mileageReminders,
        error: null,
      );
    } catch (e) {
      // Set error state
      state = NotificationSettings(
        promotionalNotifications: state.promotionalNotifications,
        oilChangeReminders: state.oilChangeReminders,
        maintenanceReminders: state.maintenanceReminders,
        mileageReminders: state.mileageReminders,
        error: 'Failed to refresh token: $e',
      );
    }
  }
} 