import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:oil_change_tracker/core/services/notification_service.dart';
import 'package:oil_change_tracker/core/models/car_model.dart';
import 'dart:developer' as dev;

class CarsNotifier extends StateNotifier<List<CarModel>> {
  final Ref ref;
  final Provider<NotificationService> notificationServiceProvider;

  CarsNotifier({required this.ref, required this.notificationServiceProvider}) : super([]);

  Future<void> scheduleNotificationsForCar(String carId, BuildContext context) async {
    final car = await getCarById(carId);
    if (car != null) {
      try {
        final notificationService = ref.watch(notificationServiceProvider);
        await notificationService.scheduleOilChangeNotification(car, context);
        
        if (car.licenseExpiryDate != null) {
          await notificationService.scheduleLicenseExpiryNotification(car, context);
        }
        
        dev.log("INFO: [CarsNotifier] Scheduled notifications for car ${car.id}");
      } catch (e) {
        dev.log("ERROR: [CarsNotifier] Error scheduling notifications: $e");
        // Don't rethrow to prevent app crashes
      }
    }
  }

  Future<void> scheduleNotificationsForAllCars(BuildContext? context) async {
    try {
      if (context == null) {
        dev.log("ERROR: [CarsNotifier] Cannot schedule notifications: null context");
        return;
      }
      
      final notificationService = ref.watch(notificationServiceProvider);
      final cars = await getAllCars();
      
      await notificationService.scheduleNotificationsForAllCars(cars, context);
      
      dev.log("INFO: [CarsNotifier] Scheduled notifications for all cars");
    } catch (e) {
      dev.log("ERROR: [CarsNotifier] Error scheduling notifications: $e");
      // Don't rethrow to prevent app crashes
    }
  }
  
  // These methods need to be implemented
  Future<CarModel?> getCarById(String id) async {
    // Implementation needed
    return null;
  }
  
  Future<List<CarModel>> getAllCars() async {
    // Implementation needed
    return [];
  }
} 