import 'package:firebase_auth/firebase_auth.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'auth_provider.g.dart';

@riverpod
Stream<User?> authStateChanges(AuthStateChangesRef ref) {
  FirebaseAuth.instance.setLanguageCode('en');
  return FirebaseAuth.instance.authStateChanges();
}

@riverpod
class Auth extends _$Auth {
  @override
  User? build() {
    return FirebaseAuth.instance.currentUser;
  }

  Future<void> signOut() async {
    await FirebaseAuth.instance.signOut();
  }
} 