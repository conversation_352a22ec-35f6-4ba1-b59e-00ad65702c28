import 'package:flutter/material.dart';
import 'app_colors.dart';

/// Extension for easier theme-aware color access
extension ThemeExtensions on BuildContext {
  /// Get theme-aware primary text color
  Color get primaryTextColor {
    return Theme.of(this).brightness == Brightness.dark 
      ? Colors.white 
      : AppColors.darkGray;
  }

  /// Get theme-aware secondary text color with opacity
  Color get secondaryTextColor {
    return Theme.of(this).brightness == Brightness.dark
      ? Colors.white.withOpacity(0.7)
      : AppColors.darkGray.withOpacity(0.7);
  }

  /// Get theme-aware card/container background color
  Color get containerBackgroundColor {
    // Use the theme's scaffoldBackgroundColor
    return Theme.of(this).scaffoldBackgroundColor;
  }

  /// Get theme-aware card color from the theme's CardTheme
  Color get cardColor {
    // Use the theme's cardTheme.color or fallback to surface color
    return Theme.of(this).cardTheme.color ?? 
           Theme.of(this).colorScheme.surface;
  }

  /// Get theme-aware accent color (gold/burgundy)
  Color get accentColor {
    // Use the theme's primary color
    return Theme.of(this).colorScheme.primary;
  }

  /// Get theme-aware secondary accent color (burgundy/gold)
  Color get secondaryAccentColor {
    // Use the theme's secondary color
    return Theme.of(this).colorScheme.secondary;
  }

  /// Get theme-aware divider/border color
  Color get borderColor {
    // Use the theme's divider color
    return Theme.of(this).dividerColor;
  }

  /// Get the current theme's brightness
  bool get isDarkMode => Theme.of(this).brightness == Brightness.dark;
}