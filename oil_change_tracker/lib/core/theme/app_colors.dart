import 'package:flutter/material.dart';

/// App color constants used throughout the application
/// 
/// This class serves as the single source of truth for the app's color palette.
/// All color references should use these constants directly or via the themed 
/// extensions (context.accentColor, etc.) to ensure consistency.
class AppColors {
  /// Dark Gray - Primary background color for dark theme
  static const Color darkGray = Color(0xFF343131);
  
  /// Dark Card Color - Slightly lighter than darkGray for cards in dark mode
  static const Color darkCardColor = Color(0xFF403C3C);
  
  /// Burgundy - Primary accent color for light theme
  static const Color burgundy = Color(0xFFA04747);
  
  /// Gold - Primary accent color for dark theme 
  static const Color gold = Color(0xFFD8A25E);
  
  /// Yellow - Secondary accent color
  static const Color yellow = Color(0xFFEEDF7A);
  
  /// Create a variant of a color with opacity
  static Color withOpacity(Color color, double opacity) {
    return color.withOpacity(opacity);
  }
  
  /// White with 70% opacity
  static Color white70 = Colors.white.withOpacity(0.7);
  
  /// White with 50% opacity 
  static Color white50 = Colors.white.withOpacity(0.5);
  
  /// Gold with 50% opacity
  static Color gold50 = gold.withOpacity(0.5);
  
  /// Gold with 30% opacity
  static Color gold30 = gold.withOpacity(0.3);
  
  /// Gold with 10% opacity
  static Color gold10 = gold.withOpacity(0.1);
  
  /// Success color
  static const Color success = Colors.green;
  
  /// Error color - same as burgundy
  static const Color error = burgundy;
  
  /// Creates a darker variant of the given color
  static Color darker(Color color, [double amount = .1]) {
    assert(amount >= 0 && amount <= 1);
    
    final hsl = HSLColor.fromColor(color);
    final hslDark = hsl.withLightness((hsl.lightness - amount).clamp(0.0, 1.0));
    
    return hslDark.toColor();
  }
  
  /// Creates a lighter variant of the given color
  static Color lighter(Color color, [double amount = .1]) {
    assert(amount >= 0 && amount <= 1);
    
    final hsl = HSLColor.fromColor(color);
    final hslLight = hsl.withLightness((hsl.lightness + amount).clamp(0.0, 1.0));
    
    return hslLight.toColor();
  }
} 