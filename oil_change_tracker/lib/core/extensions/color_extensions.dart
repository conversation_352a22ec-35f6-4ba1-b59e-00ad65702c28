import 'package:flutter/material.dart';

/// Extension methods for the Color class
extension ColorExtensions on Color {
  /// Creates a new color with the specified alpha value or RGB components.
  /// 
  /// @param alpha - The alpha value (transparency) can be specified as:
  ///   - A double between 0.0 and 1.0 (percentage of opacity)
  ///   - An integer between 0 and 255 (direct alpha channel value)
  /// @param red - Optional red component (0-255)
  /// @param green - Optional green component (0-255)
  /// @param blue - Optional blue component (0-255)
  /// 
  /// @return A new Color instance with modified values
  /// 
  /// Note: For simple opacity changes, prefer using the standard 
  /// `Color.withOpacity(double)` method instead.
  Color withValues({double? alpha, int? red, int? green, int? blue}) {
    // If only alpha is provided and no RGB components, use the more efficient withOpacity
    if (alpha != null && red == null && green == null && blue == null) {
      // Convert alpha to 0-1 range if needed
      final normalizedAlpha = alpha <= 1.0 ? alpha : alpha / 255;
      return withOpacity(normalizedAlpha);
    }
    
    // Otherwise, create a new color with all specified components
    return Color.fromARGB(
      alpha != null 
        ? alpha <= 1.0 
          ? (alpha * 255).round() 
          : alpha.round()
        : this.alpha,
      red ?? this.red,
      green ?? this.green,
      blue ?? this.blue,
    );
  }
  
  /// Creates a darker version of this color.
  /// 
  /// @param amount - Amount to darken, between 0.0 and 1.0
  /// @return A new Color that is darker by the specified amount
  Color darker([double amount = 0.1]) {
    assert(amount >= 0 && amount <= 1);
    
    final hsl = HSLColor.fromColor(this);
    final hslDark = hsl.withLightness((hsl.lightness - amount).clamp(0.0, 1.0));
    
    return hslDark.toColor();
  }
  
  /// Creates a lighter version of this color.
  /// 
  /// @param amount - Amount to lighten, between 0.0 and 1.0
  /// @return A new Color that is lighter by the specified amount
  Color lighter([double amount = 0.1]) {
    assert(amount >= 0 && amount <= 1);
    
    final hsl = HSLColor.fromColor(this);
    final hslLight = hsl.withLightness((hsl.lightness + amount).clamp(0.0, 1.0));
    
    return hslLight.toColor();
  }
}
