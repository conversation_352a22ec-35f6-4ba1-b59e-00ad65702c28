import 'dart:async';
import 'package:firebase_app_check/firebase_app_check.dart';
import 'package:flutter/foundation.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'dart:developer' as dev;

/// Helper for App Check debugging and testing
/// Inspired by: https://firebase.google.com/docs/app-check/custom-resource-backend
class AppCheckDebugHelper {
  // This is the token shown in the error dialog
  static const String debugToken = "e7debe73-145b-44ae-a6ad-be0a15c2bc2c";
  
  /// Force a complete App Check debug setup with proper checks
  static Future<bool> setupDebugToken() async {
    if (!kDebugMode) {
      dev.log('AppCheckDebugHelper: Not in debug mode, skipping debug setup');
      return false;
    }
    
    dev.log('AppCheckDebugHelper: Setting up App Check debug token explicitly');
    
    try {
      // Step 1: Ensure we disable auto-refresh first
      dev.log('AppCheckDebugHelper: Disabling auto-refresh temporarily');
      await FirebaseAppCheck.instance.setTokenAutoRefreshEnabled(false);
      
      // Step 2: Add a delay to ensure changes take effect
      await Future.delayed(const Duration(milliseconds: 500));
      
      // Step 3: Activate debug provider explicitly
      dev.log('AppCheckDebugHelper: Activating with debug provider');
      dev.log('AppCheckDebugHelper: Using debug token: $debugToken');
      await FirebaseAppCheck.instance.activate(
        androidProvider: AndroidProvider.debug,
      );
      
      // Step 4: Re-enable auto-refresh
      dev.log('AppCheckDebugHelper: Re-enabling auto-refresh');
      await FirebaseAppCheck.instance.setTokenAutoRefreshEnabled(true);
      
      // Step 5: Verify with a test request
      return await testAppCheckConnection();
    } catch (e) {
      dev.log('AppCheckDebugHelper: Error during debug setup: $e');
      return false;
    }
  }
  
  /// Test App Check connection by trying to access a special test collection
  static Future<bool> testAppCheckConnection() async {
    try {
      dev.log('AppCheckDebugHelper: Testing connection with Firestore');
      
      // Get a token to check if App Check is working
      final token = await FirebaseAppCheck.instance.getToken();
      if (token == null) {
        dev.log('AppCheckDebugHelper: Failed to get token - check debug token registration');
        return false;
      }
      
      // We got a token, that's promising
      dev.log('AppCheckDebugHelper: Successfully got a token, testing with Firestore');
      
      try {
        // Try to access the special App Check test collection
        final testRef = FirebaseFirestore.instance.collection('_app_check_tests').doc('test');
        
        // Try to read the document
        final snapshot = await testRef.get()
            .timeout(const Duration(seconds: 5));
        
        // If we got here, the test was successful
        dev.log('AppCheckDebugHelper: Successfully accessed _app_check_tests collection');
        return true;
      } catch (e) {
        final errorMsg = e.toString().toLowerCase();
        
        // Check if it's an App Check specific error
        if (errorMsg.contains('permission') || 
            errorMsg.contains('app-check') || 
            errorMsg.contains('app check')) {
          dev.log('AppCheckDebugHelper: App Check permission error: $e');
          dev.log('AppCheckDebugHelper: This suggests debug token is not properly registered');
          dev.log('AppCheckDebugHelper: Register token $debugToken in Firebase Console');
          return false;
        } else {
          // Other Firestore error, but App Check might still be working
          dev.log('AppCheckDebugHelper: Firestore error, but token was issued: $e');
          dev.log('AppCheckDebugHelper: App Check may still be properly configured');
          return true;
        }
      }
    } catch (e) {
      dev.log('AppCheckDebugHelper: Error testing App Check: $e');
      return false;
    }
  }
  
  /// Add App Check token to HTTP headers for custom backend
  static Future<Map<String, String>> addAppCheckHeaders(Map<String, String> headers) async {
    try {
      final token = await FirebaseAppCheck.instance.getToken();
      if (token != null) {
        final updatedHeaders = Map<String, String>.from(headers);
        updatedHeaders['X-Firebase-AppCheck'] = token;
        return updatedHeaders;
      }
    } catch (e) {
      dev.log('AppCheckDebugHelper: Error adding App Check headers: $e');
    }
    return headers;
  }
  
  /// Troubleshoot common App Check issues
  static Future<String> troubleshootAppCheck() async {
    final buffer = StringBuffer();
    buffer.writeln('----- APP CHECK TROUBLESHOOTING -----');
    
    try {
      // Check if we're in debug mode
      buffer.writeln('Debug mode: $kDebugMode');
      buffer.writeln('Debug token: $debugToken');
      
      // Try to get token
      try {
        final token = await FirebaseAppCheck.instance.getToken();
        final hasToken = token != null && token.isNotEmpty;
        buffer.writeln('Got token: $hasToken');
        
        if (hasToken) {
          buffer.writeln('✅ Token retrieval successful');
        } else {
          buffer.writeln('❌ Token retrieval failed');
        }
      } catch (e) {
        buffer.writeln('❌ Error getting token: $e');
      }
      
      // Try provider activation
      try {
        await FirebaseAppCheck.instance.activate(
          androidProvider: AndroidProvider.debug,
        );
        buffer.writeln('✅ Provider activation successful');
      } catch (e) {
        buffer.writeln('❌ Provider activation error: $e');
      }
      
      // Try Firestore access
      try {
        final testRef = FirebaseFirestore.instance.collection('_app_check_tests').doc('test');
        await testRef.get().timeout(const Duration(seconds: 5));
        buffer.writeln('✅ Firestore access successful');
      } catch (e) {
        buffer.writeln('❌ Firestore access error: $e');
        
        // Detailed error analysis
        if (e.toString().contains('permission-denied')) {
          buffer.writeln('   - Permission denied: Debug token may not be registered');
          buffer.writeln('   - Go to Firebase Console → Project Settings → App Check');
          buffer.writeln('   - Register token: $debugToken');
        } else if (e.toString().contains('network')) {
          buffer.writeln('   - Network error: Check internet connection');
        }
      }
      
    } catch (e) {
      buffer.writeln('Error during troubleshooting: $e');
    }
    
    buffer.writeln('------------------------------------');
    return buffer.toString();
  }
} 