// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'secure_storage_service.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$secureStorageServiceHash() =>
    r'fa92c3ef4ae29188d1b8ca5b4b659b3535fa7f3b';

/// A service for securely storing sensitive user data using Flutter Secure Storage
/// and encrypted SharedPreferences.
///
/// Copied from [SecureStorageService].
@ProviderFor(SecureStorageService)
final secureStorageServiceProvider =
    AutoDisposeAsyncNotifierProvider<SecureStorageService, void>.internal(
  SecureStorageService.new,
  name: r'secureStorageServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$secureStorageServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$SecureStorageService = AutoDisposeAsyncNotifier<void>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
