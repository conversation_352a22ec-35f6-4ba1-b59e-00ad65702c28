import 'dart:convert';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import '../utils/logger.dart';

part 'secure_storage_service.g.dart';

/// A service for securely storing sensitive user data using Flutter Secure Storage
/// and encrypted SharedPreferences.
@riverpod
class SecureStorageService extends _$SecureStorageService {
  static const _encryptionKeyName = 'encryption_key';
  static const _securePrefsPrefix = 'secure_';

  late FlutterSecureStorage _secureStorage;
  
  @override
  Future<void> build() async {
    _secureStorage = const FlutterSecureStorage(
      aOptions: AndroidOptions(
        encryptedSharedPreferences: true,
        resetOnError: true,
        keyCipherAlgorithm: KeyCipherAlgorithm.RSA_ECB_PKCS1Padding,
        storageCipherAlgorithm: StorageCipherAlgorithm.AES_GCM_NoPadding,
      ),
    );
    
    // Ensure encryption key exists for the shared preferences encryption
    await _ensureEncryptionKey();
  }

  /// Ensures that an encryption key exists in secure storage
  Future<void> _ensureEncryptionKey() async {
    try {
      final encryptionKey = await _secureStorage.read(key: _encryptionKeyName);
      if (encryptionKey == null) {
        // Generate a random encryption key
        final key = base64Encode(List<int>.generate(32, (_) => _getSecureRandom()));
        await _secureStorage.write(key: _encryptionKeyName, value: key);
      }
    } catch (e) {
      AppLogger.error('Error ensuring encryption key', e);
      // Recreate the key if there was an error
      await _secureStorage.delete(key: _encryptionKeyName);
      final key = base64Encode(List<int>.generate(32, (_) => _getSecureRandom()));
      await _secureStorage.write(key: _encryptionKeyName, value: key);
    }
  }
  
  /// Generate a secure random number
  int _getSecureRandom() {
    // Simple random number for the example - in production
    // use a cryptographically secure random number generator
    return DateTime.now().microsecondsSinceEpoch % 255;
  }

  /// Securely save data to storage
  Future<void> saveSecurely(String key, String value) async {
    try {
      await _secureStorage.write(key: key, value: value);
    } catch (e) {
      AppLogger.error('Error saving securely: $key', e);
      rethrow;
    }
  }

  /// Securely retrieve data from storage
  Future<String?> getSecurely(String key) async {
    try {
      return await _secureStorage.read(key: key);
    } catch (e) {
      AppLogger.error('Error reading securely: $key', e);
      return null;
    }
  }

  /// Delete data from secure storage
  Future<void> deleteSecurely(String key) async {
    try {
      await _secureStorage.delete(key: key);
    } catch (e) {
      AppLogger.error('Error deleting securely: $key', e);
      rethrow;
    }
  }

  /// Save a complex object securely (serializes to JSON first)
  Future<void> saveObjectSecurely<T>(String key, T object) async {
    try {
      final jsonString = jsonEncode(object);
      await saveSecurely(key, jsonString);
    } catch (e) {
      AppLogger.error('Error saving object securely: $key', e);
      rethrow;
    }
  }

  /// Get a complex object from secure storage (deserializes from JSON)
  Future<T?> getObjectSecurely<T>(String key, T Function(Map<String, dynamic>) fromJson) async {
    try {
      final jsonString = await getSecurely(key);
      if (jsonString == null || jsonString.isEmpty) {
        return null;
      }
      
      final map = jsonDecode(jsonString) as Map<String, dynamic>;
      return fromJson(map);
    } catch (e) {
      AppLogger.error('Error getting object securely: $key', e);
      return null;
    }
  }

  /// Check if a key exists in secure storage
  Future<bool> hasKey(String key) async {
    try {
      final value = await _secureStorage.read(key: key);
      return value != null;
    } catch (e) {
      AppLogger.error('Error checking key existence: $key', e);
      return false;
    }
  }

  /// Clear all secure storage
  Future<void> clearAll() async {
    try {
      // Keep the encryption key
      final encryptionKey = await _secureStorage.read(key: _encryptionKeyName);
      
      // Delete everything
      await _secureStorage.deleteAll();
      
      // Restore the encryption key
      if (encryptionKey != null) {
        await _secureStorage.write(key: _encryptionKeyName, value: encryptionKey);
      } else {
        await _ensureEncryptionKey();
      }
    } catch (e) {
      AppLogger.error('Error clearing secure storage', e);
      rethrow;
    }
  }
} 