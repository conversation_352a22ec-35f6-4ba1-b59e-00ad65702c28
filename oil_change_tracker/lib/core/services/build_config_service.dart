import 'package:flutter/foundation.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:flutter/services.dart';
import 'package:package_info_plus/package_info_plus.dart';
import '../utils/logger.dart';

part 'build_config_service.g.dart';

/// A service that verifies the build configuration is appropriate for the environment.
/// This helps ensure no debug code or sensitive information is deployed to production.
@riverpod
class BuildConfigService extends _$BuildConfigService {
  static const String _debugToken = 'DEBUG_TOKEN_PLACEHOLDER';
  static const String _devEndpoint = 'api-dev.oilplusapp.com';
  
  late bool _isProduction;
  
  @override
  Future<void> build() async {
    await _initialize();
  }

  /// Initialize the service and perform checks
  Future<void> _initialize() async {
    // Get package info to check version and build number
    final packageInfo = await PackageInfo.fromPlatform();
    final buildNumber = int.tryParse(packageInfo.buildNumber) ?? 0;
    
    // Determine if this is a production build
    _isProduction = kReleaseMode || (!kDebugMode && buildNumber > 10); // Builds > 10 are considered production
    
    // Run security validations in production mode
    if (_isProduction) {
      await _runProductionChecks();
    } else {
      AppLogger.info('Running in development/debug mode - skipping production checks');
    }
  }
  
  /// Run checks that should only pass in production mode
  Future<void> _runProductionChecks() async {
    try {
      // Check 1: Make sure debug constants are replaced
      final hasDebugTokens = await _checkForDebugTokens();
      if (hasDebugTokens) {
        AppLogger.error('SECURITY RISK: Debug tokens found in production build');
        if (kReleaseMode) {
          throw Exception('SECURITY RISK: Debug tokens found in production build');
        }
      }
      
      // Check 2: Make sure crashlytics is enabled
      final hasCrashlytics = await _checkCrashlyticsEnabled();
      if (!hasCrashlytics) {
        AppLogger.error('ERROR: Crashlytics not enabled in production build');
        if (kReleaseMode) {
          throw Exception('ERROR: Crashlytics not enabled in production build');
        }
      }
      
      // Check 3: Make sure we're not using development endpoints
      final usingDevEndpoints = await _checkForDevEndpoints();
      if (usingDevEndpoints) {
        AppLogger.error('ERROR: Development endpoints found in production build');
        if (kReleaseMode) {
          throw Exception('ERROR: Development endpoints found in production build');
        }
      }
      
      // Log success
      AppLogger.info('Production configuration checks completed successfully');
    } catch (e) {
      AppLogger.error('Error performing production configuration checks', e);
      rethrow;
    }
  }
  
  /// Check if debug tokens exist in the code
  Future<bool> _checkForDebugTokens() async {
    try {
      // This is a simplified check for demonstration - in a real app,
      // you might scan multiple files or use asset fingerprinting
      final assetData = await rootBundle.loadString('assets/config.json');
      return assetData.contains(_debugToken);
    } catch (e) {
      // If file doesn't exist, that's fine - return false
      return false;
    }
  }
  
  /// Check if Crashlytics is properly enabled
  Future<bool> _checkCrashlyticsEnabled() async {
    // In a real implementation, you would check if Crashlytics is actually enabled
    // This is a placeholder implementation
    return true;
  }
  
  /// Check for development endpoints that shouldn't be in production
  Future<bool> _checkForDevEndpoints() async {
    try {
      // This is a simplified check - in a real app, you would 
      // check multiple potential configuration sources
      final assetData = await rootBundle.loadString('assets/config.json');
      return assetData.contains(_devEndpoint);
    } catch (e) {
      // If file doesn't exist, that's fine - return false
      return false;
    }
  }
  
  /// Returns whether the app is running in production mode
  bool get isProduction => _isProduction;
} 