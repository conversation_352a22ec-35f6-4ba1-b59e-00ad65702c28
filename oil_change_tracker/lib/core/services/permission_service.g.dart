// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'permission_service.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$permissionServiceHash() => r'899f61f6d77a47d1b0e30d729db5bf0718efd35d';

/// Permission service to handle runtime permissions in a centralized way
///
/// Copied from [PermissionService].
@ProviderFor(PermissionService)
final permissionServiceProvider =
    AutoDisposeAsyncNotifierProvider<PermissionService, void>.internal(
  PermissionService.new,
  name: r'permissionServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$permissionServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$PermissionService = AutoDisposeAsyncNotifier<void>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
