import 'dart:convert';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:geolocator/geolocator.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'dart:developer' as dev;

/// Provider for the location service
final locationServiceProvider = Provider<LocationService>((ref) {
  final service = LocationService();
  // Initialize the locationService by checking permissions immediately
  // This helps avoid LateInitializationError in production
  service._initializeService();
  return service;
});

/// Service to handle location-related functionality
class LocationService {
  // Firebase references
  final FirebaseStorage _storage = FirebaseStorage.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  
  // Local cache keys
  static const String _lastLocationCacheKey = 'last_location_cache';
  static const String _lastLocationTimestampKey = 'last_location_timestamp';
  
  // Track initialization state to avoid repeated operations
  bool _initialized = false;
  
  // Initialize service by checking permissions and preparing cache
  Future<void> _initializeService() async {
    if (_initialized) return;
    
    try {
      // Check if we have a cached position
      await _getFromLocalCache();
      
      // Check current permission status (doesn't request permission)
      await checkPermission();
      
      _initialized = true;
    } catch (e) {
      dev.log('Error initializing LocationService: $e');
    }
  }
  
  /// Check if location services are enabled
  Future<bool> isLocationServiceEnabled() async {
    try {
      return await Geolocator.isLocationServiceEnabled();
    } catch (e) {
      dev.log('Error checking location service status: $e');
      return false;
    }
  }
  
  /// Check current location permission status without requesting
  Future<LocationPermission> checkPermission() async {
    try {
      return await Geolocator.checkPermission();
    } catch (e) {
      dev.log('Error checking location permission: $e');
      return LocationPermission.denied;
    }
  }
  
  /// Request location permission
  Future<LocationPermission> requestPermission() async {
    try {
      return await Geolocator.requestPermission();
    } catch (e) {
      dev.log('Error requesting location permission: $e');
      return LocationPermission.denied;
    }
  }
  
  /// Get the current position with error handling
  Future<Position?> getCurrentPosition({
    LocationAccuracy desiredAccuracy = LocationAccuracy.low,
    bool forceLocationUpdate = false,
    Duration timeLimit = const Duration(seconds: 10),
  }) async {
    try {
      // First check if we have a cached position that's recent (within last hour)
      if (!forceLocationUpdate) {
        final cachedPosition = await _getFromLocalCache();
        if (cachedPosition != null) {
          dev.log('Using cached position: ${cachedPosition.latitude}, ${cachedPosition.longitude}');
          return cachedPosition;
        }
      }
      
      // Check if location services are enabled
      final serviceEnabled = await isLocationServiceEnabled();
      if (!serviceEnabled) {
        dev.log('Location services are disabled');
        return null;
      }
      
      // Check permission
      final permission = await checkPermission();
      if (permission == LocationPermission.denied || 
          permission == LocationPermission.deniedForever ||
          permission == LocationPermission.unableToDetermine) {
        dev.log('Location permission not granted: $permission');
        return null;
      }
      
      // Get position
      final position = await Geolocator.getCurrentPosition(
        desiredAccuracy: desiredAccuracy,
        timeLimit: timeLimit,
      );
      
      dev.log('Position obtained: ${position.latitude}, ${position.longitude}');
      
      // Save position to cache
      _saveToLocalCache(position);
      
      // Try to log position to Firebase Storage (for debugging)
      _tryStoreLocationToCloud(position);
      
      return position;
    } catch (e) {
      dev.log('Error getting current position: $e');
      return null;
    }
  }
  
  /// Get the last known position without activating GPS
  Future<Position?> getLastKnownPosition() async {
    try {
      // First check if we have a cached position
      final cachedPosition = await _getFromLocalCache();
      if (cachedPosition != null) {
        dev.log('Using cached position: ${cachedPosition.latitude}, ${cachedPosition.longitude}');
        return cachedPosition;
      }
      
      // Try to get last known position from device
      final position = await Geolocator.getLastKnownPosition();
      if (position != null) {
        dev.log('Last known position: ${position.latitude}, ${position.longitude}');
        _saveToLocalCache(position);
      }
      return position;
    } catch (e) {
      dev.log('Error getting last known position: $e');
      return null;
    }
  }
  
  /// Save position to local cache
  Future<void> _saveToLocalCache(Position position) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_lastLocationCacheKey, jsonEncode(position.toJson()));
      await prefs.setInt(_lastLocationTimestampKey, DateTime.now().millisecondsSinceEpoch);
      dev.log('Position saved to local cache');
    } catch (e) {
      dev.log('Error saving position to cache: $e');
    }
  }
  
  /// Get position from local cache (if it's recent enough)
  Future<Position?> _getFromLocalCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cachedJson = prefs.getString(_lastLocationCacheKey);
      final timestamp = prefs.getInt(_lastLocationTimestampKey) ?? 0;
      
      // Check if cache is valid (not older than 1 hour)
      final cacheAge = DateTime.now().millisecondsSinceEpoch - timestamp;
      if (cachedJson != null && cacheAge < 60 * 60 * 1000) {
        dev.log('Using position from local cache');
        return Position.fromMap(jsonDecode(cachedJson));
      }
      
      dev.log('No valid position in local cache');
      return null;
    } catch (e) {
      dev.log('Error getting position from cache: $e');
      return null;
    }
  }
  
  /// Store location data to Firebase for debugging purposes
  Future<void> _tryStoreLocationToCloud(Position position) async {
    if (_auth.currentUser == null) return;
    
    try {
      final userId = _auth.currentUser!.uid;
      final ref = _storage.ref().child('user_location/$userId/last_location.json');
      final data = {
        'latitude': position.latitude,
        'longitude': position.longitude,
        'timestamp': DateTime.now().toIso8601String(),
        'accuracy': position.accuracy,
        'altitude': position.altitude,
        'heading': position.heading,
        'speed': position.speed,
        'speedAccuracy': position.speedAccuracy,
      };
      
      await ref.putString(jsonEncode(data), format: PutStringFormat.raw);
      dev.log('Location data stored to cloud');
    } catch (e) {
      // Silently fail - this is just for debugging
      dev.log('Error storing location to cloud (non-critical): $e');
    }
  }
  
  /// Calculate distance between two positions in kilometers
  double calculateDistance(double startLatitude, double startLongitude, 
                           double endLatitude, double endLongitude) {
    return Geolocator.distanceBetween(
      startLatitude, startLongitude, endLatitude, endLongitude
    ) / 1000; // Convert meters to kilometers
  }
} 