// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'build_config_service.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$buildConfigServiceHash() =>
    r'66f0b00226e3380526ea785224970b13856160f6';

/// A service that verifies the build configuration is appropriate for the environment.
/// This helps ensure no debug code or sensitive information is deployed to production.
///
/// Copied from [BuildConfigService].
@ProviderFor(BuildConfigService)
final buildConfigServiceProvider =
    AutoDisposeAsyncNotifierProvider<BuildConfigService, void>.internal(
  BuildConfigService.new,
  name: r'buildConfigServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$buildConfigServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$BuildConfigService = AutoDisposeAsyncNotifier<void>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
