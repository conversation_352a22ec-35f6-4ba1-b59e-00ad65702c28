import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Provider for accessing the SharedPreferences instance
final sharedPreferencesProvider = Provider<SharedPreferences>((ref) {
  throw UnimplementedError('Should be overridden in main.dart');
});

/// Provider for the storage service
final storageServiceProvider = Provider<StorageService>((ref) {
  final sharedPreferences = ref.watch(sharedPreferencesProvider);
  return StorageService(sharedPreferences);
}, dependencies: [sharedPreferencesProvider]);

/// Service for accessing local storage via SharedPreferences
class StorageService {
  final SharedPreferences _preferences;

  /// Constructor
  StorageService(this._preferences);

  /// Get a boolean value
  bool getBool(String key, {bool defaultValue = false}) {
    return _preferences.getBool(key) ?? defaultValue;
  }

  /// Set a boolean value
  Future<bool> setBool(String key, bool value) {
    return _preferences.setBool(key, value);
  }

  /// Get a string value
  String? getString(String key) {
    return _preferences.getString(key);
  }

  /// Set a string value
  Future<bool> setString(String key, String value) {
    return _preferences.setString(key, value);
  }

  /// Get an int value
  int? getInt(String key) {
    return _preferences.getInt(key);
  }

  /// Set an int value
  Future<bool> setInt(String key, int value) {
    return _preferences.setInt(key, value);
  }

  /// Get a double value
  double? getDouble(String key) {
    return _preferences.getDouble(key);
  }

  /// Set a double value
  Future<bool> setDouble(String key, double value) {
    return _preferences.setDouble(key, value);
  }

  /// Remove a value
  Future<bool> remove(String key) {
    return _preferences.remove(key);
  }

  /// Clear all values
  Future<bool> clear() {
    return _preferences.clear();
  }
} 