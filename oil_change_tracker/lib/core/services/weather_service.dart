import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:oil_change_tracker/core/models/weather_model.dart';
import 'dart:developer' as dev;
import 'package:geolocator/geolocator.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:oil_change_tracker/core/services/location_service.dart';

final weatherServiceProvider = Provider<WeatherService>((ref) {
  final locationService = ref.watch(locationServiceProvider);
  return WeatherService(locationService);
});

class WeatherService {
  static const String _baseUrl = 'https://api.weatherapi.com/v1';
  static const String _apiKey = '239abdd6aa534017bfc130632252403'; // Original API key
  
  // Firebase refs
  final FirebaseStorage _storage = FirebaseStorage.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  
  // Location service
  final LocationService _locationService;
  
  // Local cache keys
  static const String _lastWeatherCacheKey = 'last_weather_cache';
  static const String _lastWeatherTimestampKey = 'last_weather_timestamp';
  
  WeatherService(this._locationService);

  // Safe parsing method to handle potential errors
  WeatherModel? _safeParseWeather(String jsonStr) {
    try {
      // Ensure proper UTF-8 decoding for Arabic and other non-Latin characters
      final decodedJson = utf8.decode(jsonStr.runes.toList());
      final data = jsonDecode(decodedJson);
      return WeatherModel.fromJson(data);
    } catch (e) {
      // Try direct parsing if special decoding fails
      try {
        final data = jsonDecode(jsonStr);
        return WeatherModel.fromJson(data);
      } catch (secondError) {
        dev.log('Error parsing weather data: $e');
        dev.log('Secondary parsing error: $secondError');
        dev.log('JSON data: $jsonStr');
        dev.log('Stack trace: ${StackTrace.current}');
        return null;
      }
    }
  }

  // Helper method to add proper headers for internationalization
  Map<String, String> _getHeaders() {
    return {
      'Accept': 'application/json',
      'Accept-Charset': 'utf-8',
      'Content-Type': 'application/json; charset=utf-8',
    };
  }

  Future<WeatherModel?> getCurrentWeather(String location, [String language = 'en']) async {
    try {
      dev.log('Fetching weather for location: $location, language: $language');
      final response = await http.get(
        Uri.parse('$_baseUrl/current.json?key=$_apiKey&q=$location&aqi=no&lang=$language'),
        headers: _getHeaders(),
      );

      if (response.statusCode == 200) {
        dev.log('Weather data received successfully');
        final weather = _safeParseWeather(response.body);
        
        // Save to local cache
        if (weather != null) {
          _saveToLocalCache(response.body);
        }
        
        return weather;
      } else {
        dev.log('Error fetching weather: ${response.statusCode}', error: response.body);
        return _getFromLocalCache(); // Try to get from cache on error
      }
    } catch (e) {
      dev.log('Error fetching weather', error: e);
      return _getFromLocalCache(); // Try to get from cache on error
    }
  }

  Future<WeatherModel?> getWeatherByIp([String language = 'en']) async {
    try {
      dev.log('Fetching weather by IP address with language: $language');
      final response = await http.get(
        Uri.parse('$_baseUrl/current.json?key=$_apiKey&q=auto:ip&aqi=no&lang=$language'),
        headers: _getHeaders(),
      );

      if (response.statusCode == 200) {
        dev.log('IP-based weather data received successfully');
        final weather = _safeParseWeather(response.body);
        
        // Save to local cache
        if (weather != null) {
          _saveToLocalCache(response.body);
          _tryStoreCacheToCloud(response.body);
        }
        
        return weather;
      } else {
        dev.log('Error fetching weather by IP: ${response.statusCode}', error: response.body);
        return _getFromLocalCache(); // Try to get from cache on error
      }
    } catch (e) {
      dev.log('Error fetching weather by IP', error: e);
      return _getFromLocalCache(); // Try to get from cache on error
    }
  }
  
  Future<WeatherModel?> getWeatherByLocation([String language = 'en']) async {
    try {
      dev.log('Starting weather by location retrieval process with language: $language');
      
      // Use the location service to get position
      final position = await _locationService.getCurrentPosition();
      
      // If position is null, fall back to IP-based weather
      if (position == null) {
        dev.log('Could not obtain position, falling back to IP');
        return getWeatherByIp(language);
      }
      
      dev.log('Position obtained: ${position.latitude}, ${position.longitude}');

      // Get weather data using the coordinates
      final response = await http.get(
        Uri.parse('$_baseUrl/current.json?key=$_apiKey&q=${position.latitude},${position.longitude}&aqi=no&lang=$language'),
        headers: _getHeaders(),
      );

      if (response.statusCode == 200) {
        dev.log('Location-based weather data received successfully');
        final weather = _safeParseWeather(response.body);
        
        // Save to local cache
        if (weather != null) {
          _saveToLocalCache(response.body);
          _tryStoreCacheToCloud(response.body);
        }
        
        return weather;
      } else {
        dev.log('Error fetching weather by location: ${response.statusCode}', error: response.body);
        dev.log('Falling back to IP-based weather');
        return getWeatherByIp(language);
      }
    } catch (e) {
      dev.log('Error fetching weather by location', error: e.toString());
      dev.log('Stack trace: ${StackTrace.current}');
      dev.log('Falling back to IP-based weather due to error');
      return getWeatherByIp(language);
    }
  }
  
  // Save weather data to local cache
  Future<void> _saveToLocalCache(String weatherJson) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_lastWeatherCacheKey, weatherJson);
      await prefs.setInt(_lastWeatherTimestampKey, DateTime.now().millisecondsSinceEpoch);
      dev.log('Weather data saved to local cache');
    } catch (e) {
      dev.log('Error saving weather to cache: $e');
    }
  }
  
  // Get weather data from local cache
  Future<WeatherModel?> _getFromLocalCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cachedJson = prefs.getString(_lastWeatherCacheKey);
      final timestamp = prefs.getInt(_lastWeatherTimestampKey) ?? 0;
      
      // Check if cache is valid (not older than 6 hours)
      final cacheAge = DateTime.now().millisecondsSinceEpoch - timestamp;
      if (cachedJson != null && cacheAge < 6 * 60 * 60 * 1000) {
        dev.log('Using weather data from local cache');
        return _safeParseWeather(cachedJson);
      }
      
      dev.log('No valid weather data in local cache');
      return null;
    } catch (e) {
      dev.log('Error getting weather from cache: $e');
      return null;
    }
  }
  
  // Try to store location to cloud for debugging
  Future<void> _tryStoreLocationToCloud(Position position) async {
    if (_auth.currentUser == null) return;
    
    try {
      final userId = _auth.currentUser!.uid;
      final ref = _storage.ref().child('user_location/$userId/last_location.json');
      final data = {
        'latitude': position.latitude,
        'longitude': position.longitude,
        'timestamp': DateTime.now().toIso8601String(),
        'accuracy': position.accuracy,
        'altitude': position.altitude,
        'heading': position.heading,
        'speed': position.speed,
        'speedAccuracy': position.speedAccuracy,
      };
      
      await ref.putString(jsonEncode(data), format: PutStringFormat.raw);
      dev.log('Location data stored to cloud');
    } catch (e) {
      // Silently fail - this is just for debugging
      dev.log('Error storing location to cloud (non-critical): $e');
    }
  }
  
  // Try to store weather cache to cloud for debugging
  Future<void> _tryStoreCacheToCloud(String weatherJson) async {
    if (_auth.currentUser == null) return;
    
    try {
      final userId = _auth.currentUser!.uid;
      final ref = _storage.ref().child('weather_cache/$userId/last_weather.json');
      await ref.putString(weatherJson, format: PutStringFormat.raw);
      dev.log('Weather cache stored to cloud');
    } catch (e) {
      // Silently fail - this is just for debugging
      dev.log('Error storing weather cache to cloud (non-critical): $e');
    }
  }
} 