import 'package:firebase_app_check/firebase_app_check.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'dart:math';
import 'dart:developer' as dev;
import 'dart:async';
import 'dart:io' if (dart.library.js) 'dart:html';
import 'package:logger/logger.dart';
import 'package:cloud_firestore/cloud_firestore.dart' if (dart.library.html) 'package:cloud_firestore/cloud_firestore.dart';
import 'package:oil_change_tracker/services/integrity_service.dart';

/// Service that handles Firebase App Check functionality with better debug support
class AppCheckService {
  final _logger = Logger();
  bool _isInitialized = false;
  String? _lastToken;
  AndroidProvider? _installedProvider;
  DateTime? _lastTokenRequest;
  int _consecutiveErrors = 0;
  static const int _maxRetries = 3; // Reduced to avoid hitting rate limits
  static const int _minRequestInterval = 60; // Increased to reduce request frequency
  
  // Debug token shown in logs - USE THIS EXACT TOKEN
  static const String _debugToken = "e7debe73-145b-44ae-a6ad-be0a15c2bc2c";
  
  // Force debug provider in debug mode
  bool _isEnabled = true;
  
  // Backoff strategy to avoid rate limiting
  int _retryDelayMs = 2000; // Increased initial delay
  Timer? _retryTimer;
  
  // Cache related variables
  static const int _tokenCacheLifetimeSeconds = 3600; // Cache valid for 1 hour
  DateTime? _tokenExpiryTime;
  
  // Rate limiting for limited-use tokens
  DateTime? _lastLimitedUseTokenRequest;
  static const int _limitedUseTokenMinInterval = 60; // 60 seconds minimum between requests
  
  // Play Integrity Service for modern attestation
  final IntegrityService _integrityService;
  
  bool get isInitialized => _isInitialized;
  bool get isEnabled => _isEnabled;
  bool get hasValidToken => _lastToken != null;
  
  AppCheckService(this._integrityService);

  Future<void> initialize() async {
    if (_isInitialized) {
      dev.log('AppCheck: Already initialized, skipping initialization');
      return;
    }
    
    try {
      dev.log('AppCheck: Starting initialization');
      
      if (kDebugMode) {
        dev.log('AppCheck: Debug mode detected - using debug provider');
        dev.log('AppCheck: IMPORTANT: Register this debug token in Firebase Console:');
        dev.log('AppCheck: Debug Token: $_debugToken');
        
        try {
          // First explicitly set token auto-refresh
          await FirebaseAppCheck.instance.setTokenAutoRefreshEnabled(true);
          
          // Clear complete app state
          reset();
          
          if (Platform.isAndroid) {
            // On Android, we need a special approach for debug mode
            dev.log('AppCheck: Initializing Android debug provider');
            
            // IMPORTANT: The debug token MUST be registered in Firebase Console
            // for this exact app package or it will not work
            await FirebaseAppCheck.instance.activate(
              androidProvider: AndroidProvider.debug,
            );
            
            // Store what provider we installed
            _installedProvider = AndroidProvider.debug;
          
            // For debug mode, log a reminder about debug token
            dev.log('AppCheck: VERIFY this debug token is registered in Firebase Console:');
            dev.log('AppCheck: $_debugToken');
          } else {
            // For iOS, use the appropriate provider
            await FirebaseAppCheck.instance.activate(
              appleProvider: AppleProvider.appAttestWithDeviceCheckFallback,
            );
          }
          
          // Now enabled and initialized
          _isEnabled = true;
          _isInitialized = true;
          dev.log('AppCheck: Initialization complete');
        } catch (e) {
          dev.log('AppCheck: Error initializing debug provider: $e');
          // Continue with app execution - debug token will be retried later
          _isInitialized = true; // Still mark as initialized
        }
      } else {
        dev.log('AppCheck: Production mode - using Play Integrity provider');
        
        try {
          // In production, use Play Integrity
          await FirebaseAppCheck.instance.activate(
            androidProvider: AndroidProvider.playIntegrity,
          );
          
          // Store what provider we installed
          _installedProvider = AndroidProvider.playIntegrity;
          _isInitialized = true;
          
          // Test the Play Integrity API
          await _testPlayIntegrity();
        } catch (e) {
          dev.log('AppCheck: Error initializing Play Integrity provider: $e');
          _isEnabled = false; // Disable for now to prevent crashes
          _isInitialized = true; // Still mark as initialized
        }
      }
      
    } catch (e) {
      dev.log('AppCheck: Error during initialization: $e');
      _isEnabled = false; // Disable to prevent crashes
      _isInitialized = true; // Still mark as initialized to prevent retry loops
    }
  }
  
  // Test the Play Integrity API implementation
  Future<void> _testPlayIntegrity() async {
    try {
      dev.log('AppCheck: Testing Play Integrity API');
      final token = await _integrityService.requestIntegrityToken();
      if (token != null && token.isNotEmpty) {
        dev.log('AppCheck: Play Integrity API test successful');
      } else {
        dev.log('AppCheck: Play Integrity API test failed - null or empty token');
      }
    } catch (e) {
      dev.log('AppCheck: Play Integrity API test error: $e');
    }
  }
  
  // Get token only when needed, with proper rate limiting and caching
  Future<String?> getToken({bool forceRefresh = false}) async {
    // If not initialized or disabled, don't try to get a token
    if (!_isInitialized || !_isEnabled) {
      dev.log('AppCheck: getToken() - Service not initialized or disabled');
      return null;
    }
    
    final now = DateTime.now();
    
    // If forceRefresh is true, invalidate the cached token
    if (forceRefresh) {
      dev.log('AppCheck: Force refresh requested, invalidating cached token');
      _lastToken = null;
      _tokenExpiryTime = null;
    }
    
    // 1. ENHANCED CACHING: Use cached token if it's still valid and not forced refresh
    if (!forceRefresh && 
        _lastToken != null && 
        _tokenExpiryTime != null &&
        now.isBefore(_tokenExpiryTime!)) {
      final remainingMinutes = _tokenExpiryTime!.difference(now).inMinutes;
      dev.log('AppCheck: Using cached token (valid for $remainingMinutes more minutes)');
      
      // Log partial token for verification
      final maskedToken = _lastToken!.length > 10
          ? '${_lastToken!.substring(0, 10)}...[${_lastToken!.length} chars]'
          : _lastToken;
      dev.log('AppCheck: Cached token: $maskedToken');
      
      return _lastToken;
    }
    
    // 2. IMPROVED RATE LIMITING: Check if we've made too many requests recently
    if (_lastTokenRequest != null &&
        now.difference(_lastTokenRequest!).inSeconds < _minRequestInterval) {
      final waitTime = _minRequestInterval - now.difference(_lastTokenRequest!).inSeconds;
      dev.log('AppCheck: Rate limiting - must wait $waitTime more seconds before requesting new token');
      
      // If forced refresh, try anyway; otherwise use cached token
      if (!forceRefresh) {
        dev.log('AppCheck: Using cached token due to rate limiting');
      return _lastToken;
      }
      dev.log('AppCheck: Force refresh requested despite rate limit - proceeding anyway');
    }
    
    // Track this request time
    _lastTokenRequest = now;
    
    try {
      // 3. BETTER ERROR HANDLING: Maximum retries exceeded
      if (_consecutiveErrors >= _maxRetries && !forceRefresh) {
        dev.log('AppCheck: Too many consecutive errors ($_consecutiveErrors), using placeholder token');
        return _lastToken ?? _createPlaceholderToken();
      }
      
      dev.log('AppCheck: Requesting fresh token from Firebase (debug token: $_debugToken)');
      
      // 4. IMPROVED TIMEOUT: Get a fresh token with timeout to avoid hanging
      final token = await FirebaseAppCheck.instance.getToken()
          .timeout(const Duration(seconds: 15), onTimeout: () {
        dev.log('AppCheck: Token request timed out after 15 seconds');
        return _lastToken ?? _createPlaceholderToken();
      });
      
      if (token != null && token.isNotEmpty) {
        _lastToken = token;
        // Set token expiry time - cache for 1 hour
        _tokenExpiryTime = now.add(Duration(seconds: _tokenCacheLifetimeSeconds));
        _consecutiveErrors = 0; // Reset error counter on success
        _retryDelayMs = 2000; // Reset backoff delay
        
        // Log partial token for debugging (security)
        final maskedToken = token.length > 10
            ? '${token.substring(0, 10)}...[${token.length} chars]'
            : token;
        dev.log('AppCheck: ✅ Successfully obtained token: $maskedToken (valid until ${_tokenExpiryTime!.toIso8601String()})');
        return _lastToken;
      } else {
        dev.log('AppCheck: ❌ Received null or empty token from Firebase');
        _consecutiveErrors++;
        
        // Add diagnostic info about the debug token
        if (kDebugMode) {
          dev.log('AppCheck: Debug token being used: $_debugToken');
          dev.log('AppCheck: Make sure this token is registered in Firebase Console');
        }
        
        return _lastToken ?? _createPlaceholderToken();
      }
    } catch (e) {
      dev.log('AppCheck: ❌ Error getting token: $e');
      _consecutiveErrors++;
      
      // More detailed error analysis
      _analyzeError(e);
      
      // Handle "too many attempts" by using placeholder token
      if (e.toString().contains('Too many attempts') || 
          e.toString().contains('QUOTA_EXCEEDED')) {
        dev.log('AppCheck: Rate limit hit, using placeholder token');
        // Double the retry delay on rate limits
        _retryDelayMs = min(_retryDelayMs * 2, 60000); // Max 1 minute
      }
      
      // Return last token or placeholder
      return _lastToken ?? _createPlaceholderToken();
    }
  }
  
  /// Get a limited-use token specifically for Cloud Functions with enforceAppCheck
  /// This type of token is consumed by the function and can only be used once
  /// Implements aggressive rate limiting to prevent "Too many attempts" errors
  Future<String?> getLimitedUseToken() async {
    // If not initialized or disabled, don't try to get a token
    if (!_isInitialized || !_isEnabled) {
      dev.log('AppCheck: getLimitedUseToken() - Service not initialized or disabled');
      return null;
    }
    
    final now = DateTime.now();
    
    // CRITICAL: Enforce very strict rate limiting for limited-use tokens
    if (_lastLimitedUseTokenRequest != null &&
        now.difference(_lastLimitedUseTokenRequest!).inSeconds < _limitedUseTokenMinInterval) {
      final waitTime = _limitedUseTokenMinInterval - now.difference(_lastLimitedUseTokenRequest!).inSeconds;
      dev.log('AppCheck: ⚠️ STRICT RATE LIMITING for limited-use token! Must wait $waitTime more seconds');
      
      // IMPROVED: Instead of failing, try to use a regular token if we have a valid cached one
      if (_lastToken != null && _tokenExpiryTime != null && now.isBefore(_tokenExpiryTime!)) {
        dev.log('AppCheck: Using cached regular token instead of limited-use token');
        
        // Log diagnostic info about the token we're using
        final maskedToken = _lastToken!.length > 10
            ? '${_lastToken!.substring(0, 10)}...[${_lastToken!.length} chars]'
            : _lastToken;
        dev.log('AppCheck: Cached token: $maskedToken (valid until ${_tokenExpiryTime!.toIso8601String()})');
        
        return _lastToken;
      }
      
      // If no valid cached token, return a placeholder rather than triggering rate limits
      dev.log('AppCheck: Using placeholder token to avoid rate limiting');
      return _createPlaceholderToken();
    }
    
    // Track this limited-use token request time
    _lastLimitedUseTokenRequest = now;
    
    try {
      dev.log('AppCheck: Getting limited-use token for secure function call');
      
      // ENHANCED: Use cached token if available and we've had errors before
      if (_consecutiveErrors > 0 && _lastToken != null) {
        dev.log('AppCheck: Previous errors detected, using cached token instead of limited-use');
        return _lastToken;
      }
      
      // Get a fresh token with increased timeout for slower connections
      final token = await FirebaseAppCheck.instance.getToken()
          .timeout(const Duration(seconds: 15), onTimeout: () {
        dev.log('AppCheck: Limited-use token request timed out after 15 seconds');
        return _lastToken ?? _createPlaceholderToken();
      });
      
      if (token != null && token.isNotEmpty) {
        dev.log('AppCheck: ✅ Successfully obtained limited-use token');
        
        // IMPROVED: Cache this token temporarily as a last resort for future requests
        // Even though it's for one-time use, we can use it as fallback if we hit rate limits
        _lastToken = token;
        _tokenExpiryTime = now.add(const Duration(seconds: 300)); // Short 5 min validity
        
        // Log partial token for debugging (security)
        final maskedToken = token.length > 10
            ? '${token.substring(0, 10)}...[${token.length} chars]'
            : token;
        dev.log('AppCheck: Limited-use token: $maskedToken');
        
        return token;
      } else {
        dev.log('AppCheck: ❌ Failed to get limited-use token');
        _consecutiveErrors++;
        
        // ENHANCED: Fall back to a cached token if available
        if (_lastToken != null) {
          dev.log('AppCheck: Using cached token as fallback');
          return _lastToken;
        }
        
        return _createPlaceholderToken();
      }
    } catch (e) {
      dev.log('AppCheck: ❌ Error getting limited-use token: $e');
      _consecutiveErrors++;
      
      // More detailed error analysis
      _analyzeError(e);
      
      // IMPROVED: Better handling for rate limiting with exponential backoff
      if (e.toString().contains('Too many attempts') || 
          e.toString().contains('QUOTA_EXCEEDED')) {
        dev.log('AppCheck: Rate limit hit for limited-use token');
        
        // Increment the backoff delay with exponential factor
        _retryDelayMs = min(_retryDelayMs * 2, 120000); // Max 2 minutes
        dev.log('AppCheck: Backoff delay increased to ${_retryDelayMs}ms');
        
        // Try to use a cached token if available
        if (_lastToken != null) {
          dev.log('AppCheck: Using cached token due to rate limiting');
          return _lastToken;
        }
        
        // Last resort placeholder
        return _createPlaceholderToken();
      }
      
      // For other errors, use cached token if available
      if (_lastToken != null) {
        dev.log('AppCheck: Using cached token due to error');
        return _lastToken;
      }
      
      // Last resort placeholder
      return _createPlaceholderToken();
    }
  }
  
  // Create a temporary placeholder token when no token is available
  // This allows operations to proceed even without a valid token
  String _createPlaceholderToken() {
    return 'placeholder-token-${DateTime.now().millisecondsSinceEpoch}';
  }
  
  // Cancel any pending retries
  void cancelRetries() {
    if (_retryTimer != null && _retryTimer!.isActive) {
      _retryTimer!.cancel();
      _retryTimer = null;
    }
  }
  
  // Reset the service state
  void reset() {
    dev.log('AppCheck: Resetting service state');
    _lastToken = null;
    _consecutiveErrors = 0;
    _retryDelayMs = 2000;
    cancelRetries();
    
    // Reset the last token request time
    _lastTokenRequest = null;
  }
  
  // Force a debug token when needed, even in production
  String getDebugToken() {
    // This method generates a temporary debug token for development
    // You must register this debug token in your Firebase Console
    return _debugToken;
  }
  
  // Add a verification method to check if debug token is registered
  Future<bool> verifyDebugToken() async {
    if (kDebugMode) {
      dev.log('AppCheck: Verifying debug token registration');
      
      // Run diagnostics first
      await _runDiagnostics();
      
      try {
        // First reset any previous state
        reset();
        
        // Add slight delay to avoid rate limiting
        await Future.delayed(const Duration(seconds: 1));
        
        dev.log('AppCheck: Ensuring token auto-refresh is enabled');
        await FirebaseAppCheck.instance.setTokenAutoRefreshEnabled(true);
        
        dev.log('AppCheck: Debug token being verified: $_debugToken');
        
        // Force debug provider activation
        dev.log('AppCheck: Activating debug provider');
        await FirebaseAppCheck.instance.activate(
          androidProvider: AndroidProvider.debug,
        );
        
        // Add another slight delay
        await Future.delayed(const Duration(seconds: 1));
        
        dev.log('AppCheck: Attempting to get token with timeout protection');
        // Try to get a token and see if it succeeds, with timeout
        final token = await FirebaseAppCheck.instance.getToken()
            .timeout(const Duration(seconds: 15), onTimeout: () {
          dev.log('AppCheck: Token request timed out');
          return null;
        });
        
        if (token != null && token.isNotEmpty) {
          // Mask token for security but log part of it for verification
          final maskedToken = '${token.substring(0, min(10, token.length))}...';
          dev.log('AppCheck: Debug token is VALID ✅ - Successfully retrieved token: $maskedToken');
          return true;
        } else {
          dev.log('AppCheck: Debug token is INVALID ❌ - No token returned');
          
          // Check if we need to register the token
          dev.log('AppCheck: IMPORTANT - Make sure this exact token is registered in Firebase Console:');
          dev.log('AppCheck: $_debugToken');
          dev.log('AppCheck: Go to Firebase Console > Project Settings > App Check > Manage debug tokens');
          
          return false;
        }
      } catch (e) {
        dev.log('AppCheck: Debug token is INVALID ❌ - Error: $e');
        
        // More detailed error analysis
        _analyzeError(e);
        
        if (e.toString().contains('Too many attempts')) {
          dev.log('AppCheck: Rate limiting detected. Wait a few minutes before trying again.');
          
          // Try again with a longer delay as a last attempt
          try {
            dev.log('AppCheck: Making one final attempt after delay...');
            await Future.delayed(const Duration(seconds: 5));
            
            final token = await FirebaseAppCheck.instance.getToken();
            if (token != null) {
              dev.log('AppCheck: Success on final attempt!');
              return true;
            }
          } catch (_) {
            // Ignore error on final attempt
          }
        }
        
        return false;
      }
    } else {
      dev.log('AppCheck: Not in debug mode, skipping debug token verification');
      return false;
    }
  }
  
  /// Runs a comprehensive diagnostic check to help identify App Check issues
  Future<void> _runDiagnostics() async {
    try {
      dev.log('-------- APP CHECK DIAGNOSTICS --------');
      dev.log('Debug token: $_debugToken');
      dev.log('Is initialized: $_isInitialized');
      dev.log('Is enabled: $_isEnabled');
      dev.log('Has valid token: $hasValidToken');
      dev.log('Provider installed: ${_installedProvider ?? 'None'}');
      dev.log('Is debug mode: ${kDebugMode}');
      
      // Check SDK versions
      dev.log('Flutter version: ${_getFlutterVersion()}');
      
      // Check token refresh state
      try {
        // Check if token auto-refresh is enabled via our settings
        dev.log('Token auto-refresh setting: true (default)');
      } catch (e) {
        dev.log('Failed to check token auto-refresh state: $e');
      }
      
      // Check if we can get a token directly with debug provider
      try {
        dev.log('Trying to activate debug provider directly...');
        await FirebaseAppCheck.instance.activate(
          androidProvider: AndroidProvider.debug,
        );
        dev.log('Debug provider activation succeeded');
      } catch (e) {
        dev.log('Debug provider activation failed: $e');
      }
      
      dev.log('-------------------------------------');
    } catch (e) {
      dev.log('Error during diagnostics: $e');
    }
  }
  
  /// Analyzes error messages to provide more helpful debugging information
  void _analyzeError(dynamic error) {
    final errorString = error.toString();
    dev.log('AppCheck: DETAILED ERROR ANALYSIS');
    dev.log('AppCheck: Original error: $errorString');
    
    if (errorString.contains('App attestation failed')) {
      dev.log('AppCheck: DIAGNOSIS: "App attestation failed" indicates your debug token is not properly registered');
      dev.log('AppCheck: ACTION NEEDED: Register token $_debugToken in Firebase Console > Project Settings > App Check');
      dev.log('AppCheck: DEBUG TOKEN MISMATCH: Make sure the token in Firebase Console EXACTLY matches your app code');
      dev.log('AppCheck: DEBUGGING STEP: Delete ALL debug tokens in Firebase Console and create a new one with the exact value');
    } else if (errorString.contains('Too many attempts')) {
      dev.log('AppCheck: DIAGNOSIS: "Too many attempts" indicates you\'re being rate-limited by Firebase');
      dev.log('AppCheck: ACTION NEEDED: Wait 5-15 minutes before trying again to reset the rate limit');
      dev.log('AppCheck: WORKAROUND: If this is for testing, set App Check enforcement to "Optional" for services');
    } else if (errorString.contains('network')) {
      dev.log('AppCheck: DIAGNOSIS: Network error detected. Check your internet connection');
      dev.log('AppCheck: ACTION NEEDED: Ensure your device has a stable internet connection');
    } else if (errorString.contains('permission')) {
      dev.log('AppCheck: DIAGNOSIS: Permission issue detected');
      dev.log('AppCheck: ACTION NEEDED: Check your app\'s manifest for proper permissions');
    } else if (errorString.contains('configuration')) {
      dev.log('AppCheck: DIAGNOSIS: Firebase configuration issue detected');
      dev.log('AppCheck: ACTION NEEDED: Check your google-services.json file and Firebase project configuration');
    } else if (errorString.contains('unconfigured app')) {
      dev.log('AppCheck: DIAGNOSIS: App not properly configured in Firebase Console');
      dev.log('AppCheck: ACTION NEEDED: Verify the app is registered in Firebase Console');
      dev.log('AppCheck: ACTION NEEDED: Check that the package name matches exactly');
    } else if (errorString.contains('appCheck')) {
      dev.log('AppCheck: DIAGNOSIS: App Check specific error');
      dev.log('AppCheck: ACTION NEEDED: Make sure App Check is enabled in Firebase Console');
      dev.log('AppCheck: ACTION NEEDED: Check that debug token is properly registered for your app');
    } else {
      dev.log('AppCheck: DIAGNOSIS: Unknown error. Check the Firebase Console for more details.');
      dev.log('AppCheck: ACTION NEEDED: Run app with full debug logging enabled for more information');
    }
    
    dev.log('AppCheck: END ERROR ANALYSIS');
  }
  
  /// Helper method to get Flutter version info for diagnostics
  String _getFlutterVersion() {
    try {
      if (kIsWeb) {
        return 'Web | ${kDebugMode ? "Debug" : "Release"}';
      } else {
        try {
          return '${Platform.operatingSystem} | ${kDebugMode ? "Debug" : "Release"}';
        } catch (e) {
          return 'Native | ${kDebugMode ? "Debug" : "Release"}';
        }
      }
    } catch (e) {
      return 'Unknown | ${kDebugMode ? "Debug" : "Release"}';
    }
  }
  
  // Add dispose method to fix the error
  void dispose() {
    dev.log('AppCheck: Disposing service');
    cancelRetries();
  }

  /// Explicitly forces an update with a fresh debug token initialization
  /// This can help when token registration has changed on Firebase Console
  Future<bool> forceUpdateDebugToken() async {
    if (kDebugMode) {
      dev.log('AppCheck: Force updating debug token registration');
      
      // Completely reset state
      reset();
      
      // First deactivate if possible to clear any cached settings
      try {
        await FirebaseAppCheck.instance.setTokenAutoRefreshEnabled(false);
        dev.log('AppCheck: Disabled auto-refresh');
        
        // Add a delay to ensure changes take effect
        await Future.delayed(const Duration(seconds: 2));
      } catch (e) {
        dev.log('AppCheck: Error disabling token refresh: $e');
      }
      
      // Log the debug token we're using
      dev.log('AppCheck: Using debug token: $_debugToken');
      
      try {
        // Force clear any cached state by setting a fresh activation
        dev.log('AppCheck: Forcing new debug provider activation');
        await FirebaseAppCheck.instance.activate(
          androidProvider: AndroidProvider.debug,
        );
        
        // Re-enable auto refresh
        await FirebaseAppCheck.instance.setTokenAutoRefreshEnabled(true);
        
        // Add a delay to let activation settle
        await Future.delayed(const Duration(seconds: 2));
        
        // Try to get a fresh token
        dev.log('AppCheck: Attempting to get a fresh token after force update');
        final token = await FirebaseAppCheck.instance.getToken();
        
        if (token != null && token.isNotEmpty) {
          final maskedToken = '${token.substring(0, min(10, token.length))}...';
          dev.log('AppCheck: Force update SUCCEEDED ✅ - Got token: $maskedToken');
          return true;
        } else {
          dev.log('AppCheck: Force update FAILED ❌ - No token returned');
          return false;
        }
      } catch (e) {
        dev.log('AppCheck: Force update FAILED ❌ - Error: $e');
        _analyzeError(e);
        return false;
      }
    } else {
      dev.log('AppCheck: Not in debug mode, skipping force update');
      return false;
    }
  }

  Future<bool> _tryAllPossibleConfigurations() async {
    // Attempt to test all possible configurations to help diagnose issues
    try {
      dev.log('AppCheck: DIAGNOSTIC - Trying all possible debug token configurations');
      
      bool anySuccess = false;
      
      // Method 1: Standard activation with debug provider
      try {
        dev.log('AppCheck: [Test 1] Standard debug activation...');
        await FirebaseAppCheck.instance.activate(
          androidProvider: AndroidProvider.debug,
        );
        await Future.delayed(const Duration(seconds: 1));
        
        final token1 = await FirebaseAppCheck.instance.getToken()
            .timeout(const Duration(seconds: 5), onTimeout: () => null);
        
        if (token1 != null) {
          dev.log('AppCheck: [Test 1] ✅ SUCCESS! Got token');
          anySuccess = true;
        } else {
          dev.log('AppCheck: [Test 1] ❌ FAILED - No token returned');
        }
      } catch (e) {
        dev.log('AppCheck: [Test 1] ❌ ERROR: $e');
      }
      
      // Method 2: Try without auto-refresh first
      try {
        dev.log('AppCheck: [Test 2] Disabling auto-refresh first...');
        await FirebaseAppCheck.instance.setTokenAutoRefreshEnabled(false);
        await Future.delayed(const Duration(seconds: 1));
        
        await FirebaseAppCheck.instance.activate(
          androidProvider: AndroidProvider.debug,
        );
        
        await Future.delayed(const Duration(seconds: 1));
        await FirebaseAppCheck.instance.setTokenAutoRefreshEnabled(true);
        
        final token2 = await FirebaseAppCheck.instance.getToken()
            .timeout(const Duration(seconds: 5), onTimeout: () => null);
        
        if (token2 != null) {
          dev.log('AppCheck: [Test 2] ✅ SUCCESS! Got token');
          anySuccess = true;
        } else {
          dev.log('AppCheck: [Test 2] ❌ FAILED - No token returned');
        }
      } catch (e) {
        dev.log('AppCheck: [Test 2] ❌ ERROR: $e');
      }
      
      // Method 3: Last resort - try a longer delay
      try {
        dev.log('AppCheck: [Test 3] Using longer delay between operations...');
        await FirebaseAppCheck.instance.setTokenAutoRefreshEnabled(false);
        await Future.delayed(const Duration(seconds: 2));
        
        await FirebaseAppCheck.instance.activate(
          androidProvider: AndroidProvider.debug,
        );
        
        await Future.delayed(const Duration(seconds: 3));
        await FirebaseAppCheck.instance.setTokenAutoRefreshEnabled(true);
        
        await Future.delayed(const Duration(seconds: 3));
        
        final token3 = await FirebaseAppCheck.instance.getToken()
            .timeout(const Duration(seconds: 5), onTimeout: () => null);
        
        if (token3 != null) {
          dev.log('AppCheck: [Test 3] ✅ SUCCESS! Got token');
          anySuccess = true;
        } else {
          dev.log('AppCheck: [Test 3] ❌ FAILED - No token returned');
        }
      } catch (e) {
        dev.log('AppCheck: [Test 3] ❌ ERROR: $e');
      }
      
      // Final check - did any method work?
      if (anySuccess) {
        dev.log('AppCheck: DIAGNOSTIC COMPLETE - At least one configuration worked!');
        return true;
      } else {
        dev.log('AppCheck: DIAGNOSTIC COMPLETE - All configurations failed');
        dev.log('AppCheck: RECOMMENDATION: Double-check the debug token in Firebase Console');
        dev.log('AppCheck: The debug token should be: $_debugToken');
        return false;
      }
      
    } catch (e) {
      dev.log('AppCheck: Error during configuration tests: $e');
      return false;
    }
  }
  
  /// Add a comprehensive verification method to try all possible configurations
  Future<bool> verifyDebugTokenComprehensive() async {
    if (kDebugMode) {
      dev.log('======== APP CHECK COMPREHENSIVE VERIFICATION ========');
      dev.log('AppCheck: Running comprehensive debug token verification');
      dev.log('AppCheck: Debug token being verified: $_debugToken');
      
      // Run diagnostics first
      await _runDiagnostics();
      
      // First try normal verification
      try {
        dev.log('AppCheck: Attempting standard verification method first...');
        final normalVerificationResult = await verifyDebugToken();
        if (normalVerificationResult) {
          dev.log('AppCheck: ✅ Standard verification PASSED! Token is valid.');
          return true;
        }
        
        // If normal verification failed, try all possible configurations
        dev.log('AppCheck: ❌ Standard verification FAILED! Trying all possible configurations...');
        final allConfigsResult = await _tryAllPossibleConfigurations();
        
        if (allConfigsResult) {
          dev.log('AppCheck: ✅ Alternative configuration SUCCEEDED!');
        } else {
          dev.log('AppCheck: ❌ All verification methods FAILED!');
          dev.log('AppCheck: Make sure debug token in Firebase Console matches EXACTLY: $_debugToken');
        }
        
        return allConfigsResult;
        
      } catch (e) {
        dev.log('AppCheck: Error during comprehensive verification: $e');
        _analyzeError(e);
        
        // As a last resort, try all configurations
        dev.log('AppCheck: Trying all configurations as last resort...');
        final lastResortResult = await _tryAllPossibleConfigurations();
        
        if (lastResortResult) {
          dev.log('AppCheck: ✅ Last resort verification SUCCEEDED!');
        } else {
          dev.log('AppCheck: ❌ Last resort verification FAILED!');
        }
        
        return lastResortResult;
      } finally {
        dev.log('====================================================');
      }
    } else {
      dev.log('AppCheck: Not in debug mode, skipping comprehensive verification');
      return false;
    }
  }

  /// Performs a direct test against Firebase backend to verify if the App Check token is actually working
  Future<bool> testTokenWithFirebase() async {
    try {
      dev.log('AppCheck: Testing token validity with Firebase backend');
      
      // First get a fresh token
      final token = await getToken(forceRefresh: true);
      
      if (token == null) {
        dev.log('AppCheck: ❌ Failed to get token for testing');
        return false;
      }
      
      // Log that we're testing
      dev.log('AppCheck: Obtained token for testing with Firebase');
      
      // Now try to access a simple document in Firestore to test if token works
      try {
        // Create a special test collection for App Check verification
        final testRef = FirebaseFirestore.instance.collection('_app_check_tests').doc('test');
        
        // Try to read the document with timeout
        await testRef.get().timeout(
          const Duration(seconds: 10),
          onTimeout: () => throw TimeoutException('Firestore request timed out'),
        );
        
        dev.log('AppCheck: ✅ TEST PASSED - Successfully accessed Firestore with App Check token');
        return true;
      } catch (e) {
        // Check if the error is related to App Check
        if (e.toString().contains('permission-denied') || 
            e.toString().contains('app-check-token-invalid') ||
            e.toString().contains('PERMISSION_DENIED')) {
          dev.log('AppCheck: ❌ TEST FAILED - Firebase rejected the token: $e');
          
          // Provide detailed error analysis
          dev.log('AppCheck: This usually means the debug token is not properly registered');
          dev.log('AppCheck: Current debug token: $_debugToken');
          dev.log('AppCheck: ACTION NEEDED: Register this exact token in Firebase Console');
          
          return false;
        } else {
          // Other Firestore error not related to App Check
          dev.log('AppCheck: ⚠️ TEST INCONCLUSIVE - Error accessing Firestore, but not App Check related: $e');
          
          // If we can access Firestore at all, the token is likely valid
          // even if there are other permission issues
          return true;
        }
      }
    } catch (e) {
      dev.log('AppCheck: ❌ Error testing token with Firebase: $e');
      return false;
    }
  }
}

/// Provider for the AppCheckService 
final appCheckServiceProvider = Provider<AppCheckService>((ref) {
  final integrityService = ref.watch(integrityServiceProvider);
  final service = AppCheckService(integrityService);
  ref.onDispose(() {
    service.dispose();
  });
  return service;
});

/// Provider to expose App Check initialization state
final appCheckInitializationProvider = FutureProvider<bool>((ref) async {
  final service = ref.watch(appCheckServiceProvider);
  await service.initialize();
  return service.isInitialized;
});

/// Provider to expose App Check token
final appCheckTokenProvider = FutureProvider<String?>((ref) async {
  final service = ref.watch(appCheckServiceProvider);
  
  // First make sure the service is initialized
  if (!service.isInitialized) {
    await service.initialize();
  }
  
  // Then get a token
  return service.getToken();
});

/// Provider to get a limited-use token for secure functions
final limitedUseTokenProvider = FutureProvider<String?>((ref) async {
  final service = ref.watch(appCheckServiceProvider);
  
  // First make sure the service is initialized
  if (!service.isInitialized) {
    await service.initialize();
  }
  
  // Get a limited-use token
  return service.getLimitedUseToken();
});

/// Called when app is disposing to clean up
void dispose(AppCheckService service) {
  service.dispose();
}

/// Force refresh the App Check token using more aggressive approach
Future<String?> forceRefreshToken(AppCheckService service) async {
  try {
    if (!service.isInitialized) {
      await service.initialize();
    }
    
    // First reset the service state to clear cached tokens
    service.reset();
    
    // Try a delay between operations
    await Future.delayed(const Duration(seconds: 1));
    
    // Attempt #1: Get token normally from Firebase App Check
    try {
      // Get a fresh token - Firebase App Check instance doesn't have forceRefresh parameter
      final token = await FirebaseAppCheck.instance.getToken();
      
      // Safely handle the token which might be null
      if (token != null && token.length > 10) {
        dev.log('Successfully refreshed App Check token: ${token.substring(0, 10)}...');
        return token;
      } else if (token != null) {
        dev.log('Successfully refreshed App Check token (short token): $token');
        return token;
      }
      
      // If we've reached here, the token is null - fall through to next attempt
      dev.log('Received null token on first attempt, trying alternative approach');
    } catch (firstError) {
      dev.log('Error during first token refresh attempt: $firstError');
      // Continue to fallback
    }
    
    // Attempt #2: Try with a delay and different approach
    await Future.delayed(const Duration(seconds: 2));
    
    try {
      // Try to activate App Check again with different provider (may help in some cases)
      if (kDebugMode) {
        await FirebaseAppCheck.instance.activate(
          androidProvider: AndroidProvider.debug,
        );
      } else {
        await FirebaseAppCheck.instance.activate(
          androidProvider: AndroidProvider.playIntegrity,
        );
      }
      
      // Get token after reactivation
      final token = await FirebaseAppCheck.instance.getToken();
      
      if (token != null) {
        dev.log('Successfully refreshed App Check token on second attempt');
        return token;
      }
    } catch (secondError) {
      dev.log('Error during second token refresh attempt: $secondError');
      // Continue to fallback
    }
    
    // Attempt #3: Use a debug token as a last resort in debug mode
    if (kDebugMode) {
      dev.log('Using debug token as fallback in debug mode');
      return service.getDebugToken();
    }
    
    // If all attempts failed, use placeholder
    dev.log('All token refresh attempts failed, returning placeholder');
    return "placeholder-emergency-token-${DateTime.now().millisecondsSinceEpoch}";
  } catch (e) {
    dev.log('Critical error refreshing App Check token: $e');
    return null;
  }
} 