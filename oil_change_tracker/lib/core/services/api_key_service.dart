import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'dart:developer' as dev;
import 'package:flutter/foundation.dart';

/// Provider for the API key service
final apiKeyServiceProvider = Provider<ApiKeyService>((ref) {
  return ApiKeyService(FirebaseRemoteConfig.instance);
});

/// Service to securely manage API keys
class ApiKeyService {
  final FirebaseRemoteConfig _remoteConfig;

  ApiKeyService(this._remoteConfig);

  /// Initialize the service and fetch configuration
  Future<void> initialize() async {
    try {
      dev.log('ApiKeyService: Starting initialization...');

      // Set default values in case fetching fails
      await _remoteConfig.setDefaults({
        'google_speech_service_account': '',
        'google_speech_api_key': '',
        'openai_api_key': '', // Add OpenAI API key for AI voice processing
        'openrouter_api_key':
            '', // Add OpenRouter API key for DeepSeek via OpenRouter
      });
      dev.log('ApiKeyService: Defaults set');

      // Configure fetch settings with more aggressive settings for development
      await _remoteConfig.setConfigSettings(RemoteConfigSettings(
        fetchTimeout: const Duration(minutes: 1),
        minimumFetchInterval:
            Duration.zero, // Force immediate fetch for testing
      ));
      dev.log('ApiKeyService: Config settings applied');

      // Try to fetch and activate
      bool fetchResult = await _remoteConfig.fetchAndActivate();
      dev.log('ApiKeyService: First fetch and activate result: $fetchResult');

      // If first fetch failed, try to fetch without activate, then activate
      if (!fetchResult) {
        dev.log(
            'ApiKeyService: First fetch failed, trying alternative approach...');

        try {
          await _remoteConfig.fetch();
          dev.log('ApiKeyService: Fetch completed, now activating...');

          final activateResult = await _remoteConfig.activate();
          dev.log('ApiKeyService: Activate result: $activateResult');
        } catch (e) {
          dev.log('ApiKeyService: Alternative fetch/activate failed: $e');
        }
      }

      if (!kReleaseMode) {
        // Development-only verbose logging
        final allKeys = _remoteConfig.getAll();
        dev.log(
            'ApiKeyService: Available Remote Config keys: ${allKeys.keys.toList()}');

        // Check each key's value length (no full value print)
        for (String key in allKeys.keys) {
          final value = _remoteConfig.getString(key);
          dev.log('ApiKeyService: Key "$key" length: ${value.length}');
        }
      }

      // Check API key first (preferred method)
      final apiKey = _remoteConfig.getString('google_speech_api_key');
      dev.log('ApiKeyService: API key length: ${apiKey.length} characters');

      if (apiKey.isNotEmpty) {
        dev.log('ApiKeyService: Google Speech API key found successfully');
      } else {
        // Fallback to service account
        final serviceAccount =
            _remoteConfig.getString('google_speech_service_account');
        dev.log(
            'ApiKeyService: Service account length: ${serviceAccount.length} characters');

        if (serviceAccount.isNotEmpty) {
          dev.log('ApiKeyService: Service account JSON found successfully');
        } else {
          dev.log(
              'ApiKeyService: WARNING - Neither API key nor service account found!');
          dev.log(
              'ApiKeyService: Please set either google_speech_api_key or google_speech_service_account in Firebase Remote Config');
        }
      }

      dev.log('ApiKeyService: Initialization completed successfully');
    } catch (e) {
      dev.log('ApiKeyService: Error initializing: $e');
      rethrow;
    }
  }

  /// Get the Google Speech API key
  String getGoogleSpeechApiKey() {
    try {
      final apiKey = _remoteConfig.getString('google_speech_api_key');
      dev.log('ApiKeyService: Retrieved API key, length: ${apiKey.length}');
      return apiKey;
    } catch (e) {
      dev.log('ApiKeyService: Error getting Google Speech API key: $e');
      return '';
    }
  }

  /// Get the Google Speech service account JSON (fallback method)
  String getGoogleSpeechServiceAccount() {
    try {
      final serviceAccount =
          _remoteConfig.getString('google_speech_service_account');
      dev.log(
          'ApiKeyService: Retrieved service account, length: ${serviceAccount.length}');
      return serviceAccount;
    } catch (e) {
      dev.log('ApiKeyService: Error getting Google Speech service account: $e');
      return '';
    }
  }

  /// Get OpenAI API key for AI voice processing
  String getOpenAIApiKey() {
    try {
      final apiKey = _remoteConfig.getString('openai_api_key');
      dev.log(
          'ApiKeyService: Retrieved OpenAI API key, length: ${apiKey.length}');
      return apiKey;
    } catch (e) {
      dev.log('ApiKeyService: Error getting OpenAI API key: $e');
      return '';
    }
  }

  /// Get OpenRouter API key for AI chat via DeepSeek
  String getOpenRouterApiKey() {
    try {
      final apiKey = _remoteConfig.getString('openrouter_api_key');
      dev.log(
          'ApiKeyService: Retrieved OpenRouter API key, length: ${apiKey.length}');
      return apiKey;
    } catch (e) {
      dev.log('ApiKeyService: Error getting OpenRouter API key: $e');
      return '';
    }
  }
}
