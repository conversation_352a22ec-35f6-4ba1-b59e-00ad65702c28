import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/analytics_service.dart';
import 'dart:developer' as dev;

/// A mixin that provides analytics tracking functionality to widgets
mixin AnalyticsMixin<T extends ConsumerStatefulWidget> on ConsumerState<T> {
  late AnalyticsService _analyticsService;
  bool _isAnalyticsInitialized = false;
  String? _screenName;
  
  /// Initialize analytics tracking with the screen name
  void initAnalytics(String screenName) {
    try {
      _analyticsService = ref.read(analyticsServiceProvider);
      _screenName = screenName;
      _isAnalyticsInitialized = true;
      
      // Log screen view when initialized
      logScreenView();
    } catch (e) {
      dev.log('Error initializing analytics in $screenName: $e');
      _isAnalyticsInitialized = false;
    }
  }
  
  /// Log a screen view event for the current screen
  void logScreenView({Map<String, dynamic>? additionalParams}) {
    if (!_isAnalyticsInitialized || _screenName == null) return;
    
    try {
      _analyticsService.logScreenView(
        screenName: _screenName!,
        screenClass: widget.runtimeType.toString(),
        parameters: additionalParams,
      );
    } catch (e) {
      dev.log('Error logging screen view for $_screenName: $e');
    }
  }
  
  /// Log a user action event
  void logUserAction(AnalyticsEventType eventType, {Map<String, dynamic>? params}) {
    if (!_isAnalyticsInitialized) return;
    
    try {
      // Add the screen context to all events
      final parameters = {
        'screen_name': _screenName ?? 'unknown',
        if (params != null) ...params,
      };
      
      _analyticsService.logUserAction(
        eventType: eventType,
        parameters: parameters,
      );
    } catch (e) {
      dev.log('Error logging user action in $_screenName: $e');
    }
  }
  
  /// Track feature usage
  void trackFeatureUsage(String featureName, {Map<String, dynamic>? params}) {
    if (!_isAnalyticsInitialized) return;
    
    try {
      // Add the screen context
      final parameters = {
        'screen_name': _screenName ?? 'unknown',
        if (params != null) ...params,
      };
      
      _analyticsService.trackFeatureUsage(
        featureName: featureName,
        parameters: parameters,
      );
    } catch (e) {
      dev.log('Error tracking feature usage in $_screenName: $e');
    }
  }
  
  /// Log a funnel step
  void trackFunnelStep(AnalyticsFunnel funnel, String stepName, bool isCompleted, 
      {Map<String, dynamic>? params}) {
    if (!_isAnalyticsInitialized) return;
    
    try {
      // Add the screen context
      final parameters = {
        'screen_name': _screenName ?? 'unknown',
        if (params != null) ...params,
      };
      
      _analyticsService.trackFunnelStep(
        funnel: funnel,
        step: stepName,
        isCompleted: isCompleted,
        parameters: parameters,
      );
    } catch (e) {
      dev.log('Error tracking funnel step in $_screenName: $e');
    }
  }
  
  /// Log a car-related event
  void logCarEvent(AnalyticsEventType eventType, carModel, {Map<String, dynamic>? additionalParams}) {
    if (!_isAnalyticsInitialized) return;
    
    try {
      // Add screen context
      final params = {
        'screen_name': _screenName ?? 'unknown',
        if (additionalParams != null) ...additionalParams,
      };
      
      _analyticsService.logCarEvent(
        eventType: eventType,
        car: carModel,
        additionalParams: params,
      );
    } catch (e) {
      dev.log('Error logging car event in $_screenName: $e');
    }
  }
  
  @override
  void dispose() {
    // We could log screen exit events here if needed
    super.dispose();
  }
} 