import 'package:flutter/material.dart';

/// Class to manage app assets paths and helper methods
class AppAssets {
  // App icon image
  static const appIcon = AssetImage('assets/images/app_icon.png');
  
  // App logo widget with custom size
  static Widget appLogo({double size = 80}) {
    return Image(
      image: appIcon,
      width: size,
      height: size,
    );
  }

  // App logo with border
  static Widget appLogoBordered({
    double size = 80,
    Color borderColor = const Color(0xFFD8A25E),
    double borderWidth = 2,
  }) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        border: Border.all(color: borderColor, width: borderWidth),
      ),
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: appLogo(size: size - 16),
      ),
    );
  }
} 