import 'package:flutter/material.dart';

class AppConfig {
  static const String appName = 'Oil Plus';
  static const String appVersion = '1.0.0';
  
  // API Configurations
  static const int timeoutDuration = 30; // seconds
  
  // Storage Keys
  static const String themeKey = 'app_theme';
  static const String languageKey = 'app_language';
  static const String userKey = 'user_data';
  
  // Supported Languages
  static const List<Locale> supportedLocales = [
    Locale('en', 'US'), // English
    Locale('ar', 'SA'), // Arabic
  ];
  
  // Oil Change Default Settings
  static const int defaultKilometersInterval = 5000; // Default KM interval for oil change
  static const int defaultMonthsInterval = 6; // Default months interval for oil change
  
  // Notification Channels
  static const String mainNotificationChannel = 'oil_change_notifications';
  static const String reminderNotificationChannel = 'reminder_notifications';
  
  // Firebase Collections
  static const String usersCollection = 'users';
  static const String carsCollection = 'cars';
  static const String oilChangesCollection = 'oil_changes';
} 