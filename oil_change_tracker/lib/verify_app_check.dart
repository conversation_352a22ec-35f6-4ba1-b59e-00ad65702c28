import 'package:firebase_app_check/firebase_app_check.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'core/config/firebase_options.dart';
import 'core/services/app_check_debug_helper.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

/// A simple utility to verify the new App Check token
/// Run this file to specifically test the new token
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // Initialize Firebase first
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );
  
  runApp(const VerifyAppCheckApp());
}

class VerifyAppCheckApp extends StatelessWidget {
  const VerifyAppCheckApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Verify App Check Token',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(
          seedColor: const Color(0xFFD8A25E),
          brightness: Brightness.dark,
        ),
        useMaterial3: true,
      ),
      home: const VerifyAppCheckScreen(),
    );
  }
}

class VerifyAppCheckScreen extends StatefulWidget {
  const VerifyAppCheckScreen({super.key});

  @override
  State<VerifyAppCheckScreen> createState() => _VerifyAppCheckScreenState();
}

class _VerifyAppCheckScreenState extends State<VerifyAppCheckScreen> {
  String _status = 'Ready to verify';
  final String _tokenValue = "e7debe73-145b-44ae-a6ad-be0a15c2bc2c";
  String _verificationResult = '';
  bool _isLoading = false;
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('App Check Token Verification'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 16),
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('App Check Token', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 18)),
                    const SizedBox(height: 8),
                    Text('Value: $_tokenValue'),
                    const SizedBox(height: 8),
                    Text('Status: $_status', style: TextStyle(
                      color: _status.contains('Success') ? Colors.green : 
                             _status.contains('Error') ? Colors.red : Colors.white,
                      fontWeight: FontWeight.bold,
                    )),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 24),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                ElevatedButton(
                  onPressed: _isLoading ? null : _verifySetup,
                  child: const Text('Verify Setup'),
                ),
                ElevatedButton(
                  onPressed: _isLoading ? null : _testConnection,
                  child: const Text('Test Connection'),
                ),
              ],
            ),
            const SizedBox(height: 24),
            if (_isLoading)
              const Center(child: CircularProgressIndicator())
            else if (_verificationResult.isNotEmpty)
              Expanded(
                child: Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: SingleChildScrollView(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text('Verification Results', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 18)),
                          const SizedBox(height: 16),
                          SelectableText(_verificationResult),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Future<void> _verifySetup() async {
    setState(() {
      _status = 'Verifying setup...';
      _isLoading = true;
    });
    
    try {
      // First verify our token value is correct
      final currentToken = AppCheckDebugHelper.debugToken;
      final tokenVerification = currentToken == _tokenValue
          ? "✅ Token correctly set in AppCheckDebugHelper: $currentToken"
          : "❌ Token mismatch in AppCheckDebugHelper! Found: $currentToken, Expected: $_tokenValue";
      
      // Try to setup the debug token
      final setupSuccess = await AppCheckDebugHelper.setupDebugToken();
      final setupVerification = setupSuccess
          ? "✅ Debug token setup successful"
          : "❌ Debug token setup failed";
          
      // Get detailed troubleshooting
      final troubleshooting = await AppCheckDebugHelper.troubleshootAppCheck();
      
      setState(() {
        _verificationResult = "TOKEN VERIFICATION:\n$tokenVerification\n\nSETUP VERIFICATION:\n$setupVerification\n\nTROUBLESHOOTING DETAILS:\n$troubleshooting";
        _status = setupSuccess ? 'Setup verification successful' : 'Setup verification failed';
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _verificationResult = "Error during setup verification: $e";
        _status = 'Error verifying setup';
        _isLoading = false;
      });
    }
  }
  
  Future<void> _testConnection() async {
    setState(() {
      _status = 'Testing connection...';
      _isLoading = true;
    });
    
    try {
      final buffer = StringBuffer();
      buffer.writeln("FIREBASE APP CHECK CONNECTION TEST\n");
      
      // Try to get a token
      buffer.writeln("Step 1: Getting App Check token...");
      String? token;
      try {
        token = await FirebaseAppCheck.instance.getToken();
        if (token != null && token.isNotEmpty) {
          buffer.writeln("✅ Successfully retrieved token!");
          buffer.writeln("   Token starts with: ${token.substring(0, 10)}...");
        } else {
          buffer.writeln("❌ No token returned");
        }
      } catch (e) {
        buffer.writeln("❌ Error getting token: $e");
      }
      
      // Try to access Firestore
      buffer.writeln("\nStep 2: Testing Firestore access with App Check...");
      try {
        final testRef = FirebaseFirestore.instance.collection('_app_check_tests').doc('test');
        await testRef.get().timeout(const Duration(seconds: 10));
        buffer.writeln("✅ Successfully accessed Firestore with App Check!");
      } catch (e) {
        buffer.writeln("❌ Error accessing Firestore: $e");
        
        if (e.toString().contains('permission-denied')) {
          buffer.writeln("\nPOSSIBLE SOLUTION:");
          buffer.writeln("1. Go to Firebase Console → Project Settings → App Check");
          buffer.writeln("2. Make sure debug token is registered: $_tokenValue");
          buffer.writeln("3. Check Firestore rules to allow access to _app_check_tests collection");
        }
      }
      
      setState(() {
        _verificationResult = buffer.toString();
        _status = token != null ? 'Connection test completed' : 'Connection test failed';
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _verificationResult = "Error testing connection: $e";
        _status = 'Error testing connection';
        _isLoading = false;
      });
    }
  }
} 