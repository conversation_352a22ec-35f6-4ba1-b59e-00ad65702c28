import 'package:firebase_app_check/firebase_app_check.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'core/config/firebase_options.dart';
import 'core/services/app_check_debug_helper.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

/// A dedicated mini-app for testing and troubleshooting App Check
/// Run this instead of main.dart when you need to debug App Check issues
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // Initialize Firebase first
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );
  
  runApp(const AppCheckTestApp());
}

class AppCheckTestApp extends StatelessWidget {
  const AppCheckTestApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'App Check Debug',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(
          seedColor: const Color(0xFFD8A25E),
          brightness: Brightness.dark,
        ),
        useMaterial3: true,
      ),
      home: const AppCheckDebugScreen(),
    );
  }
}

class AppCheckDebugScreen extends StatefulWidget {
  const AppCheckDebugScreen({super.key});

  @override
  State<AppCheckDebugScreen> createState() => _AppCheckDebugScreenState();
}

class _AppCheckDebugScreenState extends State<AppCheckDebugScreen> {
  String _status = 'Not initialized';
  String _debugToken = AppCheckDebugHelper.debugToken;
  String _lastError = '';
  bool _isLoading = false;
  String _lastResponse = '';
  bool _tokenValid = false;
  
  @override
  void initState() {
    super.initState();
    _checkInitialState();
  }
  
  Future<void> _checkInitialState() async {
    setState(() {
      _status = 'Checking App Check status...';
      _isLoading = true;
    });
    
    try {
      await Future.delayed(const Duration(milliseconds: 500));
      final troubleshooting = await AppCheckDebugHelper.troubleshootAppCheck();
      
      setState(() {
        _lastResponse = troubleshooting;
        _status = 'Ready for testing';
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _lastError = e.toString();
        _status = 'Error during initialization';
        _isLoading = false;
      });
    }
  }
  
  Future<void> _setupDebugToken() async {
    setState(() {
      _status = 'Setting up debug token...';
      _isLoading = true;
    });
    
    try {
      final success = await AppCheckDebugHelper.setupDebugToken();
      
      setState(() {
        _tokenValid = success;
        _status = success 
            ? 'Debug token setup successful' 
            : 'Debug token setup failed';
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _lastError = e.toString();
        _status = 'Error setting up debug token';
        _isLoading = false;
      });
    }
  }
  
  Future<void> _testConnection() async {
    setState(() {
      _status = 'Testing connection...';
      _isLoading = true;
    });
    
    try {
      final success = await AppCheckDebugHelper.testAppCheckConnection();
      final troubleshooting = await AppCheckDebugHelper.troubleshootAppCheck();
      
      setState(() {
        _tokenValid = success;
        _lastResponse = troubleshooting;
        _status = success 
            ? 'Connection test successful!' 
            : 'Connection test failed';
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _lastError = e.toString();
        _status = 'Error testing connection';
        _isLoading = false;
      });
    }
  }
  
  Future<void> _activateDebugProvider() async {
    setState(() {
      _status = 'Activating debug provider...';
      _isLoading = true;
    });
    
    try {
      // Try to disable first
      await FirebaseAppCheck.instance.setTokenAutoRefreshEnabled(false);
      await Future.delayed(const Duration(milliseconds: 500));
      
      // Then activate with debug provider
      await FirebaseAppCheck.instance.activate(
        androidProvider: AndroidProvider.debug,
      );
      
      // Re-enable auto refresh
      await FirebaseAppCheck.instance.setTokenAutoRefreshEnabled(true);
      
      final token = await FirebaseAppCheck.instance.getToken();
      final hasToken = token != null && token.isNotEmpty;
      
      setState(() {
        _status = hasToken 
            ? 'Debug provider activated successfully!' 
            : 'Debug provider activated but no token';
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _lastError = e.toString();
        _status = 'Error activating debug provider';
        _isLoading = false;
      });
    }
  }
  
  Future<void> _getToken() async {
    setState(() {
      _status = 'Getting token...';
      _isLoading = true;
    });
    
    try {
      final token = await FirebaseAppCheck.instance.getToken();
      
      setState(() {
        if (token != null && token.isNotEmpty) {
          _status = 'Token retrieved successfully!';
          _lastResponse = 'Token starts with: ${token.substring(0, min(10, token.length))}...';
          _tokenValid = true;
        } else {
          _status = 'No token returned';
          _tokenValid = false;
        }
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _lastError = e.toString();
        _status = 'Error getting token';
        _isLoading = false;
        _tokenValid = false;
      });
    }
  }
  
  Future<void> _testFirestore() async {
    setState(() {
      _status = 'Testing Firestore access...';
      _isLoading = true;
    });
    
    try {
      // Try to access the special App Check test collection
      final testRef = FirebaseFirestore.instance.collection('_app_check_tests').doc('test');
      
      // Try to read the document
      await testRef.get().timeout(const Duration(seconds: 5));
      
      setState(() {
        _status = 'Firestore access successful!';
        _isLoading = false;
        _tokenValid = true;
      });
    } catch (e) {
      setState(() {
        _lastError = e.toString();
        _status = 'Error accessing Firestore';
        _isLoading = false;
        
        // Check if it's an App Check specific error
        final errorMsg = e.toString().toLowerCase();
        _tokenValid = !(errorMsg.contains('permission') || 
                        errorMsg.contains('app-check') || 
                        errorMsg.contains('app check'));
      });
    }
  }
  
  int min(int a, int b) => a < b ? a : b;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('App Check Debugger'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: _isLoading 
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Debug token info
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'Debug Token Information',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text('Token: $_debugToken'),
                          const SizedBox(height: 8),
                          Text(
                            'Token Status: ${_tokenValid ? '✅ Valid' : '❌ Invalid or not verified'}',
                            style: TextStyle(
                              color: _tokenValid ? Colors.green : Colors.red,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text('Current Status: $_status'),
                          if (_lastError.isNotEmpty) ...[
                            const SizedBox(height: 8),
                            Text(
                              'Last Error: $_lastError',
                              style: const TextStyle(color: Colors.red),
                            ),
                          ],
                        ],
                      ),
                    ),
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // Actions
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'Actions',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Wrap(
                            spacing: 8,
                            runSpacing: 8,
                            children: [
                              ElevatedButton(
                                onPressed: _setupDebugToken,
                                child: const Text('Setup Debug Token'),
                              ),
                              ElevatedButton(
                                onPressed: _testConnection, 
                                child: const Text('Test Connection'),
                              ),
                              ElevatedButton(
                                onPressed: _activateDebugProvider,
                                child: const Text('Activate Debug Provider'),
                              ),
                              ElevatedButton(
                                onPressed: _getToken,
                                child: const Text('Get Token'),
                              ),
                              ElevatedButton(
                                onPressed: _testFirestore,
                                child: const Text('Test Firestore'),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // Response
                  if (_lastResponse.isNotEmpty)
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              'Diagnostics Result',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Container(
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                color: Theme.of(context).colorScheme.surfaceContainerHighest,
                                borderRadius: BorderRadius.circular(8),
                              ),
                              width: double.infinity,
                              child: SelectableText(_lastResponse),
                            ),
                          ],
                        ),
                      ),
                    ),
                  
                  const SizedBox(height: 32),
                  
                  // Instructions
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'How to Fix App Check Issues',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 8),
                          const Text(
                            '1. Ensure the exact debug token is registered in Firebase Console\n'
                            '2. Go to Firebase Console → Project Settings → App Check\n'
                            '3. Click "Manage debug tokens" for your app\n'
                            '4. Add the token shown above\n'
                            '5. Make sure App Check enforcement is not set to "Enforce" during development\n'
                            '6. For custom backend resources, ensure the token is passed in X-Firebase-AppCheck header',
                            style: TextStyle(
                              height: 1.5,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
    );
  }
} 