import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';

import '../models/ai_usage.dart';

class AiUsageRepository {
  AiUsageRepository(this._firestore);

  final FirebaseFirestore _firestore;

  static const String _collection = 'ai_usage';

  /// Returns a stream of the current cycle usage doc for [uid].
  Stream<AiUsage> watchUsage(String uid) {
    if (kDebugMode) {
      print('AI Usage Repository: Watching usage for user: $uid');
    }
    final doc = _docRef(uid);
    return doc.snapshots().map((snap) {
      if (kDebugMode) {
        print('AI Usage Repository: Snapshot received. Exists: ${snap.exists}');
        if (snap.exists) {
          print('AI Usage Repository: Data: ${snap.data()}');
        }
      }
      if (!snap.exists) {
        if (kDebugMode) {
          print('AI Usage Repository: Creating fresh cycle');
        }
        return _freshCycle();
      }
      try {
        final usage = AiUsage.fromJson(snap.data()!);
        if (kDebugMode) {
          print(
              'AI Usage Repository: Parsed usage: voice=${usage.voiceCount}, chat=${usage.chatCount}');
        }
        return usage;
      } catch (e) {
        if (kDebugMode) {
          print('AI Usage Repository: Error parsing data: $e');
        }
        return _freshCycle();
      }
    });
  }

  Future<void> incrementVoice(String uid) => _increment(uid, 'voiceCount');
  Future<void> incrementChat(String uid) => _increment(uid, 'chatCount');

  /// Initialize AI usage document for testing purposes
  Future<void> initializeUsageForUser(String uid) async {
    if (kDebugMode) {
      print('AI Usage Repository: Initializing usage for user: $uid');
    }
    final doc = _docRef(uid);
    final now = DateTime.now();
    final currentCycleStart =
        DateTime(now.year, now.month).millisecondsSinceEpoch ~/ 1000;
    await doc.set({
      'cycleStartSeconds': currentCycleStart,
      'voiceCount': 0,
      'chatCount': 0,
    }, SetOptions(merge: true));
    if (kDebugMode) {
      print('AI Usage Repository: Usage initialized successfully');
    }
  }

  // ────────────────────────────────────────────────────────────────────────────
  Future<void> _increment(String uid, String field) async {
    final doc = _docRef(uid);
    final now = DateTime.now();
    final currentCycleStart =
        DateTime(now.year, now.month).millisecondsSinceEpoch ~/ 1000;
    await doc.set({
      'cycleStartSeconds': currentCycleStart,
      field: FieldValue.increment(1),
    }, SetOptions(merge: true));
  }

  DocumentReference<Map<String, dynamic>> _docRef(String uid) {
    return _firestore
        .collection('users')
        .doc(uid)
        .collection(_collection)
        .doc('current');
  }

  AiUsage _freshCycle() {
    final now = DateTime.now();
    final start = DateTime(now.year, now.month).millisecondsSinceEpoch ~/ 1000;
    return AiUsage(cycleStartSeconds: start);
  }
}
