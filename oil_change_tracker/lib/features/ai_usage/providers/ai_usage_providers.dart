import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../auth/providers/auth_provider.dart';
import '../data/ai_usage_repository.dart';
import '../models/ai_usage.dart';

final aiUsageRepositoryProvider = Provider<AiUsageRepository>((ref) {
  return AiUsageRepository(FirebaseFirestore.instance);
});

/// Provider to initialize AI usage for the current user
final initializeAiUsageProvider = FutureProvider<void>((ref) async {
  final user = ref.watch(authProvider).asData?.value;
  if (user == null || user.id == null) {
    print('Initialize AI Usage: No user to initialize');
    return;
  }
  print('Initialize AI Usage: Initializing for user: ${user.id}');
  final repo = ref.watch(aiUsageRepositoryProvider);
  await repo.initializeUsageForUser(user.id!);
});

/// Stream of this user's AI usage for the current cycle.
final aiUsageProvider = StreamProvider<AiUsage>((ref) {
  final user = ref.watch(authProvider).asData?.value;
  print(
      'AI Usage Provider: User from auth: ${user?.id}, email: ${user?.email}');
  if (user == null || user.id == null) {
    print('AI Usage Provider: No user or user ID, returning default usage');
    // Return default 0 usage when not logged in or user ID is null.
    return Stream.value(AiUsage(cycleStartSeconds: 0));
  }
  print(
      'AI Usage Provider: Getting repository and watching usage for user: ${user.id}');
  final repo = ref.watch(aiUsageRepositoryProvider);
  return repo.watchUsage(user.id!);
});
 