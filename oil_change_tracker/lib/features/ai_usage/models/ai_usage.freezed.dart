// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'ai_usage.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

AiUsage _$AiUsageFromJson(Map<String, dynamic> json) {
  return _AiUsage.fromJson(json);
}

/// @nodoc
mixin _$AiUsage {
  int get voiceCount => throw _privateConstructorUsedError;
  int get chatCount => throw _privateConstructorUsedError;

  /// Seconds since epoch (UTC) that represents the first day of the cycle.
  int get cycleStartSeconds => throw _privateConstructorUsedError;

  /// Serializes this AiUsage to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of AiUsage
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AiUsageCopyWith<AiUsage> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AiUsageCopyWith<$Res> {
  factory $AiUsageCopyWith(AiUsage value, $Res Function(AiUsage) then) =
      _$AiUsageCopyWithImpl<$Res, AiUsage>;
  @useResult
  $Res call({int voiceCount, int chatCount, int cycleStartSeconds});
}

/// @nodoc
class _$AiUsageCopyWithImpl<$Res, $Val extends AiUsage>
    implements $AiUsageCopyWith<$Res> {
  _$AiUsageCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AiUsage
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? voiceCount = null,
    Object? chatCount = null,
    Object? cycleStartSeconds = null,
  }) {
    return _then(_value.copyWith(
      voiceCount: null == voiceCount
          ? _value.voiceCount
          : voiceCount // ignore: cast_nullable_to_non_nullable
              as int,
      chatCount: null == chatCount
          ? _value.chatCount
          : chatCount // ignore: cast_nullable_to_non_nullable
              as int,
      cycleStartSeconds: null == cycleStartSeconds
          ? _value.cycleStartSeconds
          : cycleStartSeconds // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AiUsageImplCopyWith<$Res> implements $AiUsageCopyWith<$Res> {
  factory _$$AiUsageImplCopyWith(
          _$AiUsageImpl value, $Res Function(_$AiUsageImpl) then) =
      __$$AiUsageImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int voiceCount, int chatCount, int cycleStartSeconds});
}

/// @nodoc
class __$$AiUsageImplCopyWithImpl<$Res>
    extends _$AiUsageCopyWithImpl<$Res, _$AiUsageImpl>
    implements _$$AiUsageImplCopyWith<$Res> {
  __$$AiUsageImplCopyWithImpl(
      _$AiUsageImpl _value, $Res Function(_$AiUsageImpl) _then)
      : super(_value, _then);

  /// Create a copy of AiUsage
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? voiceCount = null,
    Object? chatCount = null,
    Object? cycleStartSeconds = null,
  }) {
    return _then(_$AiUsageImpl(
      voiceCount: null == voiceCount
          ? _value.voiceCount
          : voiceCount // ignore: cast_nullable_to_non_nullable
              as int,
      chatCount: null == chatCount
          ? _value.chatCount
          : chatCount // ignore: cast_nullable_to_non_nullable
              as int,
      cycleStartSeconds: null == cycleStartSeconds
          ? _value.cycleStartSeconds
          : cycleStartSeconds // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AiUsageImpl implements _AiUsage {
  const _$AiUsageImpl(
      {this.voiceCount = 0,
      this.chatCount = 0,
      required this.cycleStartSeconds});

  factory _$AiUsageImpl.fromJson(Map<String, dynamic> json) =>
      _$$AiUsageImplFromJson(json);

  @override
  @JsonKey()
  final int voiceCount;
  @override
  @JsonKey()
  final int chatCount;

  /// Seconds since epoch (UTC) that represents the first day of the cycle.
  @override
  final int cycleStartSeconds;

  @override
  String toString() {
    return 'AiUsage(voiceCount: $voiceCount, chatCount: $chatCount, cycleStartSeconds: $cycleStartSeconds)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AiUsageImpl &&
            (identical(other.voiceCount, voiceCount) ||
                other.voiceCount == voiceCount) &&
            (identical(other.chatCount, chatCount) ||
                other.chatCount == chatCount) &&
            (identical(other.cycleStartSeconds, cycleStartSeconds) ||
                other.cycleStartSeconds == cycleStartSeconds));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, voiceCount, chatCount, cycleStartSeconds);

  /// Create a copy of AiUsage
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AiUsageImplCopyWith<_$AiUsageImpl> get copyWith =>
      __$$AiUsageImplCopyWithImpl<_$AiUsageImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AiUsageImplToJson(
      this,
    );
  }
}

abstract class _AiUsage implements AiUsage {
  const factory _AiUsage(
      {final int voiceCount,
      final int chatCount,
      required final int cycleStartSeconds}) = _$AiUsageImpl;

  factory _AiUsage.fromJson(Map<String, dynamic> json) = _$AiUsageImpl.fromJson;

  @override
  int get voiceCount;
  @override
  int get chatCount;

  /// Seconds since epoch (UTC) that represents the first day of the cycle.
  @override
  int get cycleStartSeconds;

  /// Create a copy of AiUsage
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AiUsageImplCopyWith<_$AiUsageImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
