import 'package:freezed_annotation/freezed_annotation.dart';

part 'ai_usage.freezed.dart';
part 'ai_usage.g.dart';

/// Tracks a user's AI feature usage for the current billing cycle (calendar month).
@freezed
class AiUsage with _$AiUsage {
  const factory AiUsage({
    @Default(0) int voiceCount,
    @Default(0) int chatCount,

    /// Seconds since epoch (UTC) that represents the first day of the cycle.
    required int cycleStartSeconds,
  }) = _AiUsage;

  factory AiUsage.fromJson(Map<String, dynamic> json) =>
      _$AiUsageFromJson(json);
}
