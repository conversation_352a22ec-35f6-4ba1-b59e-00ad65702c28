// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ai_usage.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$AiUsageImpl _$$AiUsageImplFromJson(Map<String, dynamic> json) =>
    _$AiUsageImpl(
      voiceCount: (json['voiceCount'] as num?)?.toInt() ?? 0,
      chatCount: (json['chatCount'] as num?)?.toInt() ?? 0,
      cycleStartSeconds: (json['cycleStartSeconds'] as num).toInt(),
    );

Map<String, dynamic> _$$AiUsageImplToJson(_$AiUsageImpl instance) =>
    <String, dynamic>{
      'voiceCount': instance.voiceCount,
      'chatCount': instance.chatCount,
      'cycleStartSeconds': instance.cycleStartSeconds,
    };
