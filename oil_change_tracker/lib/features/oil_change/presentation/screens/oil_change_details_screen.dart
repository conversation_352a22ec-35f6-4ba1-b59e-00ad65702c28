import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:go_router/go_router.dart';
import '../../data/repositories/oil_change_repository.dart';
import '../../../../generated/app_localizations.dart';
import '../../../../core/theme/theme_extensions.dart';

class OilChangeDetailsScreen extends ConsumerWidget {
  final String carId;
  final String oilChangeId;

  const OilChangeDetailsScreen({
    super.key,
    required this.carId,
    required this.oilChangeId,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final l10n = S.of(context);
    final oilChangesAsync = ref.watch(oilChangeRepositoryProvider(carId));

    return oilChangesAsync.when(
      data: (oilChanges) {
        final oilChange =
            oilChanges.where((change) => change.id == oilChangeId).firstOrNull;

        if (oilChange == null) {
          return Scaffold(
            backgroundColor: context.containerBackgroundColor,
            appBar: AppBar(
              backgroundColor: context.containerBackgroundColor,
              title: Text(
                l10n.error,
                style: TextStyle(color: context.accentColor),
              ),
            ),
            body: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 48,
                    color: context.secondaryAccentColor,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    l10n.errorLoadingOilChangeDetails,
                    style: TextStyle(
                      color: context.secondaryAccentColor,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  ElevatedButton(
                    onPressed: () => context.pop(),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: context.secondaryAccentColor,
                      foregroundColor: Colors.white,
                    ),
                    child: Text(l10n.cancel),
                  ),
                ],
              ),
            ),
          );
        }

        return Scaffold(
          backgroundColor: context.containerBackgroundColor,
          appBar: AppBar(
            backgroundColor: context.containerBackgroundColor,
            title: Text(
              l10n.oilChangeDetails,
              style: TextStyle(
                color: context.accentColor,
                fontWeight: FontWeight.bold,
              ),
            ),
            actions: [
              IconButton(
                icon: Icon(Icons.history, color: context.secondaryAccentColor),
                onPressed: () => context.push('/cars/$carId/oil-changes'),
              ),
              IconButton(
                icon: Icon(Icons.delete, color: context.secondaryAccentColor),
                onPressed: () async {
                  final confirmed = await showDialog<bool>(
                    context: context,
                    builder: (context) => AlertDialog(
                      backgroundColor: context.containerBackgroundColor,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16),
                        side: BorderSide(color: context.accentColor, width: 1),
                      ),
                      title: Text(
                        l10n.deleteOilChange,
                        style: TextStyle(color: context.accentColor),
                      ),
                      content: Text(
                        l10n.deleteOilChangeConfirmation,
                        style: TextStyle(color: context.primaryTextColor),
                      ),
                      actions: [
                        TextButton(
                          onPressed: () => Navigator.pop(context, false),
                          child: Text(
                            l10n.cancel,
                            style: TextStyle(color: context.accentColor),
                          ),
                        ),
                        TextButton(
                          onPressed: () => Navigator.pop(context, true),
                          child: Text(
                            l10n.delete,
                            style:
                                TextStyle(color: context.secondaryAccentColor),
                          ),
                        ),
                      ],
                    ),
                  );

                  if (confirmed == true) {
                    await ref
                        .read(oilChangeRepositoryProvider(carId).notifier)
                        .deleteOilChange(oilChangeId);
                    if (context.mounted) {
                      context.pop();
                    }
                  }
                },
              ),
            ],
          ),
          body: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _DetailCard(
                  title: l10n.date,
                  value: DateFormat.yMMMd().format(oilChange.date),
                  icon: Icons.calendar_today,
                ),
                const SizedBox(height: 16),
                _DetailCard(
                  title: l10n.mileage,
                  value: '${oilChange.mileage} km',
                  icon: Icons.speed,
                ),
                const SizedBox(height: 16),
                _DetailCard(
                  title: l10n.oilType,
                  value: oilChange.oilType,
                  icon: Icons.oil_barrel,
                ),
                const SizedBox(height: 16),
                _DetailCard(
                  title: l10n.oilEnduranceKm,
                  value: '${oilChange.oilEnduranceKm} km',
                  icon: Icons.rocket_launch,
                ),
                if (oilChange.oilCost > 0) ...[
                  const SizedBox(height: 16),
                  _DetailCard(
                    title: l10n.oilCost,
                    value: oilChange.oilCost.toStringAsFixed(2),
                    icon: Icons.attach_money,
                  ),
                ],
                const SizedBox(height: 16),
                _DetailCard(
                  title: l10n.oilFilter,
                  value: oilChange.filterChanged
                      ? l10n.didYouChangeFilter
                      : '${l10n.didYouChangeFilter}: No',
                  icon: Icons.filter_alt,
                ),
                if (oilChange.filterType.isNotEmpty) ...[
                  const SizedBox(height: 16),
                  _DetailCard(
                    title: l10n.filterType,
                    value: oilChange.filterType,
                    icon: Icons.filter_alt,
                  ),
                ],
                if (oilChange.filterCost > 0) ...[
                  const SizedBox(height: 16),
                  _DetailCard(
                    title: l10n.filterCost,
                    value: oilChange.filterCost.toStringAsFixed(2),
                    icon: Icons.attach_money,
                  ),
                ],
                if (oilChange.notes != null && oilChange.notes!.isNotEmpty) ...[
                  const SizedBox(height: 16),
                  _DetailCard(
                    title: l10n.notes,
                    value: oilChange.notes!,
                    icon: Icons.note,
                  ),
                ],
              ],
            ),
          ),
        );
      },
      loading: () => Scaffold(
        backgroundColor: context.containerBackgroundColor,
        appBar: AppBar(
          backgroundColor: context.containerBackgroundColor,
          title: Text(
            l10n.loading,
            style: TextStyle(color: context.accentColor),
          ),
        ),
        body: Center(
          child: CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(context.accentColor),
          ),
        ),
      ),
      error: (error, stackTrace) => Scaffold(
        backgroundColor: context.containerBackgroundColor,
        appBar: AppBar(
          backgroundColor: context.containerBackgroundColor,
          title: Text(
            l10n.error,
            style: TextStyle(color: context.accentColor),
          ),
        ),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                size: 48,
                color: context.secondaryAccentColor,
              ),
              const SizedBox(height: 16),
              Text(
                l10n.errorLoadingOilChangeDetails,
                style: TextStyle(
                  color: context.secondaryAccentColor,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              ElevatedButton(
                onPressed: () {
                  ref.invalidate(oilChangeRepositoryProvider(carId));
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: context.secondaryAccentColor,
                  foregroundColor: Colors.white,
                ),
                child: Text(l10n.retry),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _DetailCard extends StatelessWidget {
  final String title;
  final String value;
  final IconData icon;

  const _DetailCard({
    required this.title,
    required this.value,
    required this.icon,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      color: context.containerBackgroundColor,
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(color: context.accentColor, width: 1),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Icon(
              icon,
              size: 32,
              color: context.accentColor,
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      color: context.primaryTextColor.withOpacity(0.7),
                      fontSize: 14,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    value,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
