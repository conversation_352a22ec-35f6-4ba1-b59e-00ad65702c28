import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:go_router/go_router.dart';
import 'package:oil_change_tracker/features/oil_change/presentation/widgets/oil_change_form.dart';
import 'package:oil_change_tracker/generated/app_localizations.dart';
import 'package:oil_change_tracker/features/oil_change/data/repositories/oil_change_repository.dart';
import 'package:oil_change_tracker/core/theme/theme_extensions.dart';

class OilChangeScreen extends ConsumerWidget {
  final String carId;

  const OilChangeScreen({super.key, required this.carId});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final l10n = S.of(context);
    final oilChangesAsync = ref.watch(oilChangeRepositoryProvider(carId));

    return Scaffold(
      backgroundColor: context.containerBackgroundColor,
      appBar: AppBar(
        backgroundColor: context.containerBackgroundColor,
        title: Text(
          l10n.oilChanges,
          style: TextStyle(
            color: context.accentColor,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
      body: oilChangesAsync.when(
        data: (changes) => changes.isEmpty
            ? Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.oil_barrel_outlined,
                      size: 64,
                      color: context.accentColor,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      l10n.noOilChangesRecorded,
                      style: TextStyle(
                        color: context.secondaryAccentColor,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      l10n.tapPlusToAddOilChange,
                      style: TextStyle(
                        color: context.secondaryTextColor,
                        fontSize: 16,
                      ),
                    ),
                  ],
                ),
              )
            : ListView.builder(
                padding: const EdgeInsets.all(16),
                itemCount: changes.length,
                itemBuilder: (context, index) {
                  final oilChange = changes[index];
                  return Card(
                    margin: const EdgeInsets.only(bottom: 16),
                    color: context.containerBackgroundColor,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                      side: BorderSide(color: context.accentColor),
                    ),
                    child: InkWell(
                      onTap: () => GoRouter.of(context)
                          .push('/oil-changes/$carId/${oilChange.id}'),
                      borderRadius: BorderRadius.circular(12),
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  DateFormat.yMMMd().format(oilChange.date),
                                  style: TextStyle(
                                    color: context.accentColor,
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                IconButton(
                                  icon: Icon(Icons.delete,
                                      color: context.secondaryAccentColor),
                                  onPressed: () => _deleteOilChange(
                                    context,
                                    ref,
                                    oilChange.id,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            _buildInfoRow(
                              context,
                              Icons.speed,
                              '${l10n.mileage}: ${oilChange.mileage} km',
                            ),
                            const SizedBox(height: 4),
                            _buildInfoRow(
                              context,
                              Icons.oil_barrel,
                              '${l10n.oilType}: ${oilChange.oilType}',
                            ),
                            const SizedBox(height: 4),
                            _buildInfoRow(
                              context,
                              Icons.rocket_launch,
                              '${l10n.oilEnduranceKm}: ${oilChange.oilEnduranceKm} km',
                            ),
                            const SizedBox(height: 4),
                            _buildInfoRow(
                              context,
                              Icons.filter_alt,
                              '${l10n.filterType}: ${oilChange.filterType}',
                            ),
                            if (oilChange.notes?.isNotEmpty == true) ...[
                              const SizedBox(height: 8),
                              Text(
                                l10n.notes,
                                style: TextStyle(
                                  color: context.accentColor,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 4),
                              Text(
                                oilChange.notes!,
                                style: TextStyle(
                                  color: context.secondaryTextColor,
                                ),
                              ),
                            ],
                          ],
                        ),
                      ),
                    ),
                  );
                },
              ),
        loading: () => Center(
          child: CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(context.accentColor),
          ),
        ),
        error: (error, stack) => Center(
          child: Text(
            '${l10n.error}: $error',
            style: TextStyle(color: context.secondaryAccentColor),
          ),
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => context.push('/oil-changes/$carId/add'),
        backgroundColor: context.accentColor,
        child: Icon(Icons.add,
            color: context.isDarkMode ? Colors.black : Colors.white),
      ),
    );
  }

  Widget _buildInfoRow(BuildContext context, IconData icon, String text) {
    return Row(
      children: [
        Icon(icon, size: 20, color: context.accentColor),
        const SizedBox(width: 8),
        Text(
          text,
          style: TextStyle(
            color: context.primaryTextColor,
            fontSize: 16,
          ),
        ),
      ],
    );
  }

  void _showAddOilChangeDialog(BuildContext context) async {
    await showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: context.containerBackgroundColor,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Padding(
        padding: EdgeInsets.only(
          bottom: MediaQuery.of(context).viewInsets.bottom,
        ),
        child: OilChangeForm(carId: carId),
      ),
    );
  }

  Future<void> _deleteOilChange(
    BuildContext context,
    WidgetRef ref,
    String id,
  ) async {
    final l10n = S.of(context);
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: context.containerBackgroundColor,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
          side: BorderSide(color: context.accentColor),
        ),
        title: Text(
          l10n.deleteOilChange,
          style: TextStyle(color: context.accentColor),
        ),
        content: Text(
          l10n.deleteOilChangeConfirmation,
          style: TextStyle(color: context.primaryTextColor),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: Text(
              l10n.cancel,
              style: TextStyle(color: context.accentColor),
            ),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: Text(
              l10n.delete,
              style: TextStyle(color: context.secondaryAccentColor),
            ),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      await ref
          .read(oilChangeRepositoryProvider(carId).notifier)
          .deleteOilChange(id);
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(l10n.oilChangeDeleted),
            backgroundColor: context.secondaryAccentColor,
          ),
        );
      }
    }
  }
}
