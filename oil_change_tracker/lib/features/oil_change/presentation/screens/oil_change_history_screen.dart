import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import '../../../../generated/app_localizations.dart';
import '../../../../core/theme/theme_extensions.dart';
import '../../../car_management/providers/car_provider.dart';
import '../../data/repositories/oil_change_repository.dart';
import '../../../../shared/widgets/loading_overlay.dart';

class OilChangeHistoryScreen extends ConsumerWidget {
  final String carId;

  const OilChangeHistoryScreen({
    super.key,
    required this.carId,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final carsAsync = ref.watch(carsProvider);
    final oilChangesAsync = ref.watch(oilChangeRepositoryProvider(carId));
    final l10n = S.of(context);

    return Scaffold(
      body: LoadingOverlay(
        isLoading: carsAsync.isLoading || oilChangesAsync.isLoading,
        child: Container(
          color: context.containerBackgroundColor,
          child: oilChangesAsync.when(
            data: (oilChanges) {
              if (oilChanges.isEmpty) {
                return Center(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 32),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Container(
                          padding: const EdgeInsets.all(32),
                          decoration: BoxDecoration(
                            color: context.accentColor.withOpacity(0.1),
                            shape: BoxShape.circle,
                          ),
                          child: Icon(
                            Icons.oil_barrel,
                            size: 96,
                            color: context.accentColor,
                          ),
                        ),
                        const SizedBox(height: 32),
                        Text(
                          l10n.noOilChangesFound,
                          style: TextStyle(
                            color: context.accentColor.withOpacity(0.9),
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          l10n.recordOilChangeFirst,
                          style: TextStyle(
                            color: context.secondaryTextColor,
                            fontSize: 18,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),
                );
              }

              return ListView.builder(
                padding: const EdgeInsets.all(16),
                itemCount: oilChanges.length,
                itemBuilder: (context, index) {
                  final oilChange = oilChanges[index];
                  final isFirst = index == 0;
                  final isLast = index == oilChanges.length - 1;
                  final nextMileage =
                      isFirst ? null : oilChanges[index - 1].mileage;
                  final mileageDiff = nextMileage != null
                      ? nextMileage - oilChange.mileage
                      : null;

                  return Material(
                    color: Colors.transparent,
                    child: InkWell(
                      onTap: () => context
                          .push('/cars/$carId/oil-changes/${oilChange.id}'),
                      child: Container(
                        margin: const EdgeInsets.only(bottom: 16),
                        child: Row(
                          children: [
                            // Timeline indicator
                            SizedBox(
                              width: 32,
                              child: Column(
                                children: [
                                  Container(
                                    width: 2,
                                    height: 24,
                                    color: isFirst
                                        ? Colors.transparent
                                        : context.accentColor.withOpacity(0.3),
                                  ),
                                  Container(
                                    width: 16,
                                    height: 16,
                                    decoration: BoxDecoration(
                                      color: isFirst
                                          ? context.accentColor
                                          : context.containerBackgroundColor,
                                      border: Border.all(
                                          color: context.accentColor, width: 2),
                                      shape: BoxShape.circle,
                                    ),
                                  ),
                                  Container(
                                    width: 2,
                                    height: 24,
                                    color: isLast
                                        ? Colors.transparent
                                        : context.accentColor.withOpacity(0.3),
                                  ),
                                ],
                              ),
                            ),
                            const SizedBox(width: 16),
                            // Content
                            Expanded(
                              child: Card(
                                color: context.containerBackgroundColor,
                                elevation: 4,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(16),
                                  side: BorderSide(
                                    color: isFirst
                                        ? context.accentColor
                                        : context.accentColor.withOpacity(0.3),
                                    width: isFirst ? 1 : 0.5,
                                  ),
                                ),
                                child: Padding(
                                  padding: const EdgeInsets.all(16),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Row(
                                        children: [
                                          Container(
                                            padding: const EdgeInsets.all(8),
                                            decoration: BoxDecoration(
                                              color: context.accentColor
                                                  .withOpacity(0.1),
                                              borderRadius:
                                                  BorderRadius.circular(8),
                                            ),
                                            child: Icon(
                                              Icons.oil_barrel,
                                              color: context.accentColor,
                                              size: 20,
                                            ),
                                          ),
                                          const SizedBox(width: 12),
                                          Expanded(
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Text(
                                                  DateFormat.yMMMd()
                                                      .format(oilChange.date),
                                                  style: TextStyle(
                                                    color: context.accentColor,
                                                    fontWeight: FontWeight.bold,
                                                    fontSize: 16,
                                                  ),
                                                ),
                                                Text(
                                                  '${NumberFormat.decimalPattern().format(oilChange.mileage)} km',
                                                  style: TextStyle(
                                                    color: context
                                                        .secondaryTextColor,
                                                    fontSize: 14,
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                          if (mileageDiff != null)
                                            Container(
                                              padding:
                                                  const EdgeInsets.symmetric(
                                                      horizontal: 8,
                                                      vertical: 4),
                                              decoration: BoxDecoration(
                                                color: context.accentColor
                                                    .withOpacity(0.1),
                                                borderRadius:
                                                    BorderRadius.circular(12),
                                              ),
                                              child: Text(
                                                '+${NumberFormat.decimalPattern().format(mileageDiff)} km',
                                                style: TextStyle(
                                                  color: context.accentColor,
                                                  fontSize: 12,
                                                  fontWeight: FontWeight.bold,
                                                ),
                                              ),
                                            ),
                                        ],
                                      ),
                                      const SizedBox(height: 16),
                                      _buildInfoRow(
                                        context,
                                        l10n.oilType,
                                        oilChange.oilType,
                                        Icons.local_gas_station,
                                      ),
                                      const SizedBox(height: 8),
                                      _buildInfoRow(
                                        context,
                                        l10n.oilEnduranceKm,
                                        '${oilChange.oilEnduranceKm} km',
                                        Icons.rocket_launch,
                                      ),
                                      const SizedBox(height: 8),
                                      if (oilChange.oilCost > 0) ...[
                                        _buildInfoRow(
                                          context,
                                          l10n.oilCost,
                                          oilChange.oilCost.toStringAsFixed(2),
                                          Icons.attach_money,
                                        ),
                                        const SizedBox(height: 8),
                                      ],
                                      _buildInfoRow(
                                        context,
                                        l10n.oilFilter,
                                        oilChange.filterChanged
                                            ? l10n.didYouChangeFilter
                                            : 'No',
                                        Icons.filter_alt,
                                      ),
                                      if (oilChange.filterType.isNotEmpty) ...[
                                        const SizedBox(height: 8),
                                        _buildInfoRow(
                                          context,
                                          l10n.filterType,
                                          oilChange.filterType,
                                          Icons.filter_alt,
                                        ),
                                      ],
                                      if (oilChange.filterCost > 0) ...[
                                        const SizedBox(height: 8),
                                        _buildInfoRow(
                                          context,
                                          l10n.filterCost,
                                          oilChange.filterCost
                                              .toStringAsFixed(2),
                                          Icons.attach_money,
                                        ),
                                      ],
                                      if (oilChange.notes?.isNotEmpty ==
                                          true) ...[
                                        const SizedBox(height: 12),
                                        Container(
                                          padding: const EdgeInsets.all(8),
                                          decoration: BoxDecoration(
                                            color: context
                                                .containerBackgroundColor
                                                .withOpacity(0.3),
                                            borderRadius:
                                                BorderRadius.circular(8),
                                            border: Border.all(
                                              color: context.accentColor
                                                  .withOpacity(0.2),
                                            ),
                                          ),
                                          child: Row(
                                            children: [
                                              Icon(
                                                Icons.note,
                                                color: context.accentColor,
                                                size: 16,
                                              ),
                                              const SizedBox(width: 8),
                                              Expanded(
                                                child: Text(
                                                  oilChange.notes!,
                                                  style: TextStyle(
                                                    color: context
                                                        .secondaryTextColor,
                                                    fontSize: 12,
                                                  ),
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ],
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  );
                },
              );
            },
            loading: () => Center(
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(context.accentColor),
              ),
            ),
            error: (error, stackTrace) => Center(
              child: Text(
                error.toString(),
                style: TextStyle(color: context.primaryTextColor),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildInfoRow(
      BuildContext context, String label, String value, IconData icon) {
    return Row(
      children: [
        Icon(
          icon,
          color: context.accentColor,
          size: 16,
        ),
        const SizedBox(width: 8),
        Text(
          '$label:',
          style: TextStyle(
            color: context.secondaryTextColor,
            fontSize: 14,
          ),
        ),
        const SizedBox(width: 8),
        Text(
          value,
          style: TextStyle(
            color: context.primaryTextColor,
            fontSize: 14,
          ),
        ),
      ],
    );
  }
}
