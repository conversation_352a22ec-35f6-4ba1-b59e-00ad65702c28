import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/models/car_model.dart';
import '../../../../core/theme/theme_extensions.dart';
import '../../../../core/utils/logger.dart';
import '../../../car_management/providers/car_provider.dart';
import '../../../../shared/widgets/custom_text_field.dart';
import '../../data/repositories/oil_change_repository.dart';
import '../../domain/models/oil_change_model.dart';
import '../../../../generated/app_localizations.dart';
import 'package:oil_change_tracker/features/ads/application/interstitial_ad_service.dart';
import '../../../../features/voice_input/presentation/widgets/voice_mic_button.dart';
import '../../../../features/voice_input/models/voice_form_type.dart';
import '../../../../features/voice_input/services/voice_command_service.dart';
import 'dart:developer' as dev;

class AddOilChangeScreen extends ConsumerStatefulWidget {
  final String? carId;
  final Map<String, dynamic>? voiceData;

  const AddOilChangeScreen({
    super.key,
    this.carId,
    this.voiceData,
  });

  @override
  ConsumerState<AddOilChangeScreen> createState() => _AddOilChangeScreenState();
}

class _AddOilChangeScreenState extends ConsumerState<AddOilChangeScreen> {
  final _formKey = GlobalKey<FormState>();
  final _mileageController = TextEditingController();
  final _oilTypeController = TextEditingController();
  final _oilEnduranceController = TextEditingController();
  final _filterTypeController = TextEditingController();
  final _notesController = TextEditingController();
  final _oilCostController = TextEditingController();
  final _filterCostController = TextEditingController();
  final _serviceProviderController = TextEditingController();
  DateTime _date = DateTime.now();
  CarModel? _selectedCar;
  bool _filterChanged = false;
  bool _processedVoiceData = false;
  bool _isProcessingAi = false;

  @override
  void initState() {
    super.initState();
    _oilCostController.text = '0.0';
    _filterCostController.text = '0.0';

    // Initialize with car ID if provided
    if (widget.carId != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        final cars = ref.read(carsProvider).value;
        if (cars != null) {
          final car = cars.firstWhere((car) => car.id == widget.carId);
          setState(() {
            _selectedCar = car;
            _mileageController.text = car.currentMileage.toString();
          });
        }
      });
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    // Process voice data if available and not already processed
    if (widget.voiceData != null && !_processedVoiceData) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _processVoiceData(widget.voiceData!);
        _processedVoiceData = true;
      });
    }
  }

  /// Process voice command data and populate form fields
  void _processVoiceData(Map<String, dynamic> voiceData) {
    dev.log('Processing voice data: $voiceData');

    try {
      // Process mileage
      if (voiceData.containsKey('current_mileage') &&
          voiceData['current_mileage'] != null) {
        _mileageController.text = voiceData['current_mileage'].toString();
      }

      // Process oil type
      if (voiceData.containsKey('oil_brand_and_type') &&
          voiceData['oil_brand_and_type'] != null) {
        _oilTypeController.text = voiceData['oil_brand_and_type'].toString();
      }

      // Process oil quantity
      if (voiceData.containsKey('oil_endurance_km') &&
          voiceData['oil_endurance_km'] != null) {
        _oilEnduranceController.text = voiceData['oil_endurance_km'].toString();
      }

      // Process filter information
      if (voiceData.containsKey('was_filter_changed') &&
          voiceData['was_filter_changed'] == true) {
        setState(() {
          _filterChanged = true;
        });
      }

      if (voiceData.containsKey('oil_filter_type') &&
          voiceData['oil_filter_type'] != null) {
        _filterTypeController.text = voiceData['oil_filter_type'].toString();
      }

      // Process costs
      if (voiceData.containsKey('cost_of_oil') &&
          voiceData['cost_of_oil'] != null) {
        _oilCostController.text = voiceData['cost_of_oil'].toString();
      }

      if (voiceData.containsKey('cost_of_filter') &&
          voiceData['cost_of_filter'] != null) {
        _filterCostController.text = voiceData['cost_of_filter'].toString();
      }

      // Process notes
      if (voiceData.containsKey('additional_notes') &&
          voiceData['additional_notes'] != null) {
        _notesController.text = voiceData['additional_notes'].toString();
      }

      // Process service provider name
      if (voiceData.containsKey('service_provider_name') &&
          voiceData['service_provider_name'] != null) {
        _serviceProviderController.text =
            voiceData['service_provider_name'].toString();
      }

      // Process date
      if (voiceData.containsKey('service_date') &&
          voiceData['service_date'] != null) {
        try {
          final parsedDate = DateTime.parse(voiceData['service_date']);
          setState(() {
            _date = parsedDate;
          });
        } catch (e) {
          AppLogger.warning(
              'Could not parse service_date: ${voiceData['service_date']}');
        }
      }

      // Process car information
      if (voiceData.containsKey('carId') && voiceData['carId'] != null) {
        final carId = voiceData['carId'].toString();
        final cars = ref.read(carsProvider).value;
        if (cars != null && cars.isNotEmpty) {
          // Try to find the car by ID first
          CarModel? matchingCar =
              cars.where((car) => car.id == carId).firstOrNull;

          // If not found by ID, try to find by make/model
          if (matchingCar == null) {
            matchingCar = cars
                .where((car) =>
                    car.make.toLowerCase().contains(carId.toLowerCase()) ||
                    car.model.toLowerCase().contains(carId.toLowerCase()))
                .firstOrNull;
          }

          // If still not found, use first car
          matchingCar ??= cars.first;

          setState(() {
            _selectedCar = matchingCar;
            if (_mileageController.text.isEmpty) {
              _mileageController.text = matchingCar!.currentMileage.toString();
            }
          });
        }
      }

      // Show confirmation to user
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Voice data processed successfully'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      dev.log('Error processing voice data: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error processing voice data: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  void dispose() {
    _serviceProviderController.dispose();
    _mileageController.dispose();
    _oilTypeController.dispose();
    _oilEnduranceController.dispose();
    _filterTypeController.dispose();
    _notesController.dispose();
    _oilCostController.dispose();
    _filterCostController.dispose();
    super.dispose();
  }

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _date,
      firstDate: DateTime(2000),
      lastDate: DateTime.now(),
    );
    if (picked != null && picked != _date) {
      setState(() {
        _date = picked;
      });
    }
  }

  void _submitForm(WidgetRef ref, BuildContext context, S l10n) async {
    if (!_formKey.currentState!.validate() || _selectedCar == null) return;

    final mileage = int.parse(_mileageController.text);
    final car = _selectedCar!;

    if (mileage <= car.lastOilChangeMileage) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text(
            'New mileage must be greater than the last oil change mileage',
          ),
        ),
      );
      return;
    }

    try {
      // Parse costs from text controllers
      final oilCost = double.parse(
          _oilCostController.text.isEmpty ? '0.0' : _oilCostController.text);
      final filterCost = double.parse(_filterCostController.text.isEmpty
          ? '0.0'
          : _filterCostController.text);

      // Create oil change record
      final oilChange = OilChangeModel(
        id: '', // Will be set by Firestore
        carId: car.id!,
        date: _date,
        mileage: mileage,
        oilType: _oilTypeController.text,
        oilEnduranceKm: int.parse(_oilEnduranceController.text),
        filterType: _filterTypeController.text,
        filterChanged: _filterChanged,
        oilCost: oilCost,
        filterCost: filterCost,
        serviceProviderName: _serviceProviderController.text.isNotEmpty
            ? _serviceProviderController.text
            : null,
        notes: _notesController.text.isNotEmpty ? _notesController.text : null,
        userId: car.userId,
      );

      // Add oil change record
      await ref
          .read(oilChangeRepositoryProvider(car.id!).notifier)
          .addOilChange(oilChange);

      // Update car details
      final updatedCar = car.copyWith(
        lastOilChangeDate: _date,
        lastOilChangeMileage: mileage,
        currentMileage: mileage,
        filterChanged: _filterChanged,
        oilCost: oilCost,
        filterCost: filterCost,
      );

      await ref.read(carsProvider.notifier).updateCar(updatedCar);

      // Clear form, show success, navigate back
      _formKey.currentState!.reset();
      _mileageController.text = '';
      _oilTypeController.text = '';
      _oilEnduranceController.text = '';
      _filterTypeController.text = '';
      _notesController.text = '';
      _oilCostController.text = '0.0';
      _filterCostController.text = '0.0';
      _serviceProviderController.text = '';
      _filterChanged = false;
      _date = DateTime.now();

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(l10n.saveSuccess)),
      );

      // Show Interstitial Ad
      ref.read(interstitialAdServiceProvider.notifier).showAdIfReady();

      if (context.mounted) {
        context.pop();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('${l10n.error}: ${e.toString()}')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final S l10n = S.of(context);
    final carsAsync = ref.watch(carsProvider);

    // Determine language code from locale
    final locale = Localizations.localeOf(context);
    final languageCode = locale.languageCode == 'ar' ? 'ar-EG' : 'en-US';

    return Stack(
      children: [
        Scaffold(
          backgroundColor: context.containerBackgroundColor,
          appBar: AppBar(
            backgroundColor: Colors.transparent,
            elevation: 0,
            title: Text(
              l10n.recordOilChange,
              style: TextStyle(
                color: context.accentColor,
                fontWeight: FontWeight.bold,
                fontSize: 22,
              ),
            ),
            leading: IconButton(
              icon: Icon(Icons.arrow_back, color: context.accentColor),
              onPressed: () => context.pop(),
            ),
          ),
          body: carsAsync.when(
            data: (cars) {
              if (widget.carId == null &&
                  _selectedCar == null &&
                  cars.isNotEmpty) {
                _selectedCar = cars.first;
              }
              if (_selectedCar == null && cars.isNotEmpty) {
                _selectedCar = cars.firstWhere((c) => c.id == widget.carId,
                    orElse: () => cars.first);
              }
              return SingleChildScrollView(
                padding: const EdgeInsets.all(16.0),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: <Widget>[
                      // Car selection
                      Card(
                        elevation: 2,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(16),
                        ),
                        margin: const EdgeInsets.only(bottom: 16),
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Icon(Icons.directions_car,
                                      color: context.accentColor, size: 22),
                                  const SizedBox(width: 8),
                                  Text(
                                    l10n.selectCar,
                                    style: TextStyle(
                                      color: context.accentColor,
                                      fontWeight: FontWeight.bold,
                                      fontSize: 16,
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 16),
                              DropdownButtonFormField<String>(
                                value: _selectedCar?.id,
                                decoration: InputDecoration(
                                  labelText: l10n.selectCar,
                                  labelStyle:
                                      TextStyle(color: context.accentColor),
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(12),
                                    borderSide: BorderSide(
                                        color: context.accentColor
                                            .withOpacity(0.3)),
                                  ),
                                  enabledBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(12),
                                    borderSide: BorderSide(
                                        color: context.accentColor
                                            .withOpacity(0.5)),
                                  ),
                                  focusedBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(12),
                                    borderSide:
                                        BorderSide(color: context.accentColor),
                                  ),
                                  contentPadding: const EdgeInsets.symmetric(
                                      horizontal: 16, vertical: 16),
                                  filled: true,
                                  fillColor: context.containerBackgroundColor,
                                ),
                                dropdownColor: context.containerBackgroundColor,
                                style: TextStyle(
                                    color: context.primaryTextColor,
                                    fontSize: 16),
                                borderRadius: BorderRadius.circular(12),
                                icon: Icon(Icons.arrow_drop_down,
                                    color: context.accentColor),
                                items: cars
                                    .map(
                                      (car) => DropdownMenuItem(
                                        value: car.id,
                                        child: Text(
                                          '${car.year} ${car.make} ${car.model}',
                                          style: TextStyle(
                                              color: context.primaryTextColor),
                                        ),
                                      ),
                                    )
                                    .toList(),
                                onChanged: (carId) {
                                  if (carId != null) {
                                    final car =
                                        cars.firstWhere((c) => c.id == carId);
                                    setState(() {
                                      _selectedCar = car;
                                      _mileageController.text =
                                          car.currentMileage.toString();
                                    });
                                  }
                                },
                                validator: (value) {
                                  if (value == null)
                                    return l10n.pleaseSelectCar;
                                  return null;
                                },
                              ),
                            ],
                          ),
                        ),
                      ),

                      // Oil change details
                      Card(
                        elevation: 2,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(16),
                        ),
                        margin: const EdgeInsets.only(bottom: 16),
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Icon(Icons.oil_barrel,
                                      color: context.accentColor, size: 22),
                                  const SizedBox(width: 8),
                                  Text(
                                    l10n.oilType,
                                    style: TextStyle(
                                      color: context.accentColor,
                                      fontWeight: FontWeight.bold,
                                      fontSize: 16,
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 16),

                              // Mileage field
                              CustomTextField(
                                controller: _mileageController,
                                labelText: l10n.currentMileage,
                                keyboardType: TextInputType.number,
                                prefixIcon: Icon(Icons.speed,
                                    color:
                                        context.accentColor.withOpacity(0.7)),
                                inputFormatters: [
                                  FilteringTextInputFormatter.digitsOnly
                                ],
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return l10n.pleaseEnterCurrentMileage;
                                  }
                                  return null;
                                }, suffixText: '',
                              ),
                              const SizedBox(height: 16),

                              // Oil type field
                              CustomTextField(
                                controller: _oilTypeController,
                                labelText: l10n.oilType,
                                prefixIcon: Icon(Icons.format_color_fill,
                                    color:
                                        context.accentColor.withOpacity(0.7)),
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return l10n.pleaseEnterOilType;
                                  }
                                  return null;
                                }, suffixText: '',
                              ),
                              const SizedBox(height: 16),

                              // Oil quantity field
                              CustomTextField(
                                controller: _oilEnduranceController,
                                labelText: '${l10n.oilQuantity} (km)',
                                keyboardType: TextInputType.number,
                                prefixIcon: Icon(Icons.route,
                                    color:
                                        context.accentColor.withOpacity(0.7)),
                                inputFormatters: [
                                  FilteringTextInputFormatter.digitsOnly
                                ],
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return l10n.pleaseEnterOilQuantity;
                                  }
                                  return null;
                                }, suffixText: '',
                              ),
                            ],
                          ),
                        ),
                      ),

                      // Filter details card
                      Card(
                        elevation: 2,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(16),
                        ),
                        margin: const EdgeInsets.only(bottom: 16),
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Icon(Icons.filter_alt,
                                      color: context.accentColor, size: 22),
                                  const SizedBox(width: 8),
                                  Text(
                                    l10n.oilFilter,
                                    style: TextStyle(
                                      color: context.accentColor,
                                      fontWeight: FontWeight.bold,
                                      fontSize: 16,
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 16),

                              // Filter change switch with animation
                              AnimatedContainer(
                                duration: const Duration(milliseconds: 300),
                                padding: const EdgeInsets.all(12),
                                decoration: BoxDecoration(
                                  color: _filterChanged
                                      ? context.accentColor.withOpacity(0.1)
                                      : context.containerBackgroundColor,
                                  borderRadius: BorderRadius.circular(12),
                                  border: Border.all(
                                    color: _filterChanged
                                        ? context.accentColor
                                        : context.accentColor.withOpacity(0.3),
                                  ),
                                ),
                                child: Row(
                                  children: [
                                    Expanded(
                                      child: Text(
                                        l10n.didYouChangeFilter,
                                        style: TextStyle(
                                          color: context.primaryTextColor,
                                          fontWeight: _filterChanged
                                              ? FontWeight.bold
                                              : FontWeight.normal,
                                        ),
                                      ),
                                    ),
                                    Switch.adaptive(
                                      value: _filterChanged,
                                      activeColor: context.secondaryAccentColor,
                                      activeTrackColor: context
                                          .secondaryAccentColor
                                          .withOpacity(0.5),
                                      onChanged: (value) {
                                        setState(() {
                                          _filterChanged = value;
                                        });
                                      },
                                    ),
                                  ],
                                ),
                              ),

                              // Filter type field (only visible when filter is changed)
                              AnimatedCrossFade(
                                duration: const Duration(milliseconds: 300),
                                crossFadeState: _filterChanged
                                    ? CrossFadeState.showSecond
                                    : CrossFadeState.showFirst,
                                firstChild: const SizedBox(height: 8),
                                secondChild: Padding(
                                  padding: const EdgeInsets.only(top: 16),
                                  child: CustomTextField(
                                    controller: _filterTypeController,
                                    labelText: l10n.filterType,
                                    placeholder: l10n.enterFilterType,
                                    prefixIcon: Icon(Icons.filter_list,
                                        color: context.accentColor
                                            .withOpacity(0.7)),
                                    textCapitalization:
                                        TextCapitalization.words, suffixText: '',
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),

                      // Cost details card
                      Card(
                        elevation: 2,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(16),
                        ),
                        margin: const EdgeInsets.only(bottom: 16),
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Icon(Icons.attach_money,
                                      color: context.accentColor, size: 22),
                                  const SizedBox(width: 8),
                                  Text(
                                    l10n.costs,
                                    style: TextStyle(
                                      color: context.accentColor,
                                      fontWeight: FontWeight.bold,
                                      fontSize: 16,
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 16),

                              // Oil cost field
                              CustomTextField(
                                controller: _oilCostController,
                                labelText: l10n.oilCost,
                                placeholder: '0.00',
                                prefixIcon: Icon(Icons.monetization_on,
                                    color:
                                        context.accentColor.withOpacity(0.7)),
                                keyboardType:
                                    const TextInputType.numberWithOptions(
                                        decimal: true),
                                inputFormatters: [
                                  FilteringTextInputFormatter.allow(
                                      RegExp(r'^\d+\.?\d{0,2}')),
                                ], suffixText: '',
                              ),

                              // Filter cost field (only visible when filter is changed)
                              AnimatedCrossFade(
                                duration: const Duration(milliseconds: 300),
                                crossFadeState: _filterChanged
                                    ? CrossFadeState.showSecond
                                    : CrossFadeState.showFirst,
                                firstChild: const SizedBox(height: 0),
                                secondChild: Padding(
                                  padding: const EdgeInsets.only(top: 16),
                                  child: CustomTextField(
                                    controller: _filterCostController,
                                    labelText: l10n.filterCost,
                                    placeholder: '0.00',
                                    prefixIcon: Icon(Icons.monetization_on,
                                        color: context.accentColor
                                            .withOpacity(0.7)),
                                    keyboardType:
                                        const TextInputType.numberWithOptions(
                                            decimal: true),
                                    inputFormatters: [
                                      FilteringTextInputFormatter.allow(
                                          RegExp(r'^\d+\.?\d{0,2}')),
                                    ], suffixText: '',
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),

                      // Service Provider Name
                      const SizedBox(height: 16),
                      CustomTextField(
                        controller: _serviceProviderController,
                        labelText: l10n.serviceProvider,
                        keyboardType: TextInputType.text, suffixText: '',
                      ),

                      // Date and notes card
                      Card(
                        elevation: 2,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(16),
                        ),
                        margin: const EdgeInsets.only(bottom: 24),
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Icon(Icons.event_note,
                                      color: context.accentColor, size: 22),
                                  const SizedBox(width: 8),
                                  Text(
                                    l10n.details,
                                    style: TextStyle(
                                      color: context.accentColor,
                                      fontWeight: FontWeight.bold,
                                      fontSize: 16,
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 16),

                              // Date picker
                              InkWell(
                                onTap: () => _selectDate(context),
                                borderRadius: BorderRadius.circular(12),
                                child: Container(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 16, vertical: 16),
                                  decoration: BoxDecoration(
                                    border: Border.all(
                                        color: context.accentColor
                                            .withOpacity(0.5)),
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: Row(
                                    children: [
                                      Icon(Icons.calendar_today,
                                          color: context.accentColor, size: 20),
                                      const SizedBox(width: 12),
                                      Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            l10n.oilChangeDate,
                                            style: TextStyle(
                                              color: context.secondaryTextColor,
                                              fontSize: 14,
                                            ),
                                          ),
                                          const SizedBox(height: 4),
                                          Text(
                                            '${_date.year}-${_date.month.toString().padLeft(2, '0')}-${_date.day.toString().padLeft(2, '0')}',
                                            style: TextStyle(
                                              color: context.primaryTextColor,
                                              fontWeight: FontWeight.bold,
                                              fontSize: 16,
                                            ),
                                          ),
                                        ],
                                      ),
                                      const Spacer(),
                                      Icon(Icons.arrow_drop_down,
                                          color: context.accentColor),
                                    ],
                                  ),
                                ),
                              ),
                              const SizedBox(height: 16),

                              // Notes field
                              CustomTextField(
                                controller: _notesController,
                                labelText: l10n.notes,
                                prefixIcon: Icon(Icons.note,
                                    color:
                                        context.accentColor.withOpacity(0.7)),
                                maxLines: 3, suffixText: '',
                              ),
                            ],
                          ),
                        ),
                      ),

                      // Save button
                      ElevatedButton(
                        onPressed: () => _submitForm(ref, context, l10n),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: context.secondaryAccentColor,
                          foregroundColor:
                              context.isDarkMode ? Colors.black : Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(30),
                          ),
                          elevation: 3,
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const Icon(Icons.save),
                            const SizedBox(width: 8),
                            Text(
                              l10n.save,
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
            loading: () => Center(
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(context.accentColor),
              ),
            ),
            error: (err, stack) => Center(child: Text('Error: $err')),
          ),
          floatingActionButton: _buildVoiceInputButton(),
          floatingActionButtonLocation: FloatingActionButtonLocation.endFloat,
        ),
        if (_isProcessingAi)
          const Opacity(
            opacity: 0.8,
            child: ModalBarrier(dismissible: false, color: Colors.black),
          ),
        if (_isProcessingAi)
          const Center(
            child: CircularProgressIndicator(),
          ),
      ],
    );
  }

  Widget _buildVoiceInputButton() {
    final locale = Localizations.localeOf(context);

    return VoiceMicButton(
      formType: VoiceFormType.oilChange,
      languageCode: locale.languageCode == 'ar' ? 'ar-EG' : 'en-US',
      onComplete: _handleVoiceInput,
    );
  }

  void _handleVoiceInput(
      String transcription, Map<String, dynamic>? extractedData) {
    VLOG('ui', 'Voice input received: "$transcription"');

    if (extractedData == null || extractedData.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(S.of(context).couldNotUnderstandCommand),
          backgroundColor: Theme.of(context).colorScheme.error,
        ),
      );
      return;
    }

    // Apply extracted data to form fields
    setState(() {
      if (extractedData['current_mileage'] != null) {
        _mileageController.text = extractedData['current_mileage'].toString();
      }
      if (extractedData['oil_brand_and_type'] != null) {
        _oilTypeController.text = extractedData['oil_brand_and_type'];
      }
      if (extractedData['oil_endurance_km'] != null) {
        _oilEnduranceController.text =
            extractedData['oil_endurance_km'].toString();
      }
      if (extractedData['was_filter_changed'] != null) {
        _filterChanged = extractedData['was_filter_changed'];
      }
      if (extractedData['oil_filter_type'] != null) {
        _filterTypeController.text = extractedData['oil_filter_type'];
      }
      if (extractedData['cost_of_oil'] != null) {
        _oilCostController.text = extractedData['cost_of_oil'].toString();
      }
      if (extractedData['cost_of_filter'] != null) {
        _filterCostController.text = extractedData['cost_of_filter'].toString();
      }
      if (extractedData['total_cost'] != null) {
        final totalCost = extractedData['total_cost'];
        // If total cost is provided, split it between oil and filter
        _oilCostController.text = totalCost.toString();
        if (_filterChanged) {
          // Assume 20% of total cost is for filter if not specified
          final filterCost = (totalCost * 0.2).round();
          final oilCost = totalCost - filterCost;
          _oilCostController.text = oilCost.toString();
          _filterCostController.text = filterCost.toString();
        }
      }
      if (extractedData['service_provider_name'] != null) {
        _serviceProviderController.text =
            extractedData['service_provider_name'];
      }
      if (extractedData['additional_notes'] != null) {
        _notesController.text = extractedData['additional_notes'];
      }
      if (extractedData['service_date'] != null) {
        try {
          _date = DateTime.parse(extractedData['service_date']);
        } catch (e) {
          // If parsing fails, use today's date
          _date = DateTime.now();
        }
      }
    });

    // Show success feedback
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(S.of(context).success),
        backgroundColor: Theme.of(context).colorScheme.primary,
      ),
    );
  }
}
