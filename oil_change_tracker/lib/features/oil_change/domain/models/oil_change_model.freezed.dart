// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'oil_change_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

OilChangeModel _$OilChangeModelFromJson(Map<String, dynamic> json) {
  return _OilChangeModel.fromJson(json);
}

/// @nodoc
mixin _$OilChangeModel {
  String get id => throw _privateConstructorUsedError;
  String get carId => throw _privateConstructorUsedError;
  String get userId => throw _privateConstructorUsedError;
  DateTime get date => throw _privateConstructorUsedError;
  int get mileage => throw _privateConstructorUsedError;
  String get oilType => throw _privateConstructorUsedError;
  int get oilEnduranceKm => throw _privateConstructorUsedError;
  String get filterType => throw _privateConstructorUsedError;
  bool get filterChanged => throw _privateConstructorUsedError;
  double get oilCost => throw _privateConstructorUsedError;
  double get filterCost => throw _privateConstructorUsedError;
  String? get serviceProviderName => throw _privateConstructorUsedError;
  String? get notes => throw _privateConstructorUsedError;
  @JsonKey(fromJson: _timestampToDateTime)
  DateTime? get createdAt => throw _privateConstructorUsedError;
  @JsonKey(fromJson: _timestampToDateTime)
  DateTime? get updatedAt => throw _privateConstructorUsedError;

  /// Serializes this OilChangeModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of OilChangeModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $OilChangeModelCopyWith<OilChangeModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OilChangeModelCopyWith<$Res> {
  factory $OilChangeModelCopyWith(
          OilChangeModel value, $Res Function(OilChangeModel) then) =
      _$OilChangeModelCopyWithImpl<$Res, OilChangeModel>;
  @useResult
  $Res call(
      {String id,
      String carId,
      String userId,
      DateTime date,
      int mileage,
      String oilType,
      int oilEnduranceKm,
      String filterType,
      bool filterChanged,
      double oilCost,
      double filterCost,
      String? serviceProviderName,
      String? notes,
      @JsonKey(fromJson: _timestampToDateTime) DateTime? createdAt,
      @JsonKey(fromJson: _timestampToDateTime) DateTime? updatedAt});
}

/// @nodoc
class _$OilChangeModelCopyWithImpl<$Res, $Val extends OilChangeModel>
    implements $OilChangeModelCopyWith<$Res> {
  _$OilChangeModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of OilChangeModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? carId = null,
    Object? userId = null,
    Object? date = null,
    Object? mileage = null,
    Object? oilType = null,
    Object? oilEnduranceKm = null,
    Object? filterType = null,
    Object? filterChanged = null,
    Object? oilCost = null,
    Object? filterCost = null,
    Object? serviceProviderName = freezed,
    Object? notes = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      carId: null == carId
          ? _value.carId
          : carId // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      date: null == date
          ? _value.date
          : date // ignore: cast_nullable_to_non_nullable
              as DateTime,
      mileage: null == mileage
          ? _value.mileage
          : mileage // ignore: cast_nullable_to_non_nullable
              as int,
      oilType: null == oilType
          ? _value.oilType
          : oilType // ignore: cast_nullable_to_non_nullable
              as String,
      oilEnduranceKm: null == oilEnduranceKm
          ? _value.oilEnduranceKm
          : oilEnduranceKm // ignore: cast_nullable_to_non_nullable
              as int,
      filterType: null == filterType
          ? _value.filterType
          : filterType // ignore: cast_nullable_to_non_nullable
              as String,
      filterChanged: null == filterChanged
          ? _value.filterChanged
          : filterChanged // ignore: cast_nullable_to_non_nullable
              as bool,
      oilCost: null == oilCost
          ? _value.oilCost
          : oilCost // ignore: cast_nullable_to_non_nullable
              as double,
      filterCost: null == filterCost
          ? _value.filterCost
          : filterCost // ignore: cast_nullable_to_non_nullable
              as double,
      serviceProviderName: freezed == serviceProviderName
          ? _value.serviceProviderName
          : serviceProviderName // ignore: cast_nullable_to_non_nullable
              as String?,
      notes: freezed == notes
          ? _value.notes
          : notes // ignore: cast_nullable_to_non_nullable
              as String?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$OilChangeModelImplCopyWith<$Res>
    implements $OilChangeModelCopyWith<$Res> {
  factory _$$OilChangeModelImplCopyWith(_$OilChangeModelImpl value,
          $Res Function(_$OilChangeModelImpl) then) =
      __$$OilChangeModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String carId,
      String userId,
      DateTime date,
      int mileage,
      String oilType,
      int oilEnduranceKm,
      String filterType,
      bool filterChanged,
      double oilCost,
      double filterCost,
      String? serviceProviderName,
      String? notes,
      @JsonKey(fromJson: _timestampToDateTime) DateTime? createdAt,
      @JsonKey(fromJson: _timestampToDateTime) DateTime? updatedAt});
}

/// @nodoc
class __$$OilChangeModelImplCopyWithImpl<$Res>
    extends _$OilChangeModelCopyWithImpl<$Res, _$OilChangeModelImpl>
    implements _$$OilChangeModelImplCopyWith<$Res> {
  __$$OilChangeModelImplCopyWithImpl(
      _$OilChangeModelImpl _value, $Res Function(_$OilChangeModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of OilChangeModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? carId = null,
    Object? userId = null,
    Object? date = null,
    Object? mileage = null,
    Object? oilType = null,
    Object? oilEnduranceKm = null,
    Object? filterType = null,
    Object? filterChanged = null,
    Object? oilCost = null,
    Object? filterCost = null,
    Object? serviceProviderName = freezed,
    Object? notes = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(_$OilChangeModelImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      carId: null == carId
          ? _value.carId
          : carId // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      date: null == date
          ? _value.date
          : date // ignore: cast_nullable_to_non_nullable
              as DateTime,
      mileage: null == mileage
          ? _value.mileage
          : mileage // ignore: cast_nullable_to_non_nullable
              as int,
      oilType: null == oilType
          ? _value.oilType
          : oilType // ignore: cast_nullable_to_non_nullable
              as String,
      oilEnduranceKm: null == oilEnduranceKm
          ? _value.oilEnduranceKm
          : oilEnduranceKm // ignore: cast_nullable_to_non_nullable
              as int,
      filterType: null == filterType
          ? _value.filterType
          : filterType // ignore: cast_nullable_to_non_nullable
              as String,
      filterChanged: null == filterChanged
          ? _value.filterChanged
          : filterChanged // ignore: cast_nullable_to_non_nullable
              as bool,
      oilCost: null == oilCost
          ? _value.oilCost
          : oilCost // ignore: cast_nullable_to_non_nullable
              as double,
      filterCost: null == filterCost
          ? _value.filterCost
          : filterCost // ignore: cast_nullable_to_non_nullable
              as double,
      serviceProviderName: freezed == serviceProviderName
          ? _value.serviceProviderName
          : serviceProviderName // ignore: cast_nullable_to_non_nullable
              as String?,
      notes: freezed == notes
          ? _value.notes
          : notes // ignore: cast_nullable_to_non_nullable
              as String?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$OilChangeModelImpl extends _OilChangeModel {
  const _$OilChangeModelImpl(
      {required this.id,
      required this.carId,
      required this.userId,
      required this.date,
      required this.mileage,
      required this.oilType,
      this.oilEnduranceKm = 5000,
      required this.filterType,
      this.filterChanged = false,
      this.oilCost = 0.0,
      this.filterCost = 0.0,
      this.serviceProviderName,
      this.notes,
      @JsonKey(fromJson: _timestampToDateTime) this.createdAt,
      @JsonKey(fromJson: _timestampToDateTime) this.updatedAt})
      : super._();

  factory _$OilChangeModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$OilChangeModelImplFromJson(json);

  @override
  final String id;
  @override
  final String carId;
  @override
  final String userId;
  @override
  final DateTime date;
  @override
  final int mileage;
  @override
  final String oilType;
  @override
  @JsonKey()
  final int oilEnduranceKm;
  @override
  final String filterType;
  @override
  @JsonKey()
  final bool filterChanged;
  @override
  @JsonKey()
  final double oilCost;
  @override
  @JsonKey()
  final double filterCost;
  @override
  final String? serviceProviderName;
  @override
  final String? notes;
  @override
  @JsonKey(fromJson: _timestampToDateTime)
  final DateTime? createdAt;
  @override
  @JsonKey(fromJson: _timestampToDateTime)
  final DateTime? updatedAt;

  @override
  String toString() {
    return 'OilChangeModel(id: $id, carId: $carId, userId: $userId, date: $date, mileage: $mileage, oilType: $oilType, oilEnduranceKm: $oilEnduranceKm, filterType: $filterType, filterChanged: $filterChanged, oilCost: $oilCost, filterCost: $filterCost, serviceProviderName: $serviceProviderName, notes: $notes, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OilChangeModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.carId, carId) || other.carId == carId) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.date, date) || other.date == date) &&
            (identical(other.mileage, mileage) || other.mileage == mileage) &&
            (identical(other.oilType, oilType) || other.oilType == oilType) &&
            (identical(other.oilEnduranceKm, oilEnduranceKm) ||
                other.oilEnduranceKm == oilEnduranceKm) &&
            (identical(other.filterType, filterType) ||
                other.filterType == filterType) &&
            (identical(other.filterChanged, filterChanged) ||
                other.filterChanged == filterChanged) &&
            (identical(other.oilCost, oilCost) || other.oilCost == oilCost) &&
            (identical(other.filterCost, filterCost) ||
                other.filterCost == filterCost) &&
            (identical(other.serviceProviderName, serviceProviderName) ||
                other.serviceProviderName == serviceProviderName) &&
            (identical(other.notes, notes) || other.notes == notes) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      carId,
      userId,
      date,
      mileage,
      oilType,
      oilEnduranceKm,
      filterType,
      filterChanged,
      oilCost,
      filterCost,
      serviceProviderName,
      notes,
      createdAt,
      updatedAt);

  /// Create a copy of OilChangeModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$OilChangeModelImplCopyWith<_$OilChangeModelImpl> get copyWith =>
      __$$OilChangeModelImplCopyWithImpl<_$OilChangeModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$OilChangeModelImplToJson(
      this,
    );
  }
}

abstract class _OilChangeModel extends OilChangeModel {
  const factory _OilChangeModel(
          {required final String id,
          required final String carId,
          required final String userId,
          required final DateTime date,
          required final int mileage,
          required final String oilType,
          final int oilEnduranceKm,
          required final String filterType,
          final bool filterChanged,
          final double oilCost,
          final double filterCost,
          final String? serviceProviderName,
          final String? notes,
          @JsonKey(fromJson: _timestampToDateTime) final DateTime? createdAt,
          @JsonKey(fromJson: _timestampToDateTime) final DateTime? updatedAt}) =
      _$OilChangeModelImpl;
  const _OilChangeModel._() : super._();

  factory _OilChangeModel.fromJson(Map<String, dynamic> json) =
      _$OilChangeModelImpl.fromJson;

  @override
  String get id;
  @override
  String get carId;
  @override
  String get userId;
  @override
  DateTime get date;
  @override
  int get mileage;
  @override
  String get oilType;
  @override
  int get oilEnduranceKm;
  @override
  String get filterType;
  @override
  bool get filterChanged;
  @override
  double get oilCost;
  @override
  double get filterCost;
  @override
  String? get serviceProviderName;
  @override
  String? get notes;
  @override
  @JsonKey(fromJson: _timestampToDateTime)
  DateTime? get createdAt;
  @override
  @JsonKey(fromJson: _timestampToDateTime)
  DateTime? get updatedAt;

  /// Create a copy of OilChangeModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$OilChangeModelImplCopyWith<_$OilChangeModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
