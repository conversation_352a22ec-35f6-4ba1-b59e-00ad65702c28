// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'oil_change_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$OilChangeModelImpl _$$OilChangeModelImplFromJson(Map<String, dynamic> json) =>
    _$OilChangeModelImpl(
      id: json['id'] as String,
      carId: json['carId'] as String,
      userId: json['userId'] as String,
      date: DateTime.parse(json['date'] as String),
      mileage: (json['mileage'] as num).toInt(),
      oilType: json['oilType'] as String,
      oilEnduranceKm: (json['oilEnduranceKm'] as num?)?.toInt() ?? 5000,
      filterType: json['filterType'] as String,
      filterChanged: json['filterChanged'] as bool? ?? false,
      oilCost: (json['oilCost'] as num?)?.toDouble() ?? 0.0,
      filterCost: (json['filterCost'] as num?)?.toDouble() ?? 0.0,
      serviceProviderName: json['serviceProviderName'] as String?,
      notes: json['notes'] as String?,
      createdAt: _timestampToDateTime(json['createdAt']),
      updatedAt: _timestampToDateTime(json['updatedAt']),
    );

Map<String, dynamic> _$$OilChangeModelImplToJson(
        _$OilChangeModelImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'carId': instance.carId,
      'userId': instance.userId,
      'date': instance.date.toIso8601String(),
      'mileage': instance.mileage,
      'oilType': instance.oilType,
      'oilEnduranceKm': instance.oilEnduranceKm,
      'filterType': instance.filterType,
      'filterChanged': instance.filterChanged,
      'oilCost': instance.oilCost,
      'filterCost': instance.filterCost,
      'serviceProviderName': instance.serviceProviderName,
      'notes': instance.notes,
      'createdAt': instance.createdAt?.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
    };
