import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

part 'oil_change_model.freezed.dart';
part 'oil_change_model.g.dart';

DateTime? _timestampToDateTime(dynamic value) {
  if (value == null) return null;
  if (value is Timestamp) return value.toDate();
  if (value is DateTime) return value;
  return null;
}

@freezed
class OilChangeModel with _$OilChangeModel {
  const OilChangeModel._();

  const factory OilChangeModel({
    required String id,
    required String carId,
    required String userId,
    required DateTime date,
    required int mileage,
    required String oilType,
    @Default(5000) int oilEnduranceKm,
    required String filterType,
    @Default(false) bool filterChanged,
    @Default(0.0) double oilCost,
    @Default(0.0) double filterCost,
    String? serviceProviderName,
    String? notes,
    @JsonKey(fromJson: _timestampToDateTime) DateTime? createdAt,
    @JsonKey(fromJson: _timestampToDateTime) DateTime? updatedAt,
  }) = _OilChangeModel;

  factory OilChangeModel.fromJson(Map<String, dynamic> json) =>
      _$OilChangeModelFromJson(json);
}

extension OilChangeModelX on OilChangeModel {
  Map<String, dynamic> toFirestore() {
    final data = toJson();

    // Remove ID as it's stored in the document reference
    data.remove('id');

    // Convert DateTime fields to Firestore Timestamp objects
    if (data.containsKey('date')) {
      data['date'] = Timestamp.fromDate(date);
    }

    if (data.containsKey('createdAt') && data['createdAt'] != null) {
      data['createdAt'] = createdAt != null
          ? Timestamp.fromDate(createdAt!)
          : FieldValue.serverTimestamp();
    } else {
      data['createdAt'] = FieldValue.serverTimestamp();
    }

    if (data.containsKey('updatedAt')) {
      data['updatedAt'] = FieldValue.serverTimestamp();
    }

    return data;
  }
}
