import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../../../../core/utils/firebase_collections.dart';
import '../../domain/models/oil_change_model.dart' show OilChangeModel, OilChangeModelX;
import '../../../../features/car_management/data/repositories/car_repository.dart';

part 'oil_change_repository.g.dart';

@riverpod
class OilChangeRepository extends _$OilChangeRepository {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  @override
  Future<List<OilChangeModel>> build(String carId) async {
    try {
      final user = _auth.currentUser;
      if (user == null) throw Exception('User not authenticated');

      final snapshot = await _firestore
          .collection(FirebaseCollections.oilChanges)
          .where('carId', isEqualTo: carId)
          .where('userId', isEqualTo: user.uid)
          .orderBy('date', descending: true)
          .get();

      return snapshot.docs.map((doc) {
        final data = doc.data();
        final timestamp = data['date'] as Timestamp;
        return OilChangeModel.fromJson({
          ...data,
          'id': doc.id,
          'date': timestamp.toDate().toIso8601String(),
        });
      }).toList();
    } catch (e) {
      throw Exception('Failed to fetch oil changes: $e');
    }
  }

  Future<OilChangeModel> addOilChange(OilChangeModel oilChange) async {
    try {
      final user = _auth.currentUser;
      if (user == null) throw Exception('User not authenticated');

      // Ensure the oil change belongs to the current user
      if (oilChange.userId != user.uid) {
        throw Exception('Oil change does not belong to authenticated user');
      }

      final data = oilChange.toFirestore();

      final docRef = await _firestore
          .collection(FirebaseCollections.oilChanges)
          .add({
            ...data,
            'userId': user.uid,
            'updatedAt': FieldValue.serverTimestamp(),
          });

      // Update car's oil change information
      final carRepository = ref.read(carRepositoryProvider);
      await carRepository.updateCarOilChangeInfo(
        oilChange.carId,
        oilChange.date,
        oilChange.mileage,
      );

      // Refresh the state after adding
      ref.invalidateSelf();
      
      return oilChange.copyWith(id: docRef.id);
    } catch (e) {
      throw Exception('Failed to add oil change: $e');
    }
  }

  Future<void> updateOilChange(OilChangeModel oilChange) async {
    try {
      final user = _auth.currentUser;
      if (user == null) throw Exception('User not authenticated');

      // Ensure the oil change belongs to the current user
      if (oilChange.userId != user.uid) {
        throw Exception('Oil change does not belong to authenticated user');
      }

      final data = oilChange.toFirestore();

      await _firestore
          .collection(FirebaseCollections.oilChanges)
          .doc(oilChange.id)
          .update({
            ...data,
            'updatedAt': FieldValue.serverTimestamp(),
          });

      // Update car's oil change information
      final carRepository = ref.read(carRepositoryProvider);
      await carRepository.updateCarOilChangeInfo(
        oilChange.carId,
        oilChange.date,
        oilChange.mileage,
      );

      // Refresh the state after updating
      ref.invalidateSelf();
    } catch (e) {
      throw Exception('Failed to update oil change: $e');
    }
  }

  Future<void> deleteOilChange(String id) async {
    try {
      final user = _auth.currentUser;
      if (user == null) throw Exception('User not authenticated');

      // Get the oil change to verify ownership
      final doc = await _firestore
          .collection(FirebaseCollections.oilChanges)
          .doc(id)
          .get();

      if (!doc.exists) {
        throw Exception('Oil change not found');
      }

      final data = doc.data()!;
      if (data['userId'] != user.uid) {
        throw Exception('Oil change does not belong to authenticated user');
      }

      await _firestore
          .collection(FirebaseCollections.oilChanges)
          .doc(id)
          .delete();

      // Refresh the state after deleting
      ref.invalidateSelf();
    } catch (e) {
      throw Exception('Failed to delete oil change: $e');
    }
  }
} 