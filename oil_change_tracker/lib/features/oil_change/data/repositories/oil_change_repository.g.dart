// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'oil_change_repository.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$oilChangeRepositoryHash() =>
    r'4477927d412b679500dbd90948ed887275c1bacd';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

abstract class _$OilChangeRepository
    extends BuildlessAutoDisposeAsyncNotifier<List<OilChangeModel>> {
  late final String carId;

  FutureOr<List<OilChangeModel>> build(
    String carId,
  );
}

/// See also [OilChangeRepository].
@ProviderFor(OilChangeRepository)
const oilChangeRepositoryProvider = OilChangeRepositoryFamily();

/// See also [OilChangeRepository].
class OilChangeRepositoryFamily
    extends Family<AsyncValue<List<OilChangeModel>>> {
  /// See also [OilChangeRepository].
  const OilChangeRepositoryFamily();

  /// See also [OilChangeRepository].
  OilChangeRepositoryProvider call(
    String carId,
  ) {
    return OilChangeRepositoryProvider(
      carId,
    );
  }

  @override
  OilChangeRepositoryProvider getProviderOverride(
    covariant OilChangeRepositoryProvider provider,
  ) {
    return call(
      provider.carId,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'oilChangeRepositoryProvider';
}

/// See also [OilChangeRepository].
class OilChangeRepositoryProvider extends AutoDisposeAsyncNotifierProviderImpl<
    OilChangeRepository, List<OilChangeModel>> {
  /// See also [OilChangeRepository].
  OilChangeRepositoryProvider(
    String carId,
  ) : this._internal(
          () => OilChangeRepository()..carId = carId,
          from: oilChangeRepositoryProvider,
          name: r'oilChangeRepositoryProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$oilChangeRepositoryHash,
          dependencies: OilChangeRepositoryFamily._dependencies,
          allTransitiveDependencies:
              OilChangeRepositoryFamily._allTransitiveDependencies,
          carId: carId,
        );

  OilChangeRepositoryProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.carId,
  }) : super.internal();

  final String carId;

  @override
  FutureOr<List<OilChangeModel>> runNotifierBuild(
    covariant OilChangeRepository notifier,
  ) {
    return notifier.build(
      carId,
    );
  }

  @override
  Override overrideWith(OilChangeRepository Function() create) {
    return ProviderOverride(
      origin: this,
      override: OilChangeRepositoryProvider._internal(
        () => create()..carId = carId,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        carId: carId,
      ),
    );
  }

  @override
  AutoDisposeAsyncNotifierProviderElement<OilChangeRepository,
      List<OilChangeModel>> createElement() {
    return _OilChangeRepositoryProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is OilChangeRepositoryProvider && other.carId == carId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, carId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin OilChangeRepositoryRef
    on AutoDisposeAsyncNotifierProviderRef<List<OilChangeModel>> {
  /// The parameter `carId` of this provider.
  String get carId;
}

class _OilChangeRepositoryProviderElement
    extends AutoDisposeAsyncNotifierProviderElement<OilChangeRepository,
        List<OilChangeModel>> with OilChangeRepositoryRef {
  _OilChangeRepositoryProviderElement(super.provider);

  @override
  String get carId => (origin as OilChangeRepositoryProvider).carId;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
