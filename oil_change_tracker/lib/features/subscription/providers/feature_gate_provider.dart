import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/subscription_tier.dart';
import './subscription_provider.dart';
import 'dart:developer' as dev;

/// Enum representing different premium features in the app
enum PremiumFeature {
  /// Voice input for quick data entry
  voiceInput,

  /// Ad-free experience
  adFree,

  /// Unlimited vehicles (free tier limited to 3)
  unlimitedVehicles,

  /// Enhanced analytics and insights
  enhancedAnalytics,

  /// Cloud backup and sync
  cloudBackup,

  /// AI chat assistant for car troubleshooting
  aiChat,
}

/// Cache for feature access decisions to prevent recalculation on every access
class FeatureAccessCache {
  final Map<PremiumFeature, _CachedAccess> _cache = {};
  final Duration _cacheDuration = const Duration(minutes: 5);

  /// Get cached access for a feature
  bool? getCachedAccess(PremiumFeature feature) {
    final cached = _cache[feature];
    if (cached == null) return null;

    // Check if cache is expired
    if (DateTime.now().difference(cached.timestamp) > _cacheDuration) {
      _cache.remove(feature);
      return null;
    }

    return cached.hasAccess;
  }

  /// Cache access for a feature
  void cacheAccess(PremiumFeature feature, bool hasAccess) {
    _cache[feature] = _CachedAccess(
      hasAccess: hasAccess,
      timestamp: DateTime.now(),
    );
  }

  /// Clear all cached access decisions
  void clearCache() {
    _cache.clear();
  }
}

/// Internal class for storing cached access with timestamp
class _CachedAccess {
  final bool hasAccess;
  final DateTime timestamp;

  _CachedAccess({required this.hasAccess, required this.timestamp});
}

/// Provider for the feature access cache
final featureAccessCacheProvider = Provider<FeatureAccessCache>((ref) {
  return FeatureAccessCache();
});

/// Provider to check if a premium feature is available to the current user
final featureGateProvider =
    Provider.family<bool, PremiumFeature>((ref, feature) {
  final subscriptionState = ref.watch(subscriptionProvider);
  final cache = ref.watch(featureAccessCacheProvider);

  // Always clear cache when subscription state changes to ensure fresh access decisions
  cache.clearCache();

  // First check cache to avoid recalculation
  final cachedAccess = cache.getCachedAccess(feature);
  if (cachedAccess != null) {
    return cachedAccess;
  }

  // Handle loading state - give benefit of doubt for better UX
  if (subscriptionState.isLoading) {
    dev.log(
        'Subscription state is loading, providing fallback access for ${feature.name}');
    // During loading, allow access to prevent flickering UI
    // For critical features, you might want to be more restrictive
    final fallbackAccess = _getFallbackAccess(feature);
    return fallbackAccess;
  }

  // If user has an active subscription OR is in trial, all premium features are available
  if (subscriptionState.hasActiveSubscription ||
      subscriptionState.subscription?.isInTrialPeriod() == true) {
    cache.cacheAccess(feature, true);
    return true;
  }

  // Handle error state - provide fallback access based on feature criticality
  if (subscriptionState.error != null) {
    dev.log(
        'Subscription error: ${subscriptionState.error}, providing fallback access for ${feature.name}');
    final fallbackAccess = _getFallbackAccess(feature);
    cache.cacheAccess(feature, fallbackAccess);
    return fallbackAccess;
  }

  // Otherwise, check feature-specific rules
  bool hasAccess = false;

  switch (feature) {
    case PremiumFeature.voiceInput:
      // Voice input is a premium feature
      hasAccess = false;
    case PremiumFeature.adFree:
      // Ad-free is a premium feature
      hasAccess = false;
    case PremiumFeature.unlimitedVehicles:
      // Free users can have up to 3 vehicles
      hasAccess = false;
    case PremiumFeature.enhancedAnalytics:
      // Enhanced analytics is a premium feature
      hasAccess = false;
    case PremiumFeature.cloudBackup:
      // Cloud backup is a premium feature
      hasAccess = false;
    case PremiumFeature.aiChat:
      // AI chat assistant is a premium feature
      hasAccess = false;
  }

  // Cache the result
  cache.cacheAccess(feature, hasAccess);
  return hasAccess;
});

/// Get fallback access for a feature when subscription state has an error
/// This provides graceful degradation instead of blocking all premium features
bool _getFallbackAccess(PremiumFeature feature) {
  switch (feature) {
    // Critical features that should work even if subscription check fails
    case PremiumFeature.unlimitedVehicles:
      return true; // Allow users to access their vehicles even if subscription check fails

    // Non-critical premium features
    case PremiumFeature.voiceInput:
    case PremiumFeature.adFree:
    case PremiumFeature.enhancedAnalytics:
    case PremiumFeature.cloudBackup:
    case PremiumFeature.aiChat:
      return false;
  }
}

/// Provider to get the current subscription tier
final subscriptionTierProvider = Provider<SubscriptionTier>((ref) {
  final subscriptionState = ref.watch(subscriptionProvider);

  if (subscriptionState.hasActiveSubscription) {
    return SubscriptionTier.premium;
  } else {
    return SubscriptionTier.free;
  }
});

/// Provider to check if the user has reached the vehicle limit
final vehicleLimitReachedProvider =
    Provider.family<bool, int>((ref, vehicleCount) {
  final tier = ref.watch(subscriptionTierProvider);

  // Constants for vehicle limits by tier
  const int FREE_TIER_LIMIT = 3;

  switch (tier) {
    case SubscriptionTier.free:
      return vehicleCount >= FREE_TIER_LIMIT;
    case SubscriptionTier.premium:
      return false; // Premium tier has unlimited vehicles
  }
});

/// Provider to get the maximum number of vehicles allowed for the current tier
final maxVehiclesAllowedProvider = Provider<int>((ref) {
  final tier = ref.watch(subscriptionTierProvider);

  switch (tier) {
    case SubscriptionTier.free:
      return 3;
    case SubscriptionTier.premium:
      return 999; // Effectively unlimited
  }
});
