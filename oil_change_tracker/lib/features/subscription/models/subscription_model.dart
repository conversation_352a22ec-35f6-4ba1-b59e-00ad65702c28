import 'package:cloud_firestore/cloud_firestore.dart';
import 'subscription_tier.dart';

/// Model representing a user's subscription
class SubscriptionModel {
  /// User ID associated with the subscription
  final String userId;

  /// Subscription tier
  final SubscriptionTier tier;

  /// Date when the subscription started
  final DateTime startDate;

  /// Date when the subscription expires
  final DateTime? expiryDate;

  /// Whether the subscription is currently active
  final bool isActive;

  /// Whether the subscription will automatically renew
  final bool autoRenew;

  /// Whether this is a trial subscription
  final bool isTrial;

  /// Whether the user has had a free trial before
  final bool hadFreeTrial;

  /// Token from the purchase
  final String? purchaseToken;

  /// Platform where the subscription was purchased (android, ios)
  final String? platform;

  /// Date when the subscription was cancelled
  final DateTime? cancelDate;

  /// Original transaction ID (for iOS)
  final String? originalTransactionId;

  /// Create a new subscription model
  const SubscriptionModel({
    required this.userId,
    required this.tier,
    required this.startDate,
    this.expiryDate,
    this.isActive = false,
    this.autoRenew = false,
    this.isTrial = false,
    this.hadFreeTrial = false,
    this.purchaseToken,
    this.platform,
    this.cancelDate,
    this.originalTransactionId,
  });

  /// Create from JSON
  factory SubscriptionModel.fromJson(Map<String, dynamic> json) {
    return SubscriptionModel(
      userId: json['userId'] as String,
      tier: SubscriptionTier.values.firstWhere(
        (e) => e.toString() == 'SubscriptionTier.${json['tier']}',
        orElse: () => SubscriptionTier.free,
      ),
      startDate: (json['startDate'] as Timestamp).toDate(),
      expiryDate: json['expiryDate'] != null
          ? (json['expiryDate'] as Timestamp).toDate()
          : null,
      isActive: json['isActive'] as bool? ?? false,
      autoRenew: json['autoRenew'] as bool? ?? false,
      isTrial: json['isTrial'] as bool? ?? false,
      hadFreeTrial: json['hadFreeTrial'] as bool? ?? false,
      purchaseToken: json['purchaseToken'] as String?,
      platform: json['platform'] as String?,
      cancelDate: json['cancelDate'] != null
          ? (json['cancelDate'] as Timestamp).toDate()
          : null,
      originalTransactionId: json['originalTransactionId'] as String?,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'tier': tier.toString().split('.').last,
      'startDate': startDate,
      'expiryDate': expiryDate,
      'isActive': isActive,
      'autoRenew': autoRenew,
      'isTrial': isTrial,
      'hadFreeTrial': hadFreeTrial,
      'purchaseToken': purchaseToken,
      'platform': platform,
      'cancelDate': cancelDate,
      'originalTransactionId': originalTransactionId,
    };
  }

  /// Check if the subscription is active
  bool isActiveNow() {
    if (!isActive) return false;
    if (expiryDate == null) return false;
    return expiryDate!.isAfter(DateTime.now());
  }

  /// Check if the subscription is in trial period
  bool isInTrialPeriod() {
    return isTrial && isActiveNow();
  }

  /// Get days remaining in the subscription
  int get daysRemaining {
    if (expiryDate == null) return 0;

    final now = DateTime.now();
    if (expiryDate!.isBefore(now)) return 0;

    return expiryDate!.difference(now).inDays;
  }

  /// Create a copy of this subscription model with some fields replaced
  SubscriptionModel copyWith({
    String? userId,
    SubscriptionTier? tier,
    DateTime? startDate,
    DateTime? expiryDate,
    bool? isActive,
    bool? autoRenew,
    bool? isTrial,
    bool? hadFreeTrial,
    String? purchaseToken,
    String? platform,
    DateTime? cancelDate,
    String? originalTransactionId,
  }) {
    return SubscriptionModel(
      userId: userId ?? this.userId,
      tier: tier ?? this.tier,
      startDate: startDate ?? this.startDate,
      expiryDate: expiryDate ?? this.expiryDate,
      isActive: isActive ?? this.isActive,
      autoRenew: autoRenew ?? this.autoRenew,
      isTrial: isTrial ?? this.isTrial,
      hadFreeTrial: hadFreeTrial ?? this.hadFreeTrial,
      purchaseToken: purchaseToken ?? this.purchaseToken,
      platform: platform ?? this.platform,
      cancelDate: cancelDate ?? this.cancelDate,
      originalTransactionId:
          originalTransactionId ?? this.originalTransactionId,
    );
  }
}
