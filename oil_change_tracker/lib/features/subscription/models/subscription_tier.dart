/// Enum representing the different subscription tiers
enum SubscriptionTier {
  /// Free tier with trial features (7 days with premium caps)
  free,

  /// Premium tier with advanced features and no ads
  premium;

  /// Get the display name of the tier
  String getDisplayName() {
    switch (this) {
      case SubscriptionTier.free:
        return 'Free Trial';
      case SubscriptionTier.premium:
        return 'Premium';
    }
  }

  /// Voice commands allowance per monthly cycle
  /// Uses remote config values when provided, otherwise defaults
  int getCapVoice({
    int? trialVoiceCap,
    int? premiumVoiceCap,
  }) {
    switch (this) {
      case SubscriptionTier.free:
        return trialVoiceCap ?? 75;
      case SubscriptionTier.premium:
        return premiumVoiceCap ?? 75;
    }
  }

  /// Chat turns allowance per monthly cycle
  /// Uses remote config values when provided, otherwise defaults
  int getCapChat({
    int? trialChatCap,
    int? premiumChatCap,
  }) {
    switch (this) {
      case SubscriptionTier.free:
        return trialChatCap ?? 150;
      case SubscriptionTier.premium:
        return premiumChatCap ?? 150;
    }
  }

  /// Voice commands allowance per monthly cycle (backward compatibility)
  int get capVoice => getCapVoice();

  /// Chat turns allowance per monthly cycle (backward compatibility)
  int get capChat => getCapChat();

  /// Get trial duration in days from remote config
  static int getTrialDays() {
    return 7; // Assuming a default value since the remote config is no longer used
  }
}

/// Extension to provide additional functionality for SubscriptionTier
extension SubscriptionTierExtension on SubscriptionTier {
  /// Get the display name of the tier
  @Deprecated('Use getDisplayName() method directly on the enum instead')
  String get displayName {
    switch (this) {
      case SubscriptionTier.free:
        return 'Free';
      case SubscriptionTier.premium:
        return 'Premium';
    }
  }

  /// Get the monthly price of the tier in USD
  double get monthlyPrice {
    switch (this) {
      case SubscriptionTier.free:
        return 0.0;
      case SubscriptionTier.premium:
        return 2.99;
    }
  }

  /// Get the yearly price of the tier in USD
  double get yearlyPrice {
    switch (this) {
      case SubscriptionTier.free:
        return 0.0;
      case SubscriptionTier.premium:
        return 24.99;
    }
  }

  /// Get the features included in this tier
  List<String> get features {
    switch (this) {
      case SubscriptionTier.free:
        return [
          'Basic oil change tracking',
          'Manual data entry',
          'Ad-supported experience',
          'Limited to 3 vehicles',
        ];
      case SubscriptionTier.premium:
        return [
          'Voice input for quick data entry',
          'AI chat assistant for car troubleshooting',
          'Ad-free experience',
          'Unlimited vehicles',
          'Enhanced analytics and insights',
          'Cloud backup and sync',
          'Priority support',
        ];
    }
  }
}
