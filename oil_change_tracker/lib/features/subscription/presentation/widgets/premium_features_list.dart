import 'package:flutter/material.dart';
import '../../../../core/theme/theme_extensions.dart';
import '../../../../generated/app_localizations.dart';

/// Centralized widget for displaying premium features consistently across the app
class PremiumFeaturesList extends StatelessWidget {
  final bool showTitle;
  final bool compact;
  
  const PremiumFeaturesList({
    super.key,
    this.showTitle = true,
    this.compact = false,
  });

  @override
  Widget build(BuildContext context) {
    final l10n = S.of(context);
    
    final features = [
      {
        'icon': Icons.mic_rounded,
        'text': 'Voice input for quick data entry',
      },
      {
        'icon': Icons.chat_bubble_rounded,
        'text': 'AI chat assistant for car troubleshooting',
      },
      {
        'icon': Icons.block_flipped,
        'text': 'Ad-free experience',
      },
      {
        'icon': Icons.directions_car_rounded,
        'text': 'Unlimited vehicles',
      },
      {
        'icon': Icons.analytics_rounded,
        'text': 'Enhanced analytics and insights',
      },
      {
        'icon': Icons.cloud_upload_rounded,
        'text': 'Cloud backup and sync',
      },
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (showTitle) ...[
          Text(
            l10n.premiumFeatures,
            style: TextStyle(
              fontSize: compact ? 16 : 18,
              fontWeight: FontWeight.bold,
              color: context.primaryTextColor,
            ),
          ),
          SizedBox(height: compact ? 8 : 12),
        ],
        ...features.map((feature) => Padding(
          padding: EdgeInsets.only(bottom: compact ? 8 : 12),
          child: Row(
            children: [
              Icon(
                feature['icon'] as IconData,
                size: compact ? 16 : 18,
                color: context.accentColor,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  feature['text'] as String,
                  style: TextStyle(
                    fontSize: compact ? 14 : 16,
                    color: context.primaryTextColor,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        )),
      ],
    );
  }
}

/// Simple premium features list for dialogs
class SimplePremiumFeaturesList extends StatelessWidget {
  const SimplePremiumFeaturesList({super.key});

  @override
  Widget build(BuildContext context) {
    final features = [
      'Voice input for quick data entry',
      'AI chat assistant for car troubleshooting',
      'Ad-free experience',
      'Unlimited vehicles',
      'Enhanced analytics and insights',
      'Cloud backup and sync',
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: features.map((feature) => Padding(
        padding: const EdgeInsets.only(bottom: 4),
        child: Row(
          children: [
            const Icon(Icons.check_circle, size: 16),
            const SizedBox(width: 8),
            Expanded(child: Text(feature)),
          ],
        ),
      )).toList(),
    );
  }
}
