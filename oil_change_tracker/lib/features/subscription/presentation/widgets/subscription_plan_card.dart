import 'package:flutter/material.dart';
import '../../models/subscription_tier.dart';
import '../../../../core/theme/theme_extensions.dart';

class SubscriptionPlanCard extends StatelessWidget {
  final SubscriptionTier tier;
  final bool isYearly;
  final bool isActive;
  final VoidCallback onSelect;

  const SubscriptionPlanCard({
    super.key,
    required this.tier,
    required this.isYearly,
    required this.isActive,
    required this.onSelect,
  });

  @override
  Widget build(BuildContext context) {
    final price = isYearly ? tier.yearlyPrice : tier.monthlyPrice;
    final features = tier.features;

    // Fallback strings for localization
    const String currentText = 'Current';
    const String freeText = 'Free';
    const String currentPlanText = 'Current Plan';
    const String subscribeText = 'Subscribe';
    const String selectPlanText = 'Select Plan';

    return Container(
      decoration: BoxDecoration(
        color: context.containerBackgroundColor,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: isActive ? context.accentColor : Colors.grey.withOpacity(0.3),
          width: isActive ? 2 : 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with tier name and price
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // Tier name with badge
              Row(
                children: [
                  Text(
                    tier.getDisplayName(),
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                  if (isActive) ...[
                    const SizedBox(width: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: context.accentColor,
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: const Text(
                        currentText,
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ],
              ),

              // Price
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  if (price > 0) ...[
                    RichText(
                      text: TextSpan(
                        style: DefaultTextStyle.of(context).style,
                        children: [
                          TextSpan(
                            text: '\$${price.toStringAsFixed(2)}',
                            style: Theme.of(context)
                                .textTheme
                                .titleLarge
                                ?.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: context.accentColor,
                                ),
                          ),
                          TextSpan(
                            text: isYearly ? '/year' : '/month',
                            style: Theme.of(context).textTheme.bodySmall,
                          ),
                        ],
                      ),
                    ),
                  ] else ...[
                    Text(
                      freeText,
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: context.accentColor,
                          ),
                    ),
                  ],
                ],
              ),
            ],
          ),

          const SizedBox(height: 16),
          const Divider(),
          const SizedBox(height: 16),

          // Features list
          ...features.map((feature) => Padding(
                padding: const EdgeInsets.only(bottom: 12),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Icon(
                      Icons.check_circle,
                      color: context.accentColor,
                      size: 20,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        feature,
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                    ),
                  ],
                ),
              )),

          const SizedBox(height: 16),

          // Action button
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: isActive ? null : onSelect,
              style: ElevatedButton.styleFrom(
                backgroundColor: context.accentColor,
                disabledBackgroundColor: Colors.grey.withOpacity(0.3),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Text(
                isActive
                    ? currentPlanText
                    : (price > 0 ? subscribeText : selectPlanText),
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
