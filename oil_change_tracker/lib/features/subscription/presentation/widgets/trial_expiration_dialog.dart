import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/theme/theme_extensions.dart';
import '../../../../generated/app_localizations.dart';

class TrialExpirationDialog extends StatelessWidget {
  final bool hasExpired;
  final int? daysRemaining;

  const TrialExpirationDialog({
    super.key,
    required this.hasExpired,
    this.daysRemaining,
  });

  @override
  Widget build(BuildContext context) {
    final l10n = S.of(context);

    return AlertDialog(
      backgroundColor: context.containerBackgroundColor,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      title: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: hasExpired
                  ? Colors.red.withOpacity(0.1)
                  : context.accentColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              hasExpired ? Icons.warning : Icons.timer,
              color: hasExpired ? Colors.red : context.accentColor,
              size: 24,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              hasExpired ? 'Trial Expired' : 'Trial Ending Soon',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: hasExpired ? Colors.red : context.accentColor,
                  ),
            ),
          ),
        ],
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            hasExpired
                ? 'Your 7-day premium trial has ended. Upgrade to continue enjoying unlimited AI features.'
                : 'Your premium trial expires in $daysRemaining day${daysRemaining == 1 ? '' : 's'}. Upgrade now to keep your premium features.',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  height: 1.4,
                ),
          ),
          const SizedBox(height: 16),

          // Features reminder
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: context.accentColor.withOpacity(0.05),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: context.accentColor.withOpacity(0.2),
                width: 1,
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Premium Features:',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: context.accentColor,
                      ),
                ),
                const SizedBox(height: 8),
                _buildFeatureItem(context, '75 Voice Commands per month'),
                _buildFeatureItem(context, '150 AI Chat Messages per month'),
                _buildFeatureItem(context, 'Ad-free experience'),
              ],
            ),
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: Text(
            hasExpired ? 'Continue with Free' : 'Maybe Later',
            style: TextStyle(
              color: Theme.of(context)
                  .textTheme
                  .bodyMedium
                  ?.color
                  ?.withOpacity(0.7),
            ),
          ),
        ),
        ElevatedButton(
          onPressed: () {
            Navigator.of(context).pop();
            context.push('/subscription');
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: context.accentColor,
            foregroundColor: Colors.white,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          child: Text(
            hasExpired ? 'Upgrade Now' : 'Upgrade to Premium',
            style: const TextStyle(fontWeight: FontWeight.w600),
          ),
        ),
      ],
    );
  }

  Widget _buildFeatureItem(BuildContext context, String feature) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          Icon(
            Icons.check_circle,
            size: 16,
            color: context.accentColor,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              feature,
              style: Theme.of(context).textTheme.bodySmall,
            ),
          ),
        ],
      ),
    );
  }

  /// Show trial expiration dialog
  static Future<void> show(
    BuildContext context, {
    required bool hasExpired,
    int? daysRemaining,
  }) {
    return showDialog(
      context: context,
      barrierDismissible: !hasExpired, // Force interaction if expired
      builder: (context) => TrialExpirationDialog(
        hasExpired: hasExpired,
        daysRemaining: daysRemaining,
      ),
    );
  }
}
