import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import '../../../../core/theme/theme_extensions.dart';
import '../../../../core/widgets/unified_app_bar.dart';
import '../../../../generated/app_localizations.dart';
import '../../models/subscription_tier.dart';
import '../../providers/subscription_provider.dart';
import '../../services/subscription_service.dart';
import 'dart:developer' as dev;

/// Screen for managing subscriptions and displaying premium options
class SubscriptionScreen extends ConsumerWidget {
  const SubscriptionScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final l10n = S.of(context);
    final subscriptionState = ref.watch(subscriptionProvider);

    return Scaffold(
      appBar: AppBarFactory.standard(
        title: l10n.subscriptions,
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header section with title and description
                Text(
                  l10n.unlockPremiumFeatures,
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: context.primaryTextColor,
                      ),
                ),
                const SizedBox(height: 12),
                Text(
                  l10n.premiumRemoveAdsDescription,
                  style: TextStyle(
                    fontSize: 16,
                    color: context.secondaryTextColor,
                  ),
                ),
                const SizedBox(height: 24),

                // Trial status (if user is in trial)
                _buildTrialStatus(context, ref, l10n),

                // Subscription plans
                _buildSubscriptionPlans(context, ref, l10n),

                const SizedBox(height: 24),

                // Premium features section
                _buildFeaturesSection(context),

                const SizedBox(height: 24),

                // Restore purchases button
                _buildRestorePurchasesButton(context, ref),

                // Add extra space at the bottom for scrolling
                const SizedBox(height: 32),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTrialStatus(BuildContext context, WidgetRef ref, S l10n) {
    final subscriptionState = ref.watch(subscriptionProvider);
    final subscription = subscriptionState.subscription;

    // Only show if user is in trial
    if (subscription?.isTrial != true) {
      return const SizedBox.shrink();
    }

    final expiryDate = subscription?.expiryDate;
    final daysRemaining = expiryDate?.difference(DateTime.now()).inDays ?? 0;

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      margin: const EdgeInsets.only(bottom: 24),
      decoration: BoxDecoration(
        color: context.accentColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: context.accentColor.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.card_giftcard,
                color: context.accentColor,
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                'Free Trial Active',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: context.accentColor,
                    ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            daysRemaining > 0
                ? 'Your trial expires in $daysRemaining day${daysRemaining == 1 ? '' : 's'}. Upgrade now to continue enjoying premium features.'
                : 'Your trial has expired. Upgrade now to continue enjoying premium features.',
            style: Theme.of(context).textTheme.bodyMedium,
          ),
          if (expiryDate != null) ...[
            const SizedBox(height: 4),
            Text(
              'Trial ends: ${DateFormat('MMM dd, yyyy').format(expiryDate)}',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: context.secondaryTextColor,
                  ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildSubscriptionPlans(BuildContext context, WidgetRef ref, S l10n) {
    return FutureBuilder(
      future: _loadSubscriptionPrices(ref),
      builder: (context, snapshot) {
        final isLoading = snapshot.connectionState == ConnectionState.waiting;
        final priceData = snapshot.data;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              l10n.chooseYourPlan,
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: context.primaryTextColor,
              ),
            ),
            const SizedBox(height: 16),
            _buildSubscriptionCard(
              context,
              title: l10n.monthlyPremium,
              price: isLoading ? '...' : priceData?['monthlyPrice'] ?? '...',
              period: l10n.perMonth,
              isPopular: false,
              discount: null,
              onSubscribe: () => _subscribe(context, ref, true),
              isLoading: isLoading,
              basePrice:
                  isLoading ? '...' : priceData?['monthlyPrice'] ?? '...',
            ),
            const SizedBox(height: 16),
            _buildSubscriptionCard(
              context,
              title: l10n.annualPremium,
              price: isLoading ? '...' : priceData?['yearlyPrice'] ?? '...',
              period: l10n.perYear,
              isPopular: true,
              discount: '${l10n.save} 17%',
              onSubscribe: () => _subscribe(context, ref, false),
              isLoading: isLoading,
              basePrice: isLoading ? '...' : priceData?['yearlyPrice'] ?? '...',
              monthlyEquivalent: isLoading
                  ? ''
                  : '(${priceData?['yearlyMonthlyEquivalent'] ?? ''} ${l10n.perMonth})',
            ),
          ],
        );
      },
    );
  }

  Future<Map<String, String>?> _loadSubscriptionPrices(WidgetRef ref) async {
    try {
      final service = ref.read(subscriptionServiceProvider);

      // Force refresh product details to get latest price changes
      dev.log('SubscriptionScreen: Starting refreshProducts...');
      await service.refreshProducts();
      dev.log('SubscriptionScreen: refreshProducts completed');

      // Use the same method that refreshProducts() affects
      dev.log('SubscriptionScreen: Getting monthly price...');
      final mPrice =
          await service.getFormattedPrice(SubscriptionTier.premium, true);
      dev.log('SubscriptionScreen: Monthly price received: $mPrice');

      dev.log('SubscriptionScreen: Getting yearly price...');
      final yPrice =
          await service.getFormattedPrice(SubscriptionTier.premium, false);
      dev.log('SubscriptionScreen: Yearly price received: $yPrice');

      final result = {
        'monthlyPrice': mPrice,
        'yearlyPrice': yPrice,
        'yearlyMonthlyEquivalent': _calculateMonthlyEquivalent(yPrice),
      };
      dev.log('SubscriptionScreen: Final price map: $result');
      return result;
    } catch (e) {
      dev.log('SubscriptionScreen: Error loading prices: $e');
      return null;
    }
  }

  String _calculateMonthlyEquivalent(String yearlyPrice) {
    try {
      // Extract numeric value from price string
      final cleanPrice = yearlyPrice.replaceAll(RegExp(r'[^0-9.]'), '');
      final yearlyAmount = double.tryParse(cleanPrice);

      if (yearlyAmount != null) {
        final monthlyEquivalent = yearlyAmount / 12;
        // Try to preserve currency symbol from original price
        final currencyMatch = RegExp(r'[^\d\.,\s]+').firstMatch(yearlyPrice);
        final currency = currencyMatch?.group(0) ?? '';

        return '$currency${monthlyEquivalent.toStringAsFixed(2)}';
      }
    } catch (e) {
      dev.log('Error calculating monthly equivalent: $e');
    }

    return '...';
  }

  Future<void> _subscribe(
      BuildContext context, WidgetRef ref, bool monthly) async {
    try {
      final result = await ref
          .read(subscriptionProvider.notifier)
          .purchase(SubscriptionTier.premium, monthly: monthly);

      // Show error only if SubscriptionNotifier populated an error message
      final subscriptionState = ref.read(subscriptionProvider);
      final err = subscriptionState.error;
      if (err != null && err.isNotEmpty) {
        if (!context.mounted) return;
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(err),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
            margin: const EdgeInsets.all(16),
          ),
        );
      }
    } catch (e) {
      if (!context.mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(S.of(context).errorOccurred),
          backgroundColor: Colors.red,
          behavior: SnackBarBehavior.floating,
          margin: const EdgeInsets.all(16),
        ),
      );
    }
  }

  Future<void> _restorePurchases(BuildContext context, WidgetRef ref) async {
    try {
      final result =
          await ref.read(subscriptionProvider.notifier).restorePurchases();

      if (!context.mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            result
                ? S.of(context).purchasesRestored
                : S.of(context).noPurchasesFound,
          ),
          backgroundColor: result ? Colors.green : Colors.orange,
          behavior: SnackBarBehavior.floating,
          margin: const EdgeInsets.all(16),
        ),
      );
    } catch (e) {
      if (!context.mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(S.of(context).errorOccurred),
          backgroundColor: Colors.red,
          behavior: SnackBarBehavior.floating,
          margin: const EdgeInsets.all(16),
        ),
      );
    }
  }

  Widget _buildSubscriptionCard(
    BuildContext context, {
    required String title,
    required String price,
    required String period,
    required bool isPopular,
    required String? discount,
    required VoidCallback onSubscribe,
    required bool isLoading,
    required String basePrice,
    String? monthlyEquivalent,
  }) {
    return Card(
      elevation: isPopular ? 4 : 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: isPopular
            ? BorderSide(color: context.accentColor, width: 2)
            : BorderSide.none,
      ),
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          color: isPopular
              ? context.accentColor.withOpacity(0.05)
              : context.containerBackgroundColor,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (isPopular)
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                decoration: BoxDecoration(
                  color: context.accentColor,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  'Best Value',
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 12,
                  ),
                ),
              ),
            if (isPopular) const SizedBox(height: 12),
            Text(
              title,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: context.primaryTextColor,
              ),
            ),
            const SizedBox(height: 12),
            Wrap(
              crossAxisAlignment: WrapCrossAlignment.end,
              spacing: 4,
              children: [
                if (isLoading)
                  SizedBox(
                    width: 24,
                    height: 24,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      color: context.accentColor,
                    ),
                  )
                else
                  Text(
                    price,
                    style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: context.accentColor,
                        ),
                  ),
                Padding(
                  padding: const EdgeInsets.only(bottom: 4),
                  child: Text(
                    period,
                    style: TextStyle(
                      color: context.secondaryTextColor,
                      fontSize: 16,
                    ),
                  ),
                ),
              ],
            ),

            // Base price information
            Text(
              S.of(context).basePrice(basePrice),
              style: TextStyle(
                fontSize: 14,
                color: context.secondaryTextColor,
              ),
            ),

            // Monthly equivalent for yearly plan
            if (monthlyEquivalent != null)
              Padding(
                padding: const EdgeInsets.only(top: 4),
                child: Text(
                  S.of(context).monthlyEquivalent(monthlyEquivalent),
                  style: TextStyle(
                    fontSize: 14,
                    color: context.secondaryTextColor,
                  ),
                ),
              ),

            if (discount != null) ...[
              const SizedBox(height: 8),
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: Colors.green,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  discount,
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 12,
                  ),
                ),
              ),
            ],
            const SizedBox(height: 24),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: onSubscribe,
                style: ElevatedButton.styleFrom(
                  backgroundColor: context.accentColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  elevation: 0,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: Text(
                  S.of(context).subscribe,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeaturesSection(BuildContext context) {
    final l10n = S.of(context);
    final features = [
      {'icon': Icons.mic_rounded, 'text': l10n.voiceCommands},
      {'icon': Icons.chat_bubble_rounded, 'text': 'AI Chat Assistant'},
      {'icon': Icons.block_flipped, 'text': l10n.adFreeExperience},
      {'icon': Icons.directions_car_rounded, 'text': l10n.unlimitedVehicles},
      {'icon': Icons.cloud_upload_rounded, 'text': l10n.cloudBackup},
      {'icon': Icons.analytics_rounded, 'text': l10n.enhancedAnalytics},
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(left: 4.0, bottom: 16.0),
          child: Text(
            l10n.premiumFeaturesTitle,
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: context.primaryTextColor,
            ),
          ),
        ),
        Container(
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: context.containerBackgroundColor,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: context.accentColor.withOpacity(0.15),
              width: 1,
            ),
          ),
          child: Column(
            children: features.map((feature) {
              return Padding(
                padding: const EdgeInsets.only(bottom: 20),
                child: Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: context.accentColor.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Icon(
                        feature['icon'] as IconData,
                        size: 24,
                        color: context.accentColor,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Text(
                        feature['text'] as String,
                        style: TextStyle(
                          fontSize: 16,
                          color: context.primaryTextColor,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.all(6),
                      decoration: BoxDecoration(
                        color: Colors.green.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Icon(
                        Icons.check_circle_rounded,
                        size: 20,
                        color: Colors.green,
                      ),
                    ),
                  ],
                ),
              );
            }).toList(),
          ),
        ),
      ],
    );
  }

  Widget _buildRestorePurchasesButton(BuildContext context, WidgetRef ref) {
    final l10n = S.of(context);

    return Center(
      child: OutlinedButton.icon(
        onPressed: () => _restorePurchases(context, ref),
        icon: Icon(
          Icons.restore,
          color: context.secondaryAccentColor,
          size: 20,
        ),
        label: Text(
          l10n.restorePurchases,
          style: TextStyle(
            color: context.secondaryAccentColor,
            fontWeight: FontWeight.w500,
          ),
        ),
        style: OutlinedButton.styleFrom(
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
          side: BorderSide(color: context.secondaryAccentColor),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      ),
    );
  }
}
