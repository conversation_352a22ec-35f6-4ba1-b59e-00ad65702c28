import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import '../../../../core/theme/theme_extensions.dart';
import '../../../../generated/app_localizations.dart';
import '../../providers/feature_gate_provider.dart';
import '../../services/cloud_backup_service.dart';
import './subscription_screen.dart';

/// Screen for managing cloud backups and restores
class CloudBackupScreen extends ConsumerStatefulWidget {
  const CloudBackupScreen({super.key});

  @override
  ConsumerState<CloudBackupScreen> createState() => _CloudBackupScreenState();
}

class _CloudBackupScreenState extends ConsumerState<CloudBackupScreen> {
  bool _isLoading = false;
  List<Map<String, dynamic>> _backups = [];
  bool _automaticBackupsEnabled = false;

  @override
  void initState() {
    super.initState();
    _loadBackups();
  }

  Future<void> _loadBackups() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Check if user has cloud backup access
      final hasAccess =
          ref.read(featureGateProvider(PremiumFeature.cloudBackup));
      if (!hasAccess) {
        setState(() {
          _isLoading = false;
        });
        return;
      }

      // Get available backups
      final backups =
          await ref.read(cloudBackupServiceProvider).getAvailableBackups();
      setState(() {
        _backups = backups;
      });
    } catch (e) {
      // Handle error
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error loading backups: $e'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _createBackup() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final success =
          await ref.read(cloudBackupServiceProvider).backupUserData();
      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(S.of(context).backupSuccessful),
            backgroundColor: Colors.green,
          ),
        );
        await _loadBackups();
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(S.of(context).backupFailed),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error creating backup: $e'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _restoreBackup() async {
    // Show confirmation dialog
    final confirm = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(S.of(context).confirmRestore),
        content: Text(S.of(context).restoreWarning),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: Text(S.of(context).cancel),
          ),
          FilledButton(
            onPressed: () => Navigator.pop(context, true),
            child: Text(S.of(context).restore),
          ),
        ],
      ),
    );

    if (confirm != true) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final success =
          await ref.read(cloudBackupServiceProvider).restoreFromLatestBackup();
      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(S.of(context).restoreSuccessful),
            backgroundColor: Colors.green,
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(S.of(context).restoreFailed),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error restoring backup: $e'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _toggleAutomaticBackups(bool value) async {
    setState(() {
      _automaticBackupsEnabled = value;
      _isLoading = true;
    });

    try {
      if (value) {
        await ref.read(cloudBackupServiceProvider).enableAutomaticBackups();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(S.of(context).automaticBackupsEnabled),
            backgroundColor: Colors.green,
          ),
        );
      } else {
        // Implement disable automatic backups
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(S.of(context).automaticBackupsDisabled),
            backgroundColor: Colors.orange,
          ),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error toggling automatic backups: $e'),
          backgroundColor: Colors.red,
        ),
      );
      // Revert the switch
      setState(() {
        _automaticBackupsEnabled = !value;
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final l10n = S.of(context);
    final hasAccess =
        ref.watch(featureGateProvider(PremiumFeature.cloudBackup));

    return Scaffold(
      appBar: AppBar(
        title: Text(l10n.cloudBackup),
        backgroundColor: context.containerBackgroundColor,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : !hasAccess
              ? _buildSubscriptionPrompt(context)
              : _buildBackupContent(context),
    );
  }

  Widget _buildSubscriptionPrompt(BuildContext context) {
    final l10n = S.of(context);

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.cloud_off,
              size: 64,
              color: context.secondaryAccentColor,
            ),
            const SizedBox(height: 16),
            Text(
              l10n.cloudBackupPremiumFeature,
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            Text(
              l10n.cloudBackupDescription,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            FilledButton(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(builder: (_) => const SubscriptionScreen()),
                );
              },
              child: Text(l10n.upgradeNow),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBackupContent(BuildContext context) {
    final l10n = S.of(context);

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Automatic backup toggle
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    l10n.automaticBackups,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(l10n.automaticBackupsDescription),
                  const SizedBox(height: 16),
                  SwitchListTile(
                    title: Text(l10n.enableAutomaticBackups),
                    value: _automaticBackupsEnabled,
                    onChanged: _toggleAutomaticBackups,
                    secondary: const Icon(Icons.schedule),
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Manual backup and restore
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    l10n.manualBackup,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      ElevatedButton.icon(
                        onPressed: _createBackup,
                        icon: const Icon(Icons.backup),
                        label: Text(l10n.createBackup),
                      ),
                      ElevatedButton.icon(
                        onPressed: _backups.isEmpty ? null : _restoreBackup,
                        icon: const Icon(Icons.restore),
                        label: Text(l10n.restore),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Backup history
          Text(
            l10n.backupHistory,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),

          Expanded(
            child: _backups.isEmpty
                ? Center(
                    child: Text(l10n.noBackupsFound),
                  )
                : ListView.builder(
                    itemCount: _backups.length,
                    itemBuilder: (context, index) {
                      final backup = _backups[index];
                      final timestamp = DateTime.parse(backup['timestamp']);
                      final formattedDate =
                          DateFormat.yMMMd().add_Hm().format(timestamp);

                      return ListTile(
                        leading: const Icon(Icons.backup),
                        title: Text(formattedDate),
                        subtitle: Text(
                          '${l10n.cars}: ${backup['car_count']} | ${l10n.oilChanges}: ${backup['oil_change_count']}',
                        ),
                        trailing: IconButton(
                          icon: const Icon(Icons.restore),
                          onPressed: () {
                            // Implement restore from specific backup
                          },
                        ),
                      );
                    },
                  ),
          ),
        ],
      ),
    );
  }
}
