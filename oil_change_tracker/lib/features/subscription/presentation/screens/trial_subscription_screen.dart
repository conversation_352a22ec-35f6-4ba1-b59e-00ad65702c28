import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/theme/theme_extensions.dart';
import '../../models/subscription_tier.dart';
import '../../providers/subscription_provider.dart';
import '../../services/subscription_service.dart';
import '../../../../generated/app_localizations.dart';
import 'dart:developer' as dev;

class TrialSubscriptionScreen extends ConsumerStatefulWidget {
  const TrialSubscriptionScreen({super.key});

  @override
  ConsumerState<TrialSubscriptionScreen> createState() =>
      _TrialSubscriptionScreenState();
}

class _TrialSubscriptionScreenState
    extends ConsumerState<TrialSubscriptionScreen> {
  SubscriptionTier _selectedTier = SubscriptionTier.premium;
  bool _isLoading = false;
  bool _hasAgreed = false;
  bool _priceLoading = true;
  String _monthlyPrice = '...';

  @override
  void initState() {
    super.initState();
    _fetchPrice();
  }

  Future<void> _fetchPrice() async {
    try {
      final service = ref.read(subscriptionServiceProvider);
      await service.refreshProducts();
      final price =
          await service.getFormattedPrice(SubscriptionTier.premium, true);
      if (mounted) {
        setState(() {
          _monthlyPrice = price;
          _priceLoading = false;
        });
      }
    } catch (e) {
      dev.log('Error fetching price in trial screen: $e');
      if (mounted) {
        setState(() {
          _monthlyPrice = '2.99 EGP';
          _priceLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(
          s.freeTrialTitle,
          style: TextStyle(
            color: context.accentColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: context.containerBackgroundColor,
        leading: BackButton(
          color: context.accentColor,
          onPressed: () => context.pop(),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: context.accentColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(16),
              ),
              child: Column(
                children: [
                  Icon(
                    Icons.card_giftcard,
                    size: 48,
                    color: context.accentColor,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    s.tryPremiumFeaturesFree,
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    s.freeTrialDescription,
                    style: Theme.of(context).textTheme.bodyMedium,
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),

            const SizedBox(height: 24),

            // Plan selection
            Text(
              s.selectPlanToTry,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 16),

            // Premium option
            _buildPlanOption(
              context,
              tier: SubscriptionTier.premium,
              isSelected: _selectedTier == SubscriptionTier.premium,
              onTap: () =>
                  setState(() => _selectedTier = SubscriptionTier.premium),
            ),

            const SizedBox(height: 24),

            // Terms agreement
            Row(
              children: [
                Checkbox(
                  value: _hasAgreed,
                  onChanged: (value) {
                    setState(() {
                      _hasAgreed = value ?? false;
                    });
                  },
                  activeColor: context.accentColor,
                ),
                Expanded(
                  child: Text(
                    s.freeTrialAgreement,
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 24),

            // Start trial button
            ElevatedButton(
              onPressed: _hasAgreed && !_isLoading ? _startTrial : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: context.accentColor,
                disabledBackgroundColor: Colors.grey.withOpacity(0.3),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: _isLoading
                  ? const SizedBox(
                      height: 20,
                      width: 20,
                      child: CircularProgressIndicator(
                        color: Colors.white,
                        strokeWidth: 2,
                      ),
                    )
                  : Text(
                      s.startFreeTrial,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
            ),

            const SizedBox(height: 16),

            // Terms and privacy
            Text(
              s.freeTrialTerms,
              style: Theme.of(context).textTheme.bodySmall,
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 24),

            // Restore purchases button
            Center(
              child: OutlinedButton.icon(
                onPressed: _restorePurchases,
                icon: Icon(
                  Icons.restore,
                  color: context.secondaryAccentColor,
                  size: 20,
                ),
                label: Text(
                  s.restorePurchases,
                  style: TextStyle(
                    color: context.secondaryAccentColor,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                style: OutlinedButton.styleFrom(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                  side: BorderSide(color: context.secondaryAccentColor),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPlanOption(
    BuildContext context, {
    required SubscriptionTier tier,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: context.containerBackgroundColor,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color:
                isSelected ? context.accentColor : Colors.grey.withOpacity(0.3),
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Row(
          children: [
            // Radio button
            Container(
              width: 24,
              height: 24,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: isSelected
                      ? context.accentColor
                      : Colors.grey.withOpacity(0.5),
                  width: 2,
                ),
              ),
              child: isSelected
                  ? Center(
                      child: Container(
                        width: 12,
                        height: 12,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: context.accentColor,
                        ),
                      ),
                    )
                  : null,
            ),
            const SizedBox(width: 16),

            // Plan details
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    tier.getDisplayName(),
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    _priceLoading
                        ? S.of(context).loadingPrice
                        : S.of(context).freeForSevenDaysThen(
                              _monthlyPrice,
                              S.of(context).perMonth,
                            ),
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _startTrial() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final success = await ref
          .read(subscriptionProvider.notifier)
          .startFreeTrial(_selectedTier);

      if (success && mounted) {
        final sLocal = S.of(context);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(sLocal.trialStartedSuccess),
            backgroundColor: Colors.green,
          ),
        );
        context.go('/dashboard');
      } else if (mounted) {
        final sLocal = S.of(context);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(sLocal.trialStartFailed),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _restorePurchases() async {
    final s = S.of(context);
    setState(() => _isLoading = true);

    try {
      final result =
          await ref.read(subscriptionProvider.notifier).restorePurchases();

      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            result ? s.purchasesRestored : s.noPurchasesFound,
          ),
          backgroundColor: result ? Colors.green : Colors.orange,
          behavior: SnackBarBehavior.floating,
          margin: const EdgeInsets.all(16),
        ),
      );
    } catch (e) {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(s.errorOccurred),
          backgroundColor: Colors.red,
          behavior: SnackBarBehavior.floating,
          margin: const EdgeInsets.all(16),
        ),
      );
    } finally {
      if (mounted) setState(() => _isLoading = false);
    }
  }
}
