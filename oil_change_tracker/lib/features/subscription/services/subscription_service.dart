import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:in_app_purchase/in_app_purchase.dart';
import 'package:in_app_purchase_android/in_app_purchase_android.dart';
import '../models/subscription_model.dart';
import '../models/subscription_tier.dart';
import 'dart:developer' as dev;

/// Result of a subscription purchase attempt
enum SubscriptionPurchaseResult { success, cancelled, failure }

/// Provider for the SubscriptionService
final subscriptionServiceProvider = Provider<SubscriptionService>((ref) {
  final service = SubscriptionService(
    FirebaseFirestore.instance,
    FirebaseAuth.instance,
    InAppPurchase.instance,
  );
  // Initialize the service when it's first provided
  service.initialize();
  return service;
});

/// Service to handle subscription-related functionality
class SubscriptionService {
  final FirebaseFirestore _firestore;
  final FirebaseAuth _auth;
  final InAppPurchase _inAppPurchase;

  // Stream subscription for purchase updates
  StreamSubscription<List<PurchaseDetails>>? _subscription;

  // Completer for purchase operations
  Completer<SubscriptionPurchaseResult>? _purchaseCompleter;

  // Product IDs for subscriptions
  static const String _premiumMonthlyId = 'premium_monthly';
  static const String _premiumYearlyId = 'premium_yearly';

  // List of all product IDs
  static final List<String> _productIds = [
    _premiumMonthlyId,
    _premiumYearlyId,
  ];

  // Cache for product details
  ProductDetails? _monthlyProduct;
  ProductDetails? _yearlyProduct;

  // Flag to track if products have been loaded
  bool _productsLoaded = false;

  // Timestamp for cache invalidation
  DateTime? _lastProductFetch;

  /// Create a new SubscriptionService
  SubscriptionService(this._firestore, this._auth, this._inAppPurchase) {
    // Initialize the purchase stream listener
    _initializePurchaseListener();
  }

  /// Initialize the purchase stream listener
  void _initializePurchaseListener() {
    _subscription = _inAppPurchase.purchaseStream.listen(
      _handlePurchaseUpdate,
      onDone: () {
        _subscription?.cancel();
        _subscription = null;
      },
      onError: (error) {
        dev.log('Error in purchase stream: $error');
        // Complete with error if there's an active purchase
        _purchaseCompleter?.completeError(error);
      },
    );
  }

  /// Handle purchase updates from the stream
  void _handlePurchaseUpdate(List<PurchaseDetails> purchaseDetailsList) async {
    for (final purchaseDetails in purchaseDetailsList) {
      if (purchaseDetails.status == PurchaseStatus.pending) {
        // Purchase is pending, show loading indicator
        dev.log('Purchase pending: ${purchaseDetails.productID}');
      } else if (purchaseDetails.status == PurchaseStatus.error) {
        // Purchase failed
        dev.log('Purchase error: ${purchaseDetails.error}');
        _purchaseCompleter?.complete(SubscriptionPurchaseResult.failure);
        _purchaseCompleter = null;
      } else if (purchaseDetails.status == PurchaseStatus.purchased ||
          purchaseDetails.status == PurchaseStatus.restored) {
        // Purchase was successful or restored

        // Verify the purchase on the server side
        final valid = await _verifyPurchase(purchaseDetails);

        if (valid) {
          // Deliver the product and store purchase details in Firestore
          await _deliverProduct(purchaseDetails);

          // Complete the purchase operation
          _purchaseCompleter?.complete(SubscriptionPurchaseResult.success);
          _purchaseCompleter = null;
        } else {
          // Invalid purchase
          dev.log('Invalid purchase: ${purchaseDetails.productID}');
          _purchaseCompleter?.complete(SubscriptionPurchaseResult.failure);
          _purchaseCompleter = null;
        }
      } else if (purchaseDetails.status == PurchaseStatus.canceled) {
        // User canceled the purchase - don't activate subscription
        dev.log('Purchase canceled by user: ${purchaseDetails.productID}');
        _purchaseCompleter?.complete(SubscriptionPurchaseResult.cancelled);
        _purchaseCompleter = null;

        // Make sure we don't proceed with completing the purchase
        continue;
      }

      // Complete the purchase if it's not pending and not canceled
      if (purchaseDetails.pendingCompletePurchase &&
          purchaseDetails.status != PurchaseStatus.canceled) {
        await _inAppPurchase.completePurchase(purchaseDetails);
      }
    }
  }

  /// Verify the purchase with the server
  Future<bool> _verifyPurchase(PurchaseDetails purchaseDetails) async {
    // For Android, we should verify the purchase with Google Play
    if (purchaseDetails is GooglePlayPurchaseDetails) {
      // Check if purchase was canceled
      if (purchaseDetails.status == PurchaseStatus.canceled) {
        return false;
      }

      // In a real app, you would verify the purchase token with your server
      // which would then verify with Google Play Developer API

      // For now, we'll assume it's valid if we have a purchase token and status is not canceled
      return purchaseDetails.billingClientPurchase.purchaseToken.isNotEmpty &&
          purchaseDetails.status != PurchaseStatus.canceled;
    }

    return false;
  }

  /// Deliver the purchased product to the user
  Future<void> _deliverProduct(PurchaseDetails purchaseDetails) async {
    final user = _auth.currentUser;
    if (user == null) return;

    // Determine subscription tier and duration from product ID
    SubscriptionTier tier;
    DateTime expiryDate;

    switch (purchaseDetails.productID) {
      case _premiumMonthlyId:
        tier = SubscriptionTier.premium;
        expiryDate = DateTime.now().add(const Duration(days: 30));
        break;
      case _premiumYearlyId:
        tier = SubscriptionTier.premium;
        expiryDate = DateTime.now().add(const Duration(days: 365));
        break;
      default:
        // Unknown product ID
        return;
    }

    // Create subscription record
    final subscription = SubscriptionModel(
      userId: user.uid,
      tier: tier,
      startDate: DateTime.now(),
      expiryDate: expiryDate,
      isActive: true,
      autoRenew: false, // Compliance: Let Google Play handle renewals
      purchaseToken: purchaseDetails is GooglePlayPurchaseDetails
          ? purchaseDetails.billingClientPurchase.purchaseToken
          : purchaseDetails.verificationData.serverVerificationData,
      platform: 'android',
      originalTransactionId: purchaseDetails.purchaseID,
    );

    // Save to Firestore
    await _firestore
        .collection('subscriptions')
        .doc(user.uid)
        .set(subscription.toJson());

    // Update local cache
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('has_active_subscription', true);
    await prefs.setInt(
        'subscription_expiry', subscription.expiryDate!.millisecondsSinceEpoch);
  }

  /// Check if the current user has an active subscription
  Future<bool> hasActiveSubscription() async {
    try {
      final user = _auth.currentUser;
      if (user == null) return false;

      // First check local cache for faster response
      final prefs = await SharedPreferences.getInstance();
      final hasValidCachedSubscription =
          prefs.getBool('has_active_subscription') ?? false;
      final subscriptionExpiryTimestamp = prefs.getInt('subscription_expiry');

      // If we have a cached subscription that hasn't expired, return true
      if (hasValidCachedSubscription &&
          subscriptionExpiryTimestamp != null &&
          DateTime.fromMillisecondsSinceEpoch(subscriptionExpiryTimestamp)
              .isAfter(DateTime.now())) {
        return true;
      }

      // Otherwise check Firestore
      final subscriptionDoc =
          await _firestore.collection('subscriptions').doc(user.uid).get();

      if (!subscriptionDoc.exists) {
        return false;
      }

      final subscriptionData = subscriptionDoc.data();
      if (subscriptionData == null) return false;

      final subscription = SubscriptionModel.fromJson(subscriptionData);

      // Check if this is a trial subscription
      final isTrial = subscription.isTrial;
      final isActive = subscription.isActiveNow();

      // Update local cache
      await prefs.setBool('has_active_subscription', isActive);
      if (isActive && subscription.expiryDate != null) {
        await prefs.setInt('subscription_expiry',
            subscription.expiryDate!.millisecondsSinceEpoch);
        // Store trial status in cache
        await prefs.setBool('is_trial_subscription', isTrial);
      }

      return isActive;
    } catch (e) {
      dev.log('Error checking subscription status: $e');
      return false;
    }
  }

  /// Get the current user's subscription details
  Future<SubscriptionModel?> getCurrentSubscription() async {
    try {
      final user = _auth.currentUser;
      if (user == null) return null;

      final subscriptionDoc =
          await _firestore.collection('subscriptions').doc(user.uid).get();

      if (!subscriptionDoc.exists) {
        return null;
      }

      final subscriptionData = subscriptionDoc.data();
      if (subscriptionData == null) return null;

      return SubscriptionModel.fromJson(subscriptionData);
    } catch (e) {
      dev.log('Error getting current subscription: $e');
      return null;
    }
  }

  /// Get available products from the store
  Future<List<ProductDetails>> getProducts() async {
    try {
      final response =
          await _inAppPurchase.queryProductDetails(_productIds.toSet());

      if (response.error != null) {
        dev.log('Error loading products: ${response.error}');
        return [];
      }

      return response.productDetails;
    } catch (e) {
      dev.log('Error getting products: $e');
      return [];
    }
  }

  /// Purchase a subscription
  Future<SubscriptionPurchaseResult> purchase(SubscriptionTier tier,
      {bool monthly = true}) async {
    try {
      // Ensure products are loaded and fresh
      if (!_productsLoaded || _isCacheStale()) {
        await _loadProductDetails();
      }

      final productId = monthly ? _premiumMonthlyId : _premiumYearlyId;
      final product = monthly ? _monthlyProduct : _yearlyProduct;

      if (product == null) {
        dev.log(
            'SubscriptionService.purchase: Product not found -> $productId');
        return SubscriptionPurchaseResult.failure;
      }

      // Reset and create a new completer that the purchase update stream will complete
      _purchaseCompleter?.complete(SubscriptionPurchaseResult.failure);
      _purchaseCompleter = Completer<SubscriptionPurchaseResult>();

      final purchaseParam = PurchaseParam(productDetails: product);

      final launchOk =
          await _inAppPurchase.buyNonConsumable(purchaseParam: purchaseParam);

      if (!launchOk) {
        dev.log('SubscriptionService.purchase: Failed to launch purchase flow');
        _purchaseCompleter?.complete(SubscriptionPurchaseResult.failure);
        return SubscriptionPurchaseResult.failure;
      }

      // Wait for result from _handlePurchaseUpdate (success, error, or cancel)
      final result = await _purchaseCompleter!.future;
      return result;
    } catch (e) {
      dev.log('Error purchasing subscription: $e');
      return SubscriptionPurchaseResult.failure;
    }
  }

  /// Cancel a subscription
  Future<bool> cancel() async {
    try {
      final user = _auth.currentUser;
      if (user == null) return false;

      // Get current subscription
      final subscriptionDoc =
          await _firestore.collection('subscriptions').doc(user.uid).get();

      if (!subscriptionDoc.exists) {
        return false;
      }

      // Update subscription record
      await _firestore.collection('subscriptions').doc(user.uid).update({
        'autoRenew': false,
        'cancelDate': FieldValue.serverTimestamp(),
      });

      // Note: In a real app, you would also need to cancel the subscription
      // through Google Play Developer API

      return true;
    } catch (e) {
      dev.log('Error cancelling subscription: $e');
      return false;
    }
  }

  /// Restore purchases
  Future<bool> restorePurchases() async {
    try {
      // Create a completer to track if any purchases were actually restored
      final completer = Completer<bool>();
      bool restoredAny = false;

      // Listen for restored purchases
      final subscription = _inAppPurchase.purchaseStream.listen((purchases) {
        for (final purchase in purchases) {
          if (purchase.status == PurchaseStatus.restored) {
            restoredAny = true;
            dev.log('Purchase restored: ${purchase.productID}');
          }
        }
      });

      // Start the restore process
      await _inAppPurchase.restorePurchases();

      // Wait a bit for restored purchases to come through the stream
      await Future.delayed(const Duration(seconds: 3));

      // Cancel the subscription and return result
      await subscription.cancel();

      dev.log('Restore purchases completed. Restored any: $restoredAny');
      return restoredAny;
    } catch (e) {
      dev.log('Error restoring purchases: $e');
      return false;
    }
  }

  /// Start a free trial
  Future<bool> startFreeTrial(SubscriptionTier tier, {int? trialDays}) async {
    try {
      final user = _auth.currentUser;
      if (user == null) return false;

      dev.log(
          'SubscriptionService.startFreeTrial: Attempting to start trial for user: ${user.uid}');

      // Check if user has had a trial before or has any subscription record
      final subscriptionDoc =
          await _firestore.collection('subscriptions').doc(user.uid).get();

      if (subscriptionDoc.exists) {
        final data = subscriptionDoc.data();
        if (data != null) {
          // Check current subscription status
          final subscription = SubscriptionModel.fromJson(data);

          // If user has an active subscription (including trial), don't allow new trial
          if (subscription.isActiveNow()) {
            dev.log(
                'SubscriptionService.startFreeTrial: User ${user.uid} already has active subscription/trial');
            return false; // User already has active subscription
          }

          // Prevent trial if user already had a trial before (even if expired)
          final hadTrial = data['hadFreeTrial'] == true;

          if (hadTrial) {
            dev.log(
                'SubscriptionService.startFreeTrial: Trial blocked for user: ${user.uid} - already had trial before');
            return false; // User already had a trial
          }
        }
      }

      // Use provided trial days or default to 7
      final daysToAdd = trialDays ?? 7;

      // Create subscription record with trial
      final subscription = SubscriptionModel(
        userId: user.uid,
        tier: tier,
        startDate: DateTime.now(),
        expiryDate: DateTime.now().add(Duration(days: daysToAdd)),
        isActive: true,
        autoRenew: false,
        isTrial: true,
        hadFreeTrial: true,
        platform: 'android',
      );

      // Save to Firestore
      await _firestore
          .collection('subscriptions')
          .doc(user.uid)
          .set(subscription.toJson());

      // Update local cache
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('has_active_subscription', true);
      await prefs.setInt('subscription_expiry',
          subscription.expiryDate!.millisecondsSinceEpoch);
      await prefs.setBool('is_trial_subscription', true);

      dev.log(
          'SubscriptionService.startFreeTrial: Trial started successfully for user: ${user.uid}, expiryDate: ${subscription.expiryDate}');
      return true;
    } catch (e) {
      dev.log(
          'SubscriptionService.startFreeTrial: Error starting free trial: $e');
      return false;
    }
  }

  /// Automatically start trial for new users
  Future<bool> startTrialForNewUser(String userId, {int? trialDays}) async {
    try {
      dev.log(
          'SubscriptionService.startTrialForNewUser: Attempting to start trial for new user: $userId');

      // Check if user already has a subscription record
      final subscriptionDoc =
          await _firestore.collection('subscriptions').doc(userId).get();

      if (subscriptionDoc.exists) {
        final data = subscriptionDoc.data();
        if (data != null) {
          // Check for any previous subscription activity
          final hadTrial = data['hadFreeTrial'] == true;
          final isTrial = data['isTrial'] == true;
          final hasAnySubscription = data['tier'] != null;

          if (hadTrial || isTrial || hasAnySubscription) {
            dev.log(
                'SubscriptionService.startTrialForNewUser: Trial blocked for user: $userId - already has subscription record (hadTrial: $hadTrial, isTrial: $isTrial, hasSubscription: $hasAnySubscription)');
            return false; // User already has a subscription record
          }
        }
      }

      // Use provided trial days or default to 7
      final daysToAdd = trialDays ?? 7;

      // Create subscription record with trial for new user
      final subscription = SubscriptionModel(
        userId: userId,
        tier: SubscriptionTier.premium, // Give premium features during trial
        startDate: DateTime.now(),
        expiryDate: DateTime.now().add(Duration(days: daysToAdd)),
        isActive: true,
        autoRenew: false,
        isTrial: true,
        hadFreeTrial: true,
        platform: 'android',
      );

      // Save to Firestore
      await _firestore
          .collection('subscriptions')
          .doc(userId)
          .set(subscription.toJson());

      // Update local cache
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('has_active_subscription', true);
      await prefs.setInt('subscription_expiry',
          subscription.expiryDate!.millisecondsSinceEpoch);
      await prefs.setBool('is_trial_subscription', true);

      dev.log(
          'SubscriptionService.startTrialForNewUser: Trial started successfully for new user: $userId, expiryDate: ${subscription.expiryDate}, isTrial: ${subscription.isTrial}');
      return true;
    } catch (e) {
      dev.log(
          'SubscriptionService.startTrialForNewUser: Error auto-starting trial for new user: $e');
      return false;
    }
  }

  /// Dispose resources
  void dispose() {
    _subscription?.cancel();
  }

  /// Initialize the subscription service
  Future<void> initialize() async {
    try {
      final available = await _inAppPurchase.isAvailable();
      if (!available) {
        dev.log('In-app purchases not available');
        return;
      }

      // Load product details if not already loaded or cache is stale
      if (!_productsLoaded || _isCacheStale()) {
        await _loadProductDetails();
      }
    } catch (e) {
      dev.log('Error initializing subscription service: $e');
    }
  }

  /// Check if the product cache is stale (older than 1 hour)
  bool _isCacheStale() {
    if (_lastProductFetch == null) return true;

    final cacheDuration = const Duration(hours: 1);
    return DateTime.now().difference(_lastProductFetch!) > cacheDuration;
  }

  /// Load product details from the store
  Future<void> _loadProductDetails() async {
    dev.log('SubscriptionService._loadProductDetails: Starting...');

    try {
      // Check if the store is available
      dev.log(
          'SubscriptionService._loadProductDetails: Checking if store is available...');
      final bool available = await _inAppPurchase.isAvailable();
      if (!available) {
        dev.log('SubscriptionService._loadProductDetails: Store not available');
        return;
      }

      dev.log(
          'SubscriptionService._loadProductDetails: Store available, querying products: $_productIds');
      // Query product details
      final ProductDetailsResponse response =
          await _inAppPurchase.queryProductDetails(_productIds.toSet());

      dev.log(
          'SubscriptionService._loadProductDetails: Query response - found ${response.productDetails.length} products, ${response.notFoundIDs.length} not found');

      if (response.notFoundIDs.isNotEmpty) {
        dev.log(
            'SubscriptionService._loadProductDetails: Not found IDs: ${response.notFoundIDs}');
      }

      // Store product details
      for (final product in response.productDetails) {
        dev.log(
            'SubscriptionService._loadProductDetails: Processing product ${product.id} - ${product.title} - ${product.price}');
        if (product.id == _premiumMonthlyId) {
          _monthlyProduct = product;
          dev.log(
              'SubscriptionService._loadProductDetails: Set monthly product: ${product.price}');
        } else if (product.id == _premiumYearlyId) {
          _yearlyProduct = product;
          dev.log(
              'SubscriptionService._loadProductDetails: Set yearly product: ${product.price}');
        }
      }

      _productsLoaded = true;
      _lastProductFetch = DateTime.now();
      dev.log(
          'SubscriptionService._loadProductDetails: Completed successfully. Monthly: ${_monthlyProduct?.price}, Yearly: ${_yearlyProduct?.price}');
    } catch (e) {
      dev.log('SubscriptionService._loadProductDetails: Error: $e');
      // Handle error
    }
  }

  /// Get the formatted price for a subscription tier and period
  Future<String> getFormattedPrice(SubscriptionTier tier, bool monthly) async {
    dev.log(
        'SubscriptionService.getFormattedPrice: Called for tier=${tier.name}, monthly=$monthly');

    if (tier == SubscriptionTier.free) {
      dev.log(
          'SubscriptionService.getFormattedPrice: Returning Free for free tier');
      return 'Free';
    }

    // Ensure products are loaded
    dev.log(
        'SubscriptionService.getFormattedPrice: Checking if products loaded. _productsLoaded=$_productsLoaded, _isCacheStale=${_isCacheStale()}');
    if (!_productsLoaded || _isCacheStale()) {
      dev.log(
          'SubscriptionService.getFormattedPrice: Loading product details...');
      await _loadProductDetails();
    }

    // Get the appropriate product
    final product = monthly ? _monthlyProduct : _yearlyProduct;
    final productName = monthly ? 'monthly' : 'yearly';
    dev.log(
        'SubscriptionService.getFormattedPrice: $productName product = ${product?.id}, price = ${product?.price}');

    // If product is available, return its price
    if (product != null) {
      dev.log(
          'SubscriptionService.getFormattedPrice: Returning live price: ${product.price}');
      return product.price;
    }

    // Fallback to hardcoded values if products couldn't be loaded
    final fallbackPrice = monthly
        ? '\$${tier.monthlyPrice.toStringAsFixed(2)}'
        : '\$${(tier.yearlyPrice / 12).toStringAsFixed(2)}';
    dev.log(
        'SubscriptionService.getFormattedPrice: Product not available, returning fallback: $fallbackPrice');

    if (monthly) {
      return '\$${tier.monthlyPrice.toStringAsFixed(2)}';
    } else {
      return '\$${(tier.yearlyPrice / 12).toStringAsFixed(2)}';
    }
  }

  /// Get the raw price amount for a subscription tier and period
  Future<double> getRawPrice(SubscriptionTier tier, bool monthly) async {
    if (tier == SubscriptionTier.free) {
      return 0.0;
    }

    // Ensure products are loaded
    if (!_productsLoaded || _isCacheStale()) {
      await _loadProductDetails();
    }

    // Get the appropriate product
    final product = monthly ? _monthlyProduct : _yearlyProduct;

    // If product is available, try to parse its price
    if (product != null) {
      try {
        // Remove currency symbol and try to parse as double
        final priceString = product.price.replaceAll(RegExp(r'[^0-9.,]'), '');
        return double.tryParse(priceString.replaceAll(',', '.')) ??
            (monthly ? tier.monthlyPrice : tier.yearlyPrice / 12);
      } catch (e) {
        dev.log('Error parsing price: $e');
      }
    }

    // Fallback to hardcoded values
    return monthly ? tier.monthlyPrice : tier.yearlyPrice / 12;
  }

  /// Public method to force refresh product details, bypassing cache.
  Future<void> refreshProducts() async {
    // Completely clear cached data
    _productsLoaded = false;
    _lastProductFetch = null;
    _monthlyProduct = null;
    _yearlyProduct = null;

    dev.log(
        'SubscriptionService.refreshProducts: Clearing cache and fetching fresh product data');

    // Force fresh load
    await _loadProductDetails();

    dev.log(
        'SubscriptionService.refreshProducts: Fresh data loaded. Monthly: ${_monthlyProduct?.price}, Yearly: ${_yearlyProduct?.price}');
  }

  /// Handle trial expiration - convert expired trial users to free tier
  Future<bool> handleTrialExpiration() async {
    try {
      final user = _auth.currentUser;
      if (user == null) return false;

      final subscription = await getCurrentSubscription();
      if (subscription == null) return false;

      // Check if trial has expired
      if (subscription.isTrial &&
          subscription.expiryDate != null &&
          subscription.expiryDate!.isBefore(DateTime.now())) {
        // Update subscription to free tier
        final expiredSubscription = SubscriptionModel(
          userId: user.uid,
          tier: SubscriptionTier.free,
          startDate: subscription.startDate,
          expiryDate: null, // Free tier doesn't expire
          isActive: false, // No active subscription
          autoRenew: false,
          isTrial: false,
          hadFreeTrial: true, // Keep track that they had a trial
          platform: subscription.platform,
        );

        // Save updated subscription to Firestore
        await _firestore
            .collection('subscriptions')
            .doc(user.uid)
            .set(expiredSubscription.toJson());

        // Update local cache - clear all subscription-related cache
        final prefs = await SharedPreferences.getInstance();
        await prefs.setBool('has_active_subscription', false);
        await prefs.remove('subscription_expiry');
        await prefs.setBool('is_trial_subscription', false);

        dev.log('Trial expired for user: ${user.uid}, converted to free tier');
        return true;
      }

      return false;
    } catch (e) {
      dev.log('Error handling trial expiration: $e');
      return false;
    }
  }

  /// Check and handle trial expiration on app startup
  Future<void> checkTrialExpiration() async {
    try {
      final user = _auth.currentUser;
      if (user == null) return;

      final subscription = await getCurrentSubscription();
      if (subscription?.isTrial == true) {
        await handleTrialExpiration();
      }
    } catch (e) {
      dev.log('Error checking trial expiration: $e');
    }
  }

  /// Clear subscription (for testing Remote Config changes)
  Future<void> clearSubscription() async {
    try {
      final user = _auth.currentUser;
      if (user == null) return;

      // Delete from Firestore
      await _firestore.collection('subscriptions').doc(user.uid).delete();

      // Clear local cache
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('has_active_subscription');
      await prefs.remove('subscription_expiry');
      await prefs.remove('is_trial_subscription');

      dev.log('Subscription cleared for user: ${user.uid}');
    } catch (e) {
      dev.log('Error clearing subscription: $e');
    }
  }

  /// Update existing trial duration to match Remote Config
  Future<bool> updateExistingTrialDuration(int newTrialDays) async {
    try {
      final user = _auth.currentUser;
      if (user == null) return false;

      final subscription = await getCurrentSubscription();
      if (subscription == null || !subscription.isTrial) {
        dev.log('No active trial subscription to update');
        return false;
      }

      // Don't extend expired trials
      if (subscription.expiryDate != null &&
          subscription.expiryDate!.isBefore(DateTime.now())) {
        dev.log(
            'Trial already expired, not extending: ${subscription.expiryDate}');
        return false;
      }

      // Calculate new expiry date from the original start date
      final newExpiryDate =
          subscription.startDate.add(Duration(days: newTrialDays));

      // Don't allow reducing trial time or extending past what would be reasonable
      if (subscription.expiryDate != null &&
          newExpiryDate.isBefore(subscription.expiryDate!)) {
        dev.log(
            'Not reducing trial duration from ${subscription.expiryDate} to $newExpiryDate');
        return false;
      }

      // Prevent unreasonable extensions (more than 30 days total)
      final totalTrialDays =
          newExpiryDate.difference(subscription.startDate).inDays;
      if (totalTrialDays > 30) {
        dev.log(
            'Preventing unreasonable trial extension: $totalTrialDays days total');
        return false;
      }

      // Update the subscription with new expiry date
      final updatedSubscription = subscription.copyWith(
        expiryDate: newExpiryDate,
      );

      // Save updated subscription to Firestore
      await _firestore
          .collection('subscriptions')
          .doc(user.uid)
          .set(updatedSubscription.toJson());

      // Update local cache
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(
          'subscription_expiry', newExpiryDate.millisecondsSinceEpoch);

      dev.log(
          'Updated trial duration for user: ${user.uid}, new expiry: $newExpiryDate');
      return true;
    } catch (e) {
      dev.log('Error updating trial duration: $e');
      return false;
    }
  }
}
