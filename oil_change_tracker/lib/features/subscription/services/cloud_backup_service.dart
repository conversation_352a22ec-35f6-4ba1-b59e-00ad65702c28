import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/utils/logger.dart';
import '../providers/feature_gate_provider.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'dart:convert';

/// Service for handling cloud backup and sync of user data
class CloudBackupService {
  final FirebaseFirestore _firestore;
  final FirebaseAuth _auth;
  final FirebaseStorage _storage;
  final Ref _ref;

  CloudBackupService(this._firestore, this._auth, this._storage, this._ref);

  /// Check if the user has access to cloud backup features
  bool get _hasCloudBackupAccess {
    try {
      return _ref.read(featureGateProvider(PremiumFeature.cloudBackup));
    } catch (e) {
      AppLogger.error(
          '[CloudBackupService] Error checking cloud backup access: $e');
      return false;
    }
  }

  /// Backup user data to the cloud
  Future<bool> backupUserData() async {
    try {
      // Check if user has premium subscription
      if (!_hasCloudBackupAccess) {
        AppLogger.warning(
            '[CloudBackupService] User does not have cloud backup access');
        return false;
      }

      final user = _auth.currentUser;
      if (user == null) {
        AppLogger.warning('[CloudBackupService] No authenticated user');
        return false;
      }

      // Get user's cars
      final carsSnapshot = await _firestore
          .collection('users')
          .doc(user.uid)
          .collection('cars')
          .get();

      // Get user's oil changes
      final oilChangesSnapshot = await _firestore
          .collection('users')
          .doc(user.uid)
          .collection('oil_changes')
          .get();

      // Get user's settings
      final settingsSnapshot = await _firestore
          .collection('users')
          .doc(user.uid)
          .collection('settings')
          .doc('preferences')
          .get();

      // Create backup data
      final backupData = {
        'timestamp': FieldValue.serverTimestamp(),
        'cars': carsSnapshot.docs.map((doc) => doc.data()).toList(),
        'oil_changes':
            oilChangesSnapshot.docs.map((doc) => doc.data()).toList(),
        'settings': settingsSnapshot.exists ? settingsSnapshot.data() : {},
      };

      // Save backup to Firestore
      await _firestore
          .collection('users')
          .doc(user.uid)
          .collection('backups')
          .doc(DateTime.now().toIso8601String())
          .set(backupData);

      // Also save a JSON backup to Storage for redundancy
      final jsonBackup = json.encode({
        'timestamp': DateTime.now().toIso8601String(),
        'cars': carsSnapshot.docs.map((doc) => doc.data()).toList(),
        'oil_changes':
            oilChangesSnapshot.docs.map((doc) => doc.data()).toList(),
        'settings': settingsSnapshot.exists ? settingsSnapshot.data() : {},
      });

      final backupRef = _storage
          .ref('backups/${user.uid}/${DateTime.now().toIso8601String()}.json');

      await backupRef.putString(jsonBackup, format: PutStringFormat.raw);

      AppLogger.info('[CloudBackupService] Backup completed successfully');
      return true;
    } catch (e) {
      AppLogger.error('[CloudBackupService] Error backing up data: $e');
      return false;
    }
  }

  /// Restore user data from the cloud
  Future<bool> restoreFromLatestBackup() async {
    try {
      // Check if user has premium subscription
      if (!_hasCloudBackupAccess) {
        AppLogger.warning(
            '[CloudBackupService] User does not have cloud backup access');
        return false;
      }

      final user = _auth.currentUser;
      if (user == null) {
        AppLogger.warning('[CloudBackupService] No authenticated user');
        return false;
      }

      // Get the latest backup
      final backupsSnapshot = await _firestore
          .collection('users')
          .doc(user.uid)
          .collection('backups')
          .orderBy('timestamp', descending: true)
          .limit(1)
          .get();

      if (backupsSnapshot.docs.isEmpty) {
        AppLogger.warning('[CloudBackupService] No backups found');
        return false;
      }

      final latestBackup = backupsSnapshot.docs.first.data();

      // Start a batch write
      final batch = _firestore.batch();

      // Restore cars
      if (latestBackup['cars'] != null) {
        // First delete existing cars
        final existingCars = await _firestore
            .collection('users')
            .doc(user.uid)
            .collection('cars')
            .get();

        for (var doc in existingCars.docs) {
          batch.delete(doc.reference);
        }

        // Then add backed up cars
        for (var car in latestBackup['cars']) {
          final carRef = _firestore
              .collection('users')
              .doc(user.uid)
              .collection('cars')
              .doc();

          batch.set(carRef, car);
        }
      }

      // Restore oil changes
      if (latestBackup['oil_changes'] != null) {
        // First delete existing oil changes
        final existingOilChanges = await _firestore
            .collection('users')
            .doc(user.uid)
            .collection('oil_changes')
            .get();

        for (var doc in existingOilChanges.docs) {
          batch.delete(doc.reference);
        }

        // Then add backed up oil changes
        for (var oilChange in latestBackup['oil_changes']) {
          final oilChangeRef = _firestore
              .collection('users')
              .doc(user.uid)
              .collection('oil_changes')
              .doc();

          batch.set(oilChangeRef, oilChange);
        }
      }

      // Restore settings
      if (latestBackup['settings'] != null) {
        batch.set(
            _firestore
                .collection('users')
                .doc(user.uid)
                .collection('settings')
                .doc('preferences'),
            latestBackup['settings']);
      }

      // Commit all changes
      await batch.commit();

      AppLogger.info('[CloudBackupService] Restore completed successfully');
      return true;
    } catch (e) {
      AppLogger.error('[CloudBackupService] Error restoring data: $e');
      return false;
    }
  }

  /// Get list of available backups
  Future<List<Map<String, dynamic>>> getAvailableBackups() async {
    try {
      // Check if user has premium subscription
      if (!_hasCloudBackupAccess) {
        AppLogger.warning(
            '[CloudBackupService] User does not have cloud backup access');
        return [];
      }

      final user = _auth.currentUser;
      if (user == null) {
        AppLogger.warning('[CloudBackupService] No authenticated user');
        return [];
      }

      // Get all backups
      final backupsSnapshot = await _firestore
          .collection('users')
          .doc(user.uid)
          .collection('backups')
          .orderBy('timestamp', descending: true)
          .get();

      return backupsSnapshot.docs.map((doc) {
        final data = doc.data();
        return {
          'id': doc.id,
          'timestamp': data['timestamp'] != null
              ? (data['timestamp'] as Timestamp).toDate().toIso8601String()
              : DateTime.now().toIso8601String(),
          'car_count': data['cars'] != null ? (data['cars'] as List).length : 0,
          'oil_change_count': data['oil_changes'] != null
              ? (data['oil_changes'] as List).length
              : 0,
        };
      }).toList();
    } catch (e) {
      AppLogger.error(
          '[CloudBackupService] Error getting available backups: $e');
      return [];
    }
  }

  /// Enable automatic backups
  Future<bool> enableAutomaticBackups() async {
    try {
      // Check if user has premium subscription
      if (!_hasCloudBackupAccess) {
        AppLogger.warning(
            '[CloudBackupService] User does not have cloud backup access');
        return false;
      }

      final user = _auth.currentUser;
      if (user == null) {
        AppLogger.warning('[CloudBackupService] No authenticated user');
        return false;
      }

      // Update user settings to enable automatic backups
      await _firestore
          .collection('users')
          .doc(user.uid)
          .collection('settings')
          .doc('preferences')
          .set({
        'automatic_backups': true,
        'backup_frequency': 'daily', // Options: daily, weekly, monthly
        'last_backup': FieldValue.serverTimestamp(),
      }, SetOptions(merge: true));

      AppLogger.info('[CloudBackupService] Automatic backups enabled');
      return true;
    } catch (e) {
      AppLogger.error(
          '[CloudBackupService] Error enabling automatic backups: $e');
      return false;
    }
  }
}

/// Provider for the cloud backup service
final cloudBackupServiceProvider = Provider<CloudBackupService>((ref) {
  return CloudBackupService(
    FirebaseFirestore.instance,
    FirebaseAuth.instance,
    FirebaseStorage.instance,
    ref,
  );
});
