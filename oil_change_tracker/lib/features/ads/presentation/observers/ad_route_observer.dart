import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../managers/interstitial_ad_manager.dart';
import 'dart:developer' as dev;

/// Route observer that shows interstitial ads during navigation
class AdRouteObserver extends NavigatorObserver {
  final Ref _ref;
  String? _previousRouteName;

  AdRouteObserver(this._ref);

  @override
  void didPush(Route<dynamic> route, Route<dynamic>? previousRoute) {
    super.didPush(route, previousRoute);
    _handleRouteChange(route, previousRoute);
  }

  @override
  void didReplace({Route<dynamic>? newRoute, Route<dynamic>? oldRoute}) {
    super.didReplace(newRoute: newRoute, oldRoute: oldRoute);
    if (newRoute != null) {
      _handleRouteChange(newRoute, oldRoute);
    }
  }

  @override
  void didPop(Route<dynamic> route, Route<dynamic>? previousRoute) {
    super.didPop(route, previousRoute);
    if (previousRoute != null) {
      _handleRouteChange(previousRoute, route);
    }
  }

  /// Handle route changes and show ads if appropriate
  void _handleRouteChange(
      Route<dynamic> currentRoute, Route<dynamic>? previousRoute) {
    final currentRouteName = _getRouteName(currentRoute);
    if (currentRouteName == null) return;

    final previousRouteName = previousRoute != null
        ? _getRouteName(previousRoute)
        : _previousRouteName;

    if (previousRouteName == null || previousRouteName == currentRouteName) {
      _previousRouteName = currentRouteName;
      return;
    }

    // Skip ad for certain navigation patterns
    if (_shouldSkipAdForNavigation(previousRouteName, currentRouteName)) {
      _previousRouteName = currentRouteName;
      return;
    }

    // Show ad between screens with the appropriate probability
    final adManager = _ref.read(interstitialAdManagerProvider.notifier);
    final navigator = Navigator.of(currentRoute.navigator!.context);

    // Quick check to avoid unnecessary async work
    final routeKey = '${previousRouteName}_to_$currentRouteName';
    if (adManager.canPotentiallyShowAd(routeKey)) {
      WidgetsBinding.instance.addPostFrameCallback((_) async {
        try {
          await adManager.showAdBetweenScreens(
              navigator.context, previousRouteName, currentRouteName);
        } catch (e) {
          dev.log('AdRouteObserver: Error showing ad: $e');
        }
      });
    }

    _previousRouteName = currentRouteName;
  }

  /// Extract a meaningful route name from the route
  String? _getRouteName(Route<dynamic> route) {
    // For named routes
    if (route.settings.name != null) {
      return route.settings.name;
    }

    // For GoRouter routes that might not have names but have arguments
    if (route.settings.arguments is Map<String, dynamic>) {
      final args = route.settings.arguments as Map<String, dynamic>;
      if (args.containsKey('location')) {
        return _cleanGoRouterPath(args['location'] as String);
      }
    }

    // Try to extract path from any object that might have a 'location' property
    if (route.settings.arguments != null) {
      try {
        final dynamic args = route.settings.arguments;
        if (args.location != null && args.location is String) {
          return _cleanGoRouterPath(args.location as String);
        }
      } catch (_) {
        // Ignore errors trying to access properties
      }
    }

    // Try to get information from route.toString()
    final routeString = route.toString();
    if (routeString.contains('location:')) {
      final locationMatch =
          RegExp(r'location: ([^,\)]+)').firstMatch(routeString);
      if (locationMatch != null && locationMatch.groupCount >= 1) {
        return _cleanGoRouterPath(locationMatch.group(1)!);
      }
    }

    // Last resort: use route type name
    return route.runtimeType.toString();
  }

  /// Clean a GoRouter path to remove parameters
  String _cleanGoRouterPath(String path) {
    final pathSegments = path.split('/');

    // Get just the main route segments, filtering out parameter segments
    final cleanSegments = pathSegments
        .where((segment) => segment.isNotEmpty)
        .where((segment) => !RegExp(r'^\w+:[a-zA-Z0-9-_]+$').hasMatch(segment))
        .toList();

    return '/${cleanSegments.join('/')}';
  }

  /// Determine if we should skip showing ads for certain navigation patterns
  bool _shouldSkipAdForNavigation(String from, String to) {
    // Skip ads for navigations within the same feature
    if (_isSameFeature(from, to)) {
      return true;
    }

    // Skip ads for short-lived screens
    final shortLivedScreens = [
      '/photo-view',
      '/forgot-password',
      '/email-verification',
    ];

    if (shortLivedScreens.contains(from) || shortLivedScreens.contains(to)) {
      return true;
    }

    // Skip ads for quick back navigations
    if (to == '/') {
      return true;
    }

    return false;
  }

  /// Check if two routes are part of the same feature
  bool _isSameFeature(String route1, String route2) {
    final segments1 = route1.split('/').where((s) => s.isNotEmpty).toList();
    final segments2 = route2.split('/').where((s) => s.isNotEmpty).toList();

    if (segments1.isEmpty || segments2.isEmpty) {
      return false;
    }

    // If both routes are part of the same main feature
    return segments1.first == segments2.first;
  }
}
