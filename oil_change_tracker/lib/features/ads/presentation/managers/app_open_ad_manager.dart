import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:oil_change_tracker/core/utils/logger.dart';
import 'package:oil_change_tracker/features/ads/application/app_open_ad_manager.dart';
import 'package:oil_change_tracker/features/subscription/services/subscription_service.dart';

// This class acts as a facade over the real AppOpenAdManager
// By keeping the original provider name, we don't need to update all references
class AppOpenAdManagerNotifier extends Notifier<bool> {
  // Create a new instance with subscription service
  late final AppOpenAdManager _manager;
  bool _initialized = false;
  bool _disposed = false;

  @override
  bool build() {
    // Get the subscription service
    final subscriptionService = ref.watch(subscriptionServiceProvider);

    // Initialize the manager with subscription service
    _manager = AppOpenAdManager(subscriptionService);

    ref.onDispose(() {
      _disposed = true;
      AppLogger.info('AppOpenAdManagerNotifier: Marked as disposed');
    });

    if (!_initialized) {
      _initialized = true;
      // Initialize the manager
      _manager.initialize();
      AppLogger.info('AppOpenAdManagerNotifier: Initialized AppOpenAdManager');

      // Load ad after initialization, but without triggering a rebuild
      Future.microtask(() {
        if (!_disposed) {
          _safeLoadAd();
        }
      });
    }
    return _manager.isAdAvailable;
  }

  // Internal method to safely load ad without directly calling notifyListeners
  void _safeLoadAd() {
    try {
      AppLogger.info('AppOpenAdManagerNotifier: Safe loading ad');
      _manager.showAdIfAvailable();
      // Instead of direct notifyListeners, update state through proper channels
      _safeUpdateState(_manager.isAdAvailable);
    } catch (e) {
      AppLogger.info('AppOpenAdManagerNotifier: Error in _safeLoadAd: $e');
    }
  }

  // Helper method to safely update state, checking for disposal
  void _safeUpdateState(bool newState) {
    if (_disposed) {
      AppLogger.info(
          'AppOpenAdManagerNotifier: Cannot update state, notifier is disposed');
      return;
    }

    try {
      state = newState;
    } catch (e) {
      // Notifier might be disposed, ignore
      AppLogger.info('AppOpenAdManagerNotifier: Error updating state: $e');
    }
  }

  Future<void> loadAd() async {
    // Defer to microtask to avoid modifying providers during build
    Future.microtask(() {
      if (!_disposed) {
        _safeLoadAd();
      }
    });
  }

  Future<void> showAdIfAvailable() async {
    if (_disposed) return;

    AppLogger.info('AppOpenAdManagerNotifier: Showing ad');

    try {
      _manager.showAdIfAvailable();
      // Update state after showing ad, but safely
      Future.microtask(() {
        _safeUpdateState(_manager.isAdAvailable);
      });
    } catch (e) {
      AppLogger.info('AppOpenAdManagerNotifier: Error showing ad: $e');
    }
  }

  void onAppStateChanged(bool isResumed) {
    if (_disposed) return;

    if (isResumed) {
      AppLogger.info('AppOpenAdManagerNotifier: App resumed');
      // Use the safe version to avoid build-time modifications
      Future.microtask(() {
        if (!_disposed) {
          _safeLoadAd();
        }
      });
    }
  }

  // Expose properties
  bool get isAdAvailable => _disposed ? false : _manager.isAdAvailable;
  String? get lastError => _disposed ? null : _manager.lastError;
  bool get isInitialized => _disposed ? false : _manager.isInitialized;

  // Delegate the showAdsAfterLogin property
  set showAdsAfterLogin(bool value) {
    if (!_disposed) {
      _manager.showAdsAfterLogin = value;
    }
  }

  bool get showAdsAfterLogin => _disposed ? false : _manager.showAdsAfterLogin;
}

// Keep the original provider name to avoid breaking existing code
final appOpenAdManagerProvider =
    NotifierProvider<AppOpenAdManagerNotifier, bool>(
  () => AppOpenAdManagerNotifier(),
);
