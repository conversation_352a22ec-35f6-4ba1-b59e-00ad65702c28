// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'interstitial_ad_manager.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$interstitialAdManagerHash() =>
    r'40cb8697d10b84120912deebba4ece4236189d44';

/// See also [InterstitialAdManager].
@ProviderFor(InterstitialAdManager)
final interstitialAdManagerProvider =
    NotifierProvider<InterstitialAdManager, void>.internal(
  InterstitialAdManager.new,
  name: r'interstitialAdManagerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$interstitialAdManagerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$InterstitialAdManager = Notifier<void>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
