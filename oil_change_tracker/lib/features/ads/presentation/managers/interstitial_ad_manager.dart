import 'package:flutter/material.dart';
import 'package:oil_change_tracker/core/utils/logger.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:oil_change_tracker/features/ads/application/interstitial_ad_service.dart';
import 'package:oil_change_tracker/features/subscription/providers/feature_gate_provider.dart';
import 'dart:math' as math;

part 'interstitial_ad_manager.g.dart';

@Riverpod(keepAlive: true)
class InterstitialAdManager extends _$InterstitialAdManager {
  late final InterstitialAdService _adService;

  // Map to track ad frequency for different actions
  final Map<String, DateTime> _lastAdShowTimeByAction = {};
  final Duration _minActionInterval = const Duration(minutes: 5);

  // Map to track ad frequency for different route transitions
  final Map<String, DateTime> _lastAdShowTimeByRoute = {};
  final Duration _minRouteInterval = const Duration(minutes: 10);

  @override
  void build() {
    // Get the real ad service implementation
    _adService = ref.watch(interstitialAdServiceProvider.notifier);
    AppLogger.info('InterstitialAdManager: Connected to ad service');
  }

  Future<void> loadAd() async {
    // Check if user has ad-free subscription
    final hasAdFree = ref.read(featureGateProvider(PremiumFeature.adFree));

    if (hasAdFree) {
      AppLogger.info(
          'InterstitialAdManager: Skipping ad load - user has ad-free subscription');
      return;
    }

    AppLogger.info('InterstitialAdManager: Delegating to ad service');
    _adService.preloadAd();
  }

  void showAdIfAvailable() {
    // Check if user has ad-free subscription
    final hasAdFree = ref.read(featureGateProvider(PremiumFeature.adFree));

    if (hasAdFree) {
      AppLogger.info(
          'InterstitialAdManager: Skipping ad display - user has ad-free subscription');
      return;
    }

    AppLogger.info('InterstitialAdManager: Showing ad via ad service');
    _adService.showAdIfReady();
  }

  // Show ad after specific user actions (with probability)
  Future<void> showAdAfterAction(
      BuildContext context, String actionName) async {
    // Check if user has ad-free subscription
    final hasAdFree = ref.read(featureGateProvider(PremiumFeature.adFree));

    if (hasAdFree) {
      AppLogger.info(
          'InterstitialAdManager: Skipping ad after $actionName - user has ad-free subscription');
      return;
    }

    // Check frequency capping for this specific action
    final now = DateTime.now();
    final lastShown = _lastAdShowTimeByAction[actionName];
    if (lastShown != null && now.difference(lastShown) < _minActionInterval) {
      AppLogger.info(
          'InterstitialAdManager: Too soon to show ad after $actionName');
      return;
    }

    // 50% probability of showing ad after an action
    if (math.Random().nextDouble() > 0.5) {
      AppLogger.info(
          'InterstitialAdManager: Probability check failed for $actionName');
      return;
    }

    // Show ad if available
    if (_adService.isAdReady && _adService.canShowAd) {
      _adService.showAdIfReady();
      _lastAdShowTimeByAction[actionName] = now;
    } else {
      // Preload for next time
      _adService.preloadAd();
    }
  }

  // Show ad between screen transitions
  Future<void> showAdBetweenScreens(
      BuildContext context, String fromRoute, String toRoute) async {
    final routeKey = '${fromRoute}_to_$toRoute';

    // Check if user has ad-free subscription
    final hasAdFree = ref.read(featureGateProvider(PremiumFeature.adFree));

    if (hasAdFree) {
      AppLogger.info(
          'InterstitialAdManager: Skipping ad between screens - user has ad-free subscription');
      return;
    }

    // Check frequency capping for this specific route transition
    final now = DateTime.now();
    final lastShown = _lastAdShowTimeByRoute[routeKey];
    if (lastShown != null && now.difference(lastShown) < _minRouteInterval) {
      AppLogger.info(
          'InterstitialAdManager: Too soon to show ad for $routeKey');
      return;
    }

    // 30% probability of showing ad between screens
    if (math.Random().nextDouble() > 0.3) {
      AppLogger.info(
          'InterstitialAdManager: Probability check failed for $routeKey');
      return;
    }

    // Show ad if available
    if (_adService.isAdReady && _adService.canShowAd) {
      _adService.showAdIfReady();
      _lastAdShowTimeByRoute[routeKey] = now;
    } else {
      // Preload for next time
      _adService.preloadAd();
    }
  }

  // Quick check if we could potentially show an ad for a route transition
  bool canPotentiallyShowAd(String routeKey) {
    // Check if user has ad-free subscription
    final hasAdFree = ref.read(featureGateProvider(PremiumFeature.adFree));

    if (hasAdFree) {
      return false;
    }

    // Check frequency capping
    final lastShown = _lastAdShowTimeByRoute[routeKey];
    if (lastShown != null &&
        DateTime.now().difference(lastShown) < _minRouteInterval) {
      return false;
    }

    return _adService.isAdReady && _adService.canShowAd;
  }

  bool get isAdLoaded {
    // Check if user has ad-free subscription
    final hasAdFree = ref.read(featureGateProvider(PremiumFeature.adFree));

    if (hasAdFree) {
      return false;
    }

    return _adService.isAdReady;
  }

  bool get canShowAd {
    // Check if user has ad-free subscription
    final hasAdFree = ref.read(featureGateProvider(PremiumFeature.adFree));

    if (hasAdFree) {
      return false;
    }

    return _adService.canShowAd;
  }
}
