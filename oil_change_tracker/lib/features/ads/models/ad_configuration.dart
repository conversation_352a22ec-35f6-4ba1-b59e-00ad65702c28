import 'package:flutter/foundation.dart';
import 'dart:io';

/// Configuration model for ad settings
class AdConfiguration {
  // Test mode detection
  static final bool isTestMode = kDebugMode;

  // Android App Open Ad Unit IDs
  static String get androidAppOpenAdUnitId => isTestMode
      ? 'ca-app-pub-3940256099942544/9257395921' // Android Test ID
      : 'ca-app-pub-2630068952962472/1893839045'; // Android Production ID

  // iOS App Open Ad Unit IDs (assuming you don't have iOS ads yet)
  static String get iosAppOpenAdUnitId => isTestMode
      ? 'ca-app-pub-3940256099942544/5575463023' // iOS Test ID
      : 'ca-app-pub-2630068952962472/ios-app-open'; // Placeholder - replace with real ID

  // Android Interstitial Ad Unit IDs
  static String get androidInterstitialAdUnitId => isTestMode
      ? 'ca-app-pub-3940256099942544/1033173712' // Android Test ID
      : 'ca-app-pub-2630068952962472/5641512363'; // Android Production ID

  // iOS Interstitial Ad Unit IDs (assuming you don't have iOS ads yet)
  static String get iosInterstitialAdUnitId => isTestMode
      ? 'ca-app-pub-3940256099942544/4411468910' // iOS Test ID
      : 'ca-app-pub-2630068952962472/ios-interstitial'; // Placeholder - replace with real ID

  // Get correct App Open Ad Unit ID based on platform
  static String get appOpenAdUnitId => Platform.isAndroid
      ? androidAppOpenAdUnitId
      : iosAppOpenAdUnitId;

  // Get correct Interstitial Ad Unit ID based on platform
  static String get interstitialAdUnitId => Platform.isAndroid
      ? androidInterstitialAdUnitId
      : iosInterstitialAdUnitId;

  // Test device IDs
  static List<String> get testDeviceIds => [
    '238C25149125A427A1E432D626488295', // Add your test device ID here
  ];
} 