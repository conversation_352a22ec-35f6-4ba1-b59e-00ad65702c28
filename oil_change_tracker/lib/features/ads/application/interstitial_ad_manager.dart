import 'package:flutter/material.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:oil_change_tracker/core/utils/logger.dart';
import 'interstitial_ad_service.dart';
import 'dart:math';

part 'interstitial_ad_manager.g.dart';

/// Manages when and where to show interstitial ads throughout the app
@riverpod
class InterstitialAdManager extends _$InterstitialAdManager {
  static const int _minInterval = 60; // Minimum seconds between ads
  static const int _probability = 50; // Probability of showing an ad in percentage (50%)
  
  DateTime? _lastAdShownTime;
  final Set<String> _adShownInSession = {};
  final Map<String, DateTime> _lastAdShownForScreen = {};
  
  // Random instance for probability calculations
  final Random _random = Random();
  
  @override
  void build() {
    // Initialize by preloading an ad
    _preloadAd();
    
    // Clean up on disposal
    ref.onDispose(() {
      _adShownInSession.clear();
      _lastAdShownForScreen.clear();
    });
  }
  
  /// Preload an ad for later use
  Future<void> _preloadAd() async {
    final adService = ref.read(interstitialAdServiceProvider.notifier);
    await adService.preloadAd();
  }
  
  /// Check if an ad can be shown based on global frequency cap
  bool _canShowAdGlobally() {
    if (_lastAdShownTime == null) return true;
    
    final currentTime = DateTime.now();
    final difference = currentTime.difference(_lastAdShownTime!).inSeconds;
    
    return difference >= _minInterval;
  }
  
  /// Check if an ad can be shown for a specific screen based on session and time caps
  bool _canShowAdForScreen(String screenName) {
    // Check if ad was already shown in this session for this screen
    if (_adShownInSession.contains(screenName)) {
      return false;
    }
    
    // Check time-based frequency cap for this specific screen
    if (_lastAdShownForScreen.containsKey(screenName)) {
      final lastShown = _lastAdShownForScreen[screenName]!;
      final currentTime = DateTime.now();
      final minutesDifference = currentTime.difference(lastShown).inMinutes;
      
      // More restrictive per-screen caps: 5 minutes for most screens
      return minutesDifference >= 5;
    }
    
    return true;
  }
  
  /// Try to show an ad for a specific screen with the given probability
  /// Returns true if an ad was shown, false otherwise
  Future<bool> showAdForScreen(BuildContext context, String screenName) async {
    // Skip if global frequency cap is not met
    if (!_canShowAdGlobally()) {
      AppLogger.info('AdManager: Skip showing ad due to global frequency cap');
      return false;
    }
    
    // Skip if screen-specific frequency cap is not met
    if (!_canShowAdForScreen(screenName)) {
      AppLogger.info('AdManager: Skip showing ad for $screenName due to screen-specific cap');
      return false;
    }
    
    // Apply probability logic
    if (_random.nextInt(100) >= _probability) {
      AppLogger.info('AdManager: Skip showing ad due to probability');
      return false;
    }
    
        // Get ad service    final adService = ref.read(interstitialAdServiceProvider.notifier);        // Check if ad is ready and can be shown (respects service-level frequency capping)    if (!adService.isAdReady) {      AppLogger.info('AdManager: No ad ready to show');      _preloadAd(); // Preload for next time      return false;    }        // Double-check the service's own frequency capping    if (!adService.canShowAd) {      AppLogger.info('AdManager: Ad service frequency cap active');      return false;    }        // Show the ad    AppLogger.info('AdManager: Showing ad for $screenName');    adService.showAdIfReady();
    
    // Update tracking variables
    _lastAdShownTime = DateTime.now();
    _adShownInSession.add(screenName);
    _lastAdShownForScreen[screenName] = DateTime.now();
    
    // Preload next ad
    Future.delayed(const Duration(seconds: 2), _preloadAd);
    
    return true;
  }
  
  /// Show ad when navigating between screens
  /// This should be called in the route change observer
  Future<bool> showAdBetweenScreens(BuildContext context, String fromScreen, String toScreen) async {
    // Combine screens to create a unique transition identifier
    final transitionKey = '${fromScreen}_to_$toScreen';
    
    return showAdForScreen(context, transitionKey);
  }
  
  /// Show ad after completing a significant action
  /// For example: after adding a car, after adding maintenance record, etc.
  Future<bool> showAdAfterAction(BuildContext context, String actionName) async {
    final actionKey = 'action_$actionName';
    
    return showAdForScreen(context, actionKey);
  }
  
  /// Clear session tracking for a specific screen to allow showing an ad again
  void resetScreenTracking(String screenName) {
    _adShownInSession.remove(screenName);
  }
  
  /// Check if we can potentially show an ad (for UI preparation)
  bool canPotentiallyShowAd(String screenName) {
    return _canShowAdGlobally() && _canShowAdForScreen(screenName);
  }
} 