// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'interstitial_ad_manager.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$interstitialAdManagerHash() =>
    r'6fa0ee5b4a6e1ec922e8a46634a0d0580d21c39b';

/// Manages when and where to show interstitial ads throughout the app
///
/// Copied from [InterstitialAdManager].
@ProviderFor(InterstitialAdManager)
final interstitialAdManagerProvider =
    AutoDisposeNotifierProvider<InterstitialAdManager, void>.internal(
  InterstitialAdManager.new,
  name: r'interstitialAdManagerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$interstitialAdManagerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$InterstitialAdManager = AutoDisposeNotifier<void>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
