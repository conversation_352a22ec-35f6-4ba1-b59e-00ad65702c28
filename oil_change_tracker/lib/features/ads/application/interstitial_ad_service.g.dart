// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'interstitial_ad_service.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$interstitialAdServiceHash() =>
    r'af8762b272af18b0753df4f5babea8514ce12c9a';

/// See also [InterstitialAdService].
@ProviderFor(InterstitialAdService)
final interstitialAdServiceProvider =
    AutoDisposeNotifierProvider<InterstitialAdService, void>.internal(
  InterstitialAdService.new,
  name: r'interstitialAdServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$interstitialAdServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$InterstitialAdService = AutoDisposeNotifier<void>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
