import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'package:flutter/foundation.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:oil_change_tracker/core/utils/logger.dart';
import 'dart:io';
import 'package:oil_change_tracker/features/ads/models/ad_configuration.dart';
import 'dart:async';
import 'package:oil_change_tracker/features/subscription/providers/subscription_provider.dart';

part 'interstitial_ad_service.g.dart';

@riverpod
class InterstitialAdService extends _$InterstitialAdService {
  InterstitialAd? _interstitialAd;
  bool _isLoading = false;
  int _numLoadAttempts = 0;
  static const int _maxLoadAttempts = 3;
  Timer? _retryTimer;

  // TODO: Replace with your real Ad Unit ID for production
  static final String _adUnitId = kDebugMode
      ? Platform.isAndroid
          ? 'ca-app-pub-3940256099942544/**********' // Android Test ID
          : 'ca-app-pub-3940256099942544/**********' // iOS Test ID (Placeholder)
      : Platform.isAndroid
          ? 'ca-app-pub-2630068952962472/**********' // Android Production ID
          : 'YOUR_IOS_INTERSTITIAL_AD_UNIT_ID'; // iOS Production ID (Placeholder)

  // Keep track of the last time an ad was shown to implement frequency capping
  DateTime? _lastAdShowTime;
  final Duration _minInterval =
      const Duration(minutes: 1); // 1 minute minimum interval between ads

  // Check if user has premium subscription
  bool get _hasPremiumSubscription {
    final subscriptionState = ref.read(subscriptionProvider);
    return subscriptionState.hasActiveSubscription;
  }

  @override
  void build() {
    ref.onDispose(() {
      _interstitialAd?.dispose();
      _retryTimer?.cancel();
    });
  }

  Future<void> preloadAd() async {
    // Check for premium subscription first and skip loading ads completely
    if (_hasPremiumSubscription) {
      AppLogger.info(
          'InterstitialAdService: Skipping ad load - user has premium subscription');
      return;
    }

    if (_interstitialAd != null) {
      AppLogger.info('InterstitialAdService: Ad already loaded');
      return;
    }

    if (_isLoading) {
      AppLogger.info('InterstitialAdService: Ad already loading');
      return;
    }

    if (_numLoadAttempts >= _maxLoadAttempts) {
      AppLogger.warning('InterstitialAdService: Exceeded max load attempts');
      _scheduleRetry();
      return;
    }

    _isLoading = true;
    _numLoadAttempts++;

    try {
      AppLogger.info('InterstitialAdService: Loading interstitial ad...');

      // Add timeout for ad loading
      Timer(const Duration(seconds: 30), () {
        if (_isLoading) {
          AppLogger.warning(
              'InterstitialAdService: Ad load timed out after 30 seconds');
          _isLoading = false;
          _scheduleRetry();
        }
      });

      await InterstitialAd.load(
        adUnitId: AdConfiguration.interstitialAdUnitId,
        request: const AdRequest(),
        adLoadCallback: InterstitialAdLoadCallback(
          onAdLoaded: (InterstitialAd ad) {
            AppLogger.info('InterstitialAdService: Ad loaded successfully');
            _interstitialAd = ad;
            _isLoading = false;
            _numLoadAttempts = 0;

            // Set up callbacks
            _interstitialAd!.fullScreenContentCallback =
                FullScreenContentCallback(
              onAdShowedFullScreenContent: (InterstitialAd ad) {
                AppLogger.info(
                    'InterstitialAdService: Ad showed full screen content');
              },
              onAdDismissedFullScreenContent: (InterstitialAd ad) {
                AppLogger.info(
                    'InterstitialAdService: Ad dismissed full screen content');
                ad.dispose();
                _interstitialAd = null;

                // Preload the next ad
                Future.delayed(const Duration(seconds: 1), () {
                  if (!_isLoading) {
                    preloadAd();
                  }
                });
              },
              onAdFailedToShowFullScreenContent:
                  (InterstitialAd ad, AdError error) {
                AppLogger.error(
                    'InterstitialAdService: Ad failed to show full screen content',
                    error);
                ad.dispose();
                _interstitialAd = null;
              },
            );
          },
          onAdFailedToLoad: (LoadAdError error) {
            AppLogger.error('InterstitialAdService: Failed to load ad', error);
            _interstitialAd = null;
            _isLoading = false;

            _scheduleRetry();
          },
        ),
      );
    } catch (e) {
      AppLogger.error('InterstitialAdService: Exception during ad load', e);
      _isLoading = false;
      _scheduleRetry();
    }
  }

  void _scheduleRetry() {
    // Cancel any existing retry timer
    _retryTimer?.cancel();

    // Calculate delay based on attempts (exponential backoff)
    final delaySeconds = (_numLoadAttempts * 5).clamp(5, 60);

    AppLogger.info(
        'InterstitialAdService: Scheduling retry in $delaySeconds seconds');

    _retryTimer = Timer(Duration(seconds: delaySeconds), () {
      AppLogger.info('InterstitialAdService: Attempting retry');
      _numLoadAttempts = 0; // Reset attempts for the retry
      preloadAd();
    });
  }

  // Check if we can show an ad (respects frequency capping)
  bool get canShowAd {
    // If user has premium subscription, never show ads
    if (_hasPremiumSubscription) {
      AppLogger.info(
          'InterstitialAdService: Skip showing ad - user has premium subscription');
      return false;
    }

    // Check frequency capping
    if (_lastAdShowTime != null) {
      final now = DateTime.now();
      final difference = now.difference(_lastAdShowTime!);
      if (difference < _minInterval) {
        AppLogger.info(
            'InterstitialAdService: Skip showing ad due to frequency cap');
        return false;
      }
    }
    return true;
  }

  // Check if ad is loaded and ready to show
  bool get isAdReady => _interstitialAd != null;

  // Show ad if it's ready and respects frequency capping
  void showAdIfReady() {
    // Skip if user has premium subscription
    if (_hasPremiumSubscription) {
      AppLogger.info(
          'InterstitialAdService: Skip showing ad - user has premium subscription');
      return;
    }

    if (_interstitialAd == null) {
      AppLogger.info('InterstitialAdService: No ad available to show');
      preloadAd();
      return;
    }

    if (!canShowAd) {
      AppLogger.info(
          'InterstitialAdService: Cannot show ad due to frequency cap');
      return;
    }

    try {
      _interstitialAd!.show();
      _lastAdShowTime = DateTime.now();
    } catch (e) {
      AppLogger.error('InterstitialAdService: Error showing ad', e);
    }
  }
}
