import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/models/maintenance_model.dart';
import '../data/repositories/maintenance_repository.dart';
import 'dart:developer' as dev;
import 'dart:async';
import '../../../generated/app_localizations.dart';

final maintenanceProvider = StreamProvider.family<List<MaintenanceModel>, String>((ref, carId) {
  final repository = ref.watch(maintenanceRepositoryProvider);
  return repository.getMaintenanceForCar(carId);
});

final maintenanceNotifierProvider = StateNotifierProvider.family<MaintenanceNotifier, AsyncValue<List<MaintenanceModel>>, String>((ref, carId) {
  final repository = ref.watch(maintenanceRepositoryProvider);
  return MaintenanceNotifier(ref, repository, carId);
});

class MaintenanceNotifier extends StateNotifier<AsyncValue<List<MaintenanceModel>>> {
  final MaintenanceRepository _repository;
  final String _carId;
  final Ref _ref;
  S? _l10n;

  MaintenanceNotifier(this._ref, this._repository, this._carId) : super(const AsyncValue.loading()) {
    _loadMaintenance();
  }

  // Method to set localization for the notifier
  void setL10n(S l10n) {
    _l10n = l10n;
    _repository.setL10n(l10n);
  }

  Future<void> _loadMaintenance() async {
    try {
      final maintenance = await _repository.getMaintenanceForCar(_carId).first;
      if (mounted) {
        state = AsyncValue.data(maintenance);
      }
    } catch (error, stackTrace) {
      if (mounted) {
        state = AsyncValue.error(error, stackTrace);
      }
    }
  }

  Future<void> addMaintenance(MaintenanceModel maintenance) async {
    try {
      // Save the current state
      final previousState = state;
      
      // Optimistically update the UI with the new maintenance record
      if (previousState is AsyncData<List<MaintenanceModel>>) {
        final updatedList = [
          maintenance.copyWith(id: 'temp-${DateTime.now().millisecondsSinceEpoch}'),
          ...previousState.value
        ];
        state = AsyncData(updatedList);
      }
      
      // Add the maintenance to Firestore
      await _repository.addMaintenance(maintenance);
      
      // Force reload data from Firestore to get the server-generated ID
      await _loadMaintenance();
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
      rethrow; // Rethrow to let the UI handle the error
    }
  }

  Future<void> updateMaintenance(MaintenanceModel maintenance) async {
    try {
      // Save current state
      final previousState = state;
      
      // Optimistically update UI
      if (previousState is AsyncData<List<MaintenanceModel>>) {
        final updatedList = previousState.value.map((item) => 
          item.id == maintenance.id ? maintenance : item
        ).toList();
        state = AsyncData(updatedList);
      }
      
      // Update in repository
      await _repository.updateMaintenance(maintenance);
      
      // Force reload to ensure data consistency
      await _loadMaintenance();
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
      rethrow;
    }
  }

  Future<void> deleteMaintenance(String maintenanceId) async {
    // Save the current state and the specific maintenance to be deleted
    final previousState = state;
    MaintenanceModel? deletedMaintenance;
    
    if (previousState is AsyncData<List<MaintenanceModel>>) {
      // Find the maintenance record being deleted
      for (final item in previousState.value) {
        if (item.id == maintenanceId) {
          deletedMaintenance = item;
          break;
        }
      }
      
      // Optimistically update the UI by removing the maintenance record
      final updatedList = previousState.value
          .where((item) => item.id != maintenanceId)
          .toList();
      
      // Update state with optimistic removal
      state = AsyncData(updatedList);
    }
    
    try {
      // Log the deletion attempt
      dev.log('MaintenanceNotifier: Attempting to delete maintenance $maintenanceId');
      
      // Call repository to delete the maintenance record
      final success = await _repository.deleteMaintenance(maintenanceId, _carId);
      
      if (success) {
        dev.log('MaintenanceNotifier: Successfully deleted maintenance $maintenanceId');
        // No need to invalidate any providers since we're using the same provider for UI
      } else {
        dev.log('MaintenanceNotifier: Deletion reported as unsuccessful for $maintenanceId');
        
        // If deletion was unsuccessful but didn't throw, restore previous state
        if (previousState is AsyncData<List<MaintenanceModel>> && 
            deletedMaintenance != null) {
          dev.log('MaintenanceNotifier: Restoring previous state after deletion failure');
          state = previousState;
        }
        
        throw Exception('Failed to delete maintenance record');
      }
    } catch (error, stackTrace) {
      dev.log('MaintenanceNotifier: Error in deleteMaintenance: $error');
      
      // Check if the error message indicates the maintenance record doesn't exist
      final errorStr = error.toString().toLowerCase();
      final isNotFoundError = errorStr.contains('not found') || 
                             errorStr.contains('not-found') ||
                             errorStr.contains('no document');
      
      if (isNotFoundError) {
        // If the document doesn't exist, consider the deletion successful
        dev.log('MaintenanceNotifier: Document not found, considering deletion successful');
        
        // Keep the optimistically updated state (with item removed) and don't restore
        // No need to throw an exception
        return; // Exit without throwing
      }
      
      // For network errors, we might want to keep the optimistic state update
      // to avoid a jarring UI experience, but still notify the user of the error
      final isNetworkError = errorStr.contains('network') || 
                           errorStr.contains('connection') ||
                           errorStr.contains('timeout') ||
                           errorStr.contains('socket');
      
      if (!isNetworkError) {
        // For non-network errors, restore previous state if we had valid data
        if (previousState is AsyncData<List<MaintenanceModel>> && 
            deletedMaintenance != null) {
          dev.log('MaintenanceNotifier: Restoring previous state after deletion error');
          state = previousState;
        } else {
          // Update error state
          state = AsyncValue.error(error, stackTrace);
        }
      }
      
      // Rethrow for UI error handling
      rethrow;
    }
  }
} 