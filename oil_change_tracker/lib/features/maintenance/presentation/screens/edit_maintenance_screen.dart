import 'dart:io';
import 'dart:developer' as dev;
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../../../../core/providers/auth_provider.dart';
import '../../../../core/models/maintenance_model.dart';
import '../../../../core/theme/theme_extensions.dart';
import '../../../../generated/app_localizations.dart';
import '../../../../shared/widgets/custom_text_field.dart';
import '../../../car_management/providers/car_provider.dart';
import '../../providers/maintenance_provider.dart';
import '../../data/repositories/maintenance_repository.dart';
import '../widgets/maintenance_photo_picker.dart';

class EditMaintenanceScreen extends ConsumerStatefulWidget {
  final String maintenanceId;
  final String carId;

  const EditMaintenanceScreen({
    super.key,
    required this.maintenanceId,
    required this.carId,
  });

  @override
  ConsumerState<EditMaintenanceScreen> createState() =>
      _EditMaintenanceScreenState();
}

class _EditMaintenanceScreenState extends ConsumerState<EditMaintenanceScreen> {
  final _formKey = GlobalKey<FormState>();
  final _descriptionController = TextEditingController();
  final _mileageController = TextEditingController();
  final _costController = TextEditingController();
  final _serviceProviderController = TextEditingController();
  final _notesController = TextEditingController();
  DateTime _date = DateTime.now();
  String _selectedMaintenanceType = 'generalService';
  bool _isLoading = false;
  bool _dataLoaded = false;
  MaintenanceModel? _originalMaintenance;

  // List to store selected photo files
  final List<File> _selectedPhotoFiles = [];

  // List to store existing photo URLs
  List<String> _existingPhotoUrls = [];

  // List to store photo URLs that should be deleted
  final List<String> _photosToDelete = [];

  List<String> _getMaintenanceTypes() {
    return [
      'generalService',
      'brakeService',
      'engineService',
      'transmissionService',
      'tireService',
      'batteryService',
      'airConditioning',
      'electricalSystem',
      'suspension',
      'exhaustSystem',
      'fuelSystem',
      'coolingSystem',
      'regularMaintenance',
      'other'
    ];
  }

  String _getLocalizedMaintenanceType(String type, S l10n) {
    switch (type) {
      case 'generalService':
        return l10n.generalService;
      case 'brakeService':
        return l10n.brakeService;
      case 'engineService':
        return l10n.engineService;
      case 'transmissionService':
        return l10n.transmissionService;
      case 'tireService':
        return l10n.tireService;
      case 'batteryService':
        return l10n.batteryService;
      case 'airConditioning':
        return l10n.airConditioning;
      case 'electricalSystem':
        return l10n.electricalSystem;
      case 'suspension':
        return l10n.suspension;
      case 'exhaustSystem':
        return l10n.exhaustSystem;
      case 'fuelSystem':
        return l10n.fuelSystem;
      case 'coolingSystem':
        return l10n.coolingSystem;
      case 'regularMaintenance':
        return l10n.regularMaintenance;
      case 'other':
        return l10n.other;
      default:
        return l10n.generalService;
    }
  }

  @override
  void initState() {
    super.initState();
    _loadMaintenanceData();
  }

  Future<void> _loadMaintenanceData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final maintenanceList =
          await ref.read(maintenanceProvider(widget.carId).future);
      final maintenance = maintenanceList.firstWhere(
        (m) => m.id == widget.maintenanceId,
        orElse: () => throw Exception('Maintenance record not found'),
      );

      setState(() {
        _originalMaintenance = maintenance;
        _descriptionController.text = maintenance.description;
        _mileageController.text = maintenance.mileage.toString();
        _costController.text = maintenance.cost.toString();
        _serviceProviderController.text = maintenance.serviceProvider;
        _notesController.text = maintenance.notes;
        _date = maintenance.date;
        _selectedMaintenanceType = maintenance.maintenanceType;
        _existingPhotoUrls = List<String>.from(maintenance.photoUrls);
        _dataLoaded = true;
        _isLoading = false;
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading maintenance: $e'),
            backgroundColor: context.secondaryAccentColor,
          ),
        );
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  void dispose() {
    _descriptionController.dispose();
    _mileageController.dispose();
    _costController.dispose();
    _serviceProviderController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  // Handler for when a photo is selected
  void _handlePhotoSelected(File photoFile) {
    setState(() {
      _selectedPhotoFiles.add(photoFile);
    });
  }

  // Handler for removing an existing photo
  void _handleRemoveExistingPhoto(String photoUrl) {
    setState(() {
      _existingPhotoUrls.remove(photoUrl);
      _photosToDelete.add(photoUrl);
    });
  }

  // Handler for removing a newly selected photo file
  void _handleRemoveSelectedPhoto(int index) {
    if (index >= 0 && index < _selectedPhotoFiles.length) {
      setState(() {
        _selectedPhotoFiles.removeAt(index);
      });
    }
  }

  Future<void> _updateMaintenance() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // Show confirmation dialog
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) {
        final l10n = S.of(context);
        return AlertDialog(
          backgroundColor: context.containerBackgroundColor,
          title: Text(
            l10n.saveChanges,
            style: TextStyle(color: context.accentColor),
          ),
          content: Text(
            "${l10n.saveChanges}?",
            style: TextStyle(color: context.primaryTextColor),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: Text(
                l10n.cancel,
                style: TextStyle(color: context.accentColor),
              ),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              style: ElevatedButton.styleFrom(
                backgroundColor: context.secondaryAccentColor,
              ),
              child: Text(
                l10n.save,
                style: const TextStyle(color: Colors.white),
              ),
            ),
          ],
        );
      },
    );

    if (confirmed != true) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // Check authentication status
      User? currentUser;
      try {
        currentUser = FirebaseAuth.instance.currentUser;

        if (currentUser == null) {
          final authState = ref.read(authStateChangesProvider).value;
          if (authState != null) {
            currentUser = authState;
          } else {
            throw Exception('User not authenticated. Please sign in again.');
          }
        }

        String? currentToken = await currentUser.getIdToken(false);
        if (currentToken?.isEmpty ?? true) {
          await currentUser.getIdToken(true);
        }

        if (FirebaseAuth.instance.currentUser == null) {
          throw Exception('Authentication error. Please sign in again.');
        }
      } catch (authError) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(authError.toString().contains('sign in again')
                  ? S.of(context).authenticationError
                  : '${S.of(context).authenticationError}: ${authError.toString().split('Exception:').last}'),
              backgroundColor: context.secondaryAccentColor,
              duration: const Duration(seconds: 5),
              action: SnackBarAction(
                label: S.of(context).signInAgain,
                onPressed: () => context.go('/login'),
              ),
            ),
          );
          setState(() => _isLoading = false);
        }
        return;
      }

      if (_originalMaintenance == null) {
        throw Exception('Original maintenance data not found');
      }

      // Get the updated maintenance model
      final updatedMaintenance = _originalMaintenance!.copyWith(
        description: _descriptionController.text,
        maintenanceType: _selectedMaintenanceType,
        mileage: int.parse(_mileageController.text),
        cost: double.parse(
            _costController.text.isEmpty ? '0' : _costController.text),
        serviceProvider: _serviceProviderController.text,
        notes: _notesController.text,
        date: _date,
        updatedAt: DateTime.now(),
      );

      // Get the repository
      final maintenanceRepo = ref.read(maintenanceRepositoryProvider);
      // Set the localization object on the repository
      maintenanceRepo.setL10n(S.of(context));

      // Delete removed photos
      for (final photoUrl in _photosToDelete) {
        await maintenanceRepo.deleteMaintenancePhoto(photoUrl);
      }

      // Upload new photos
      final List<String> newPhotoUrls = List<String>.from(_existingPhotoUrls);
      for (final photoFile in _selectedPhotoFiles) {
        try {
          dev.log('EditMaintenanceScreen: Uploading photo: ${photoFile.path}');

          // Check if file exists and is readable
          if (!await photoFile.exists()) {
            dev.log(
                'EditMaintenanceScreen: Photo file does not exist: ${photoFile.path}');
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                    content:
                        Text('Error: Photo file not found or inaccessible')),
              );
            }
            continue;
          }

          final photoUrl = await maintenanceRepo.uploadMaintenancePhoto(
              photoFile, widget.carId, widget.maintenanceId);

          if (photoUrl != null) {
            newPhotoUrls.add(photoUrl);
            dev.log(
                'EditMaintenanceScreen: Photo uploaded successfully: $photoUrl');
          } else {
            dev.log(
                'EditMaintenanceScreen: Failed to upload photo: ${photoFile.path}');
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('Failed to upload photo')),
              );
            }
          }
        } catch (uploadError) {
          dev.log(
              'EditMaintenanceScreen: Error during photo upload: $uploadError');
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                  content: Text(
                      'Error uploading photo: ${uploadError.toString().split('Exception:').last}')),
            );
          }
        }
      }

      // Update the maintenance record with updated photo URLs
      final finalUpdatedMaintenance =
          updatedMaintenance.copyWith(photoUrls: newPhotoUrls);
      await maintenanceRepo.updateMaintenance(finalUpdatedMaintenance);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(S.of(context).maintenanceUpdated)),
        );
        context.pop(true);
      }
    } catch (e) {
      dev.log('EditMaintenanceScreen: Error updating maintenance: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
                '${S.of(context).error}: ${e.toString().split('Exception:').last}'),
            backgroundColor: context.secondaryAccentColor,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final l10n = S.of(context);
    final carsAsync = ref.watch(carsProvider);

    return Scaffold(
      backgroundColor: context.containerBackgroundColor,
      appBar: AppBar(
        title: Text(
          l10n.editMaintenance,
          style: TextStyle(
            color: context.accentColor,
            fontWeight: FontWeight.bold,
            fontSize: 22,
          ),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        iconTheme: IconThemeData(color: context.accentColor),
      ),
      body: Stack(
        children: [
          if (!_dataLoaded) ...[
            Center(
              child: CircularProgressIndicator(
                valueColor:
                    AlwaysStoppedAnimation<Color>(context.accentColor),
              ),
            )
          ] else ...[
            SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    _buildFormFields(context, l10n),
                  ],
                ),
              ),
            ),
          ],
          if (_isLoading)
            const Opacity(
              opacity: 0.8,
              child: ModalBarrier(dismissible: false, color: Colors.black),
            ),
          if (_isLoading)
            const Center(
              child: CircularProgressIndicator(),
            ),
        ],
      ),
    );
  }

  Widget _buildFormFields(BuildContext context, S l10n) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        // Maintenance type and details
        Card(
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          margin: const EdgeInsets.only(bottom: 16),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(Icons.build, color: context.accentColor, size: 22),
                    const SizedBox(width: 8),
                    Text(
                      l10n.maintenanceType,
                      style: TextStyle(
                        color: context.accentColor,
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                DropdownButtonFormField<String>(
                  value: _selectedMaintenanceType,
                  decoration: InputDecoration(
                    labelText: l10n.maintenanceType,
                    labelStyle: TextStyle(color: context.accentColor),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(
                          color: context.accentColor.withOpacity(0.3)),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(
                          color: context.accentColor.withOpacity(0.5)),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: context.accentColor),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                        horizontal: 16, vertical: 16),
                    filled: true,
                    fillColor: context.containerBackgroundColor,
                  ),
                  dropdownColor: context.containerBackgroundColor,
                  style: TextStyle(color: context.primaryTextColor, fontSize: 16),
                  borderRadius: BorderRadius.circular(12),
                  icon: Icon(Icons.arrow_drop_down, color: context.accentColor),
                  items: _getMaintenanceTypes().map((type) {
                    return DropdownMenuItem(
                      value: type,
                      child: Text(
                        _getLocalizedMaintenanceType(type, l10n),
                        style: TextStyle(color: context.primaryTextColor),
                      ),
                    );
                  }).toList(),
                  onChanged: (value) {
                    if (value != null) {
                      setState(() {
                        _selectedMaintenanceType = value;
                      });
                    }
                  },
                ),
                const SizedBox(height: 16),

                // Description field
                CustomTextField(
                  controller: _descriptionController,
                  labelText: l10n.maintenanceDescription,
                  prefixIcon: Icon(Icons.description,
                      color: context.accentColor.withOpacity(0.7)),
                  maxLines: 3,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return l10n.maintenanceDescription;
                    }
                    return null;
                  }, suffixText: '',
                ),
              ],
            ),
          ),
        ),

        // Mileage and cost details
        Card(
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          margin: const EdgeInsets.only(bottom: 16),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(Icons.speed, color: context.accentColor, size: 22),
                    const SizedBox(width: 8),
                    Text(
                      l10n.currentMileage,
                      style: TextStyle(
                        color: context.accentColor,
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                // Mileage field
                CustomTextField(
                  controller: _mileageController,
                  labelText: l10n.currentMileage,
                  keyboardType: TextInputType.number,
                  prefixIcon: Icon(Icons.speed,
                      color: context.accentColor.withOpacity(0.7)),
                  suffixText: 'KM',
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return l10n.pleaseEnterMileage;
                    }
                    final mileage = int.tryParse(value);
                    if (mileage == null) {
                      return l10n.invalidMileage;
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // Cost field
                CustomTextField(
                  controller: _costController,
                  labelText: l10n.cost,
                  keyboardType: const TextInputType.numberWithOptions(decimal: true),
                  prefixIcon: Icon(Icons.attach_money,
                      color: context.accentColor.withOpacity(0.7)),
                  placeholder: l10n.pleaseEnterCost, suffixText: '',
                ),
              ],
            ),
          ),
        ),

        // Service Provider Name
        const SizedBox(height: 16),
        CustomTextField(
          controller: _serviceProviderController,
          labelText: l10n.serviceProvider,
          keyboardType: TextInputType.text, suffixText: '',
        ),

        // Date, notes and photos card
        Card(
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          margin: const EdgeInsets.only(bottom: 24),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(Icons.event_note, color: context.accentColor, size: 22),
                    const SizedBox(width: 8),
                    Text(
                      l10n.date,
                      style: TextStyle(
                        color: context.accentColor,
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                // Date picker
                _buildDatePicker(context),
                const SizedBox(height: 16),

                // Notes field
                CustomTextField(
                  controller: _notesController,
                  labelText: l10n.notes,
                  prefixIcon: Icon(Icons.note,
                      color: context.accentColor.withOpacity(0.7)),
                  maxLines: 3,
                  placeholder: '${l10n.notes} (${l10n.optional})', suffixText: '',
                ),
                const SizedBox(height: 16),

                // Existing Photos Section
                if (_existingPhotoUrls.isNotEmpty) ...[
                  Text(
                    l10n.receiptPhotos,
                    style: TextStyle(
                      color: context.accentColor,
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  MaintenancePhotoPicker(
                    photoUrls: _existingPhotoUrls,
                    onPhotoSelected: (_) {}, // Not used for existing photos
                    onPhotoRemoved: _handleRemoveExistingPhoto,
                    isEditing: true,
                    showTitle: false,
                    showButtons: false,
                    borderColor: context.accentColor.withOpacity(0.5),
                    backgroundColor: context.containerBackgroundColor,
                    borderRadius: 12.0,
                  ),
                  const SizedBox(height: 16),
                ],

                // New Photos Section
                MaintenancePhotoPicker(
                  photoUrls: _selectedPhotoFiles.map((file) => file.path).toList(),
                  onPhotoSelected: _handlePhotoSelected,
                  onPhotoRemoved: (path) {
                    final index =
                        _selectedPhotoFiles.indexWhere((file) => file.path == path);
                    if (index >= 0) {
                      _handleRemoveSelectedPhoto(index);
                    }
                  },
                  isEditing: true,
                  showTitle: false,
                  borderColor: context.accentColor.withOpacity(0.5),
                  backgroundColor: context.containerBackgroundColor,
                  borderRadius: 12.0,
                ),
              ],
            ),
          ),
        ),

        // Save button
        ElevatedButton(
          onPressed: _isLoading ? null : _updateMaintenance,
          style: ElevatedButton.styleFrom(
            backgroundColor: context.secondaryAccentColor,
            foregroundColor: context.isDarkMode ? Colors.black : Colors.white,
            padding: const EdgeInsets.symmetric(vertical: 16),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(30),
            ),
            elevation: 3,
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.save),
              const SizedBox(width: 8),
              Text(
                _isLoading ? l10n.loading : l10n.saveChanges,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildDatePicker(BuildContext context) {
    final l10n = S.of(context);
    return InkWell(
      onTap: () async {
        final picked = await showDatePicker(
          context: context,
          initialDate: _date,
          firstDate: DateTime(2000),
          lastDate: DateTime.now(),
        );
        if (picked != null) {
          setState(() => _date = picked);
        }
      },
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
        decoration: BoxDecoration(
          border: Border.all(color: context.accentColor.withOpacity(0.5)),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          children: [
            Icon(Icons.calendar_today, color: context.accentColor, size: 20),
            const SizedBox(width: 12),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  l10n.date,
                  style: TextStyle(
                    color: context.secondaryTextColor,
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '${_date.year}-${_date.month.toString().padLeft(2, '0')}-${_date.day.toString().padLeft(2, '0')}',
                  style: TextStyle(
                    color: context.primaryTextColor,
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
              ],
            ),
            const Spacer(),
            Icon(Icons.arrow_drop_down, color: context.accentColor),
          ],
        ),
      ),
    );
  }
}
