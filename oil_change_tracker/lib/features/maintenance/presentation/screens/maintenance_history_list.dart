import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:timeline_tile/timeline_tile.dart';
import 'package:go_router/go_router.dart';
import '../../../../generated/app_localizations.dart';
import '../../../../core/models/maintenance_model.dart';
import '../../providers/maintenance_provider.dart';
import '../../../../core/theme/theme_extensions.dart';

class MaintenanceHistoryList extends ConsumerWidget {
  final String carId;
  final bool useNotifierProvider;

  const MaintenanceHistoryList({
    super.key,
    required this.carId,
    this.useNotifierProvider = false,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Use different provider based on the useNotifierProvider flag
    final maintenanceAsync = useNotifierProvider 
        ? ref.watch(maintenanceNotifierProvider(carId))
        : ref.watch(maintenanceProvider(carId));
    final l10n = S.of(context);

    // Only refresh on error, not on every build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (maintenanceAsync is AsyncError) {
        if (useNotifierProvider) {
          ref.invalidate(maintenanceNotifierProvider(carId));
        } else {
          ref.invalidate(maintenanceProvider(carId));
        }
      }
    });

    return maintenanceAsync.when(
      data: (maintenanceList) {
        if (maintenanceList.isEmpty) {
          return RefreshIndicator(
            onRefresh: () async {
              if (useNotifierProvider) {
                ref.invalidate(maintenanceNotifierProvider(carId));
              } else {
                ref.invalidate(maintenanceProvider(carId));
              }
            },
            color: context.accentColor,
            child: ListView(
              physics: const AlwaysScrollableScrollPhysics(),
              children: [
                SizedBox(
                  height: MediaQuery.of(context).size.height * 0.7,
                  child: Center(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 32),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Container(
                            padding: const EdgeInsets.all(32),
                            decoration: BoxDecoration(
                              color: context.accentColor.withOpacity(0.1),
                              shape: BoxShape.circle,
                            ),
                            child: Icon(
                              Icons.build_outlined,
                              size: 96,
                              color: context.accentColor,
                            ),
                          ),
                          const SizedBox(height: 32),
                          Text(
                            l10n.noMaintenanceRecords,
                            style: TextStyle(
                              color: context.secondaryAccentColor,
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                            ),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 16),
                          Text(
                            l10n.recordMaintenance,
                            style: TextStyle(
                              color: context.secondaryTextColor,
                              fontSize: 18,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          );
        }

        // Sort maintenance records by date (newest first)
        final sortedMaintenance = List<MaintenanceModel>.from(maintenanceList)
          ..sort((a, b) => b.date.compareTo(a.date));
        
        // Calculate total maintenance cost
        final totalCost = sortedMaintenance.fold<double>(
          0, (previousValue, maintenance) => previousValue + maintenance.cost);
        
        // Format the total cost with the correct locale
        final formattedTotalCost = NumberFormat.currency(
          symbol: '', 
          decimalDigits: 2,
        ).format(totalCost);

        return RefreshIndicator(
          onRefresh: () async {
            if (useNotifierProvider) {
              ref.invalidate(maintenanceNotifierProvider(carId));
            } else {
              ref.invalidate(maintenanceProvider(carId));
            }
          },
          color: context.accentColor,
          child: ListView(
            physics: const AlwaysScrollableScrollPhysics(),
            children: [
              // Summary card with total cost
              if (sortedMaintenance.isNotEmpty)
                Padding(
                  padding: const EdgeInsets.fromLTRB(16, 16, 16, 0),
                  child: Card(
                    color: context.containerBackgroundColor,
                    elevation: 4,
                    surfaceTintColor: Colors.transparent,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(16),
                      side: BorderSide(color: context.accentColor, width: 1),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 24),
                      child: Column(
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                l10n.totalMaintenanceCost,
                                style: TextStyle(
                                  color: context.primaryTextColor,
                                  fontSize: 16,
                                ),
                              ),
                              Text(
                                formattedTotalCost,
                                style: TextStyle(
                                  color: context.secondaryAccentColor,
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                l10n.totalRecords,
                                style: TextStyle(
                                  color: context.secondaryTextColor,
                                  fontSize: 14,
                                ),
                              ),
                              Container(
                                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                                decoration: BoxDecoration(
                                  color: context.containerBackgroundColor,
                                  borderRadius: BorderRadius.circular(12),
                                  border: Border.all(color: context.accentColor.withOpacity(0.5)),
                                ),
                                child: Text(
                                  '${sortedMaintenance.length}',
                                  style: TextStyle(
                                    color: context.accentColor,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              
              // Timeline of maintenance records
              for (int index = 0; index < sortedMaintenance.length; index++) 
                Builder(
                  builder: (context) {
                    final maintenance = sortedMaintenance[index];
                    final isFirst = index == 0;
                    final isLast = index == sortedMaintenance.length - 1;
                    
                    return Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: TimelineTile(
                        alignment: TimelineAlign.manual,
                        lineXY: 0.1,
                        isFirst: isFirst,
                        isLast: isLast,
                        indicatorStyle: IndicatorStyle(
                          width: 16,
                          height: 16,
                          color: isFirst ? context.accentColor : context.containerBackgroundColor,
                          padding: const EdgeInsets.all(0),
                          indicator: Container(
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color: isFirst ? context.accentColor : context.containerBackgroundColor,
                              border: Border.all(
                                width: 2,
                                color: context.accentColor,
                              ),
                            ),
                          ),
                        ),
                        beforeLineStyle: LineStyle(
                          color: context.accentColor.withOpacity(0.3),
                          thickness: 2,
                        ),
                        afterLineStyle: LineStyle(
                          color: context.accentColor.withOpacity(0.3),
                          thickness: 2,
                        ),
                        endChild: Padding(
                          padding: const EdgeInsets.only(left: 16.0, bottom: 16.0),
                          child: _buildMaintenanceCard(context, maintenance),
                        ),
                      ),
                    );
                  }
                ),
            ],
          ),
        );
      },
      loading: () => Center(
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(context.accentColor),
        ),
      ),
      error: (error, _) => Center(
        child: Text(
          error.toString(),
          style: TextStyle(color: context.secondaryAccentColor),
        ),
      ),
    );
  }

  String _getLocalizedMaintenanceType(String type, S l10n) {
    switch (type) {
      case 'generalService':
        return l10n.generalService;
      case 'brakeService':
        return l10n.brakeService;
      case 'engineService':
        return l10n.engineService;
      case 'transmissionService':
        return l10n.transmissionService;
      case 'tireService':
        return l10n.tireService;
      case 'batteryService':
        return l10n.batteryService;
      case 'airConditioning':
        return l10n.airConditioning;
      case 'electricalSystem':
        return l10n.electricalSystem;
      case 'suspension':
        return l10n.suspension;
      case 'exhaustSystem':
        return l10n.exhaustSystem;
      case 'fuelSystem':
        return l10n.fuelSystem;
      case 'coolingSystem':
        return l10n.coolingSystem;
      case 'regularMaintenance':
        return l10n.regularMaintenance;
      case 'other':
        return l10n.other;
      default:
        return l10n.generalService;
    }
  }

  IconData _getMaintenanceIcon(String type) {
    switch (type) {
      case 'brakeService':
        return Icons.warning;
      case 'engineService':
        return Icons.precision_manufacturing;
      case 'transmissionService':
        return Icons.settings;
      case 'tireService':
        return Icons.circle;
      case 'batteryService':
        return Icons.battery_charging_full;
      case 'airConditioning':
        return Icons.ac_unit;
      case 'electricalSystem':
        return Icons.electrical_services;
      case 'suspension':
        return Icons.line_weight;
      case 'exhaustSystem':
        return Icons.air;
      case 'fuelSystem':
        return Icons.local_gas_station;
      case 'coolingSystem':
        return Icons.water_drop;
      case 'regularMaintenance':
        return Icons.calendar_month;
      default:
        return Icons.handyman;
    }
  }

  Widget _buildMaintenanceCard(BuildContext context, MaintenanceModel maintenance) {
    final l10n = S.of(context);
    final maintenanceType = _getLocalizedMaintenanceType(maintenance.maintenanceType, l10n);
    final iconData = _getMaintenanceIcon(maintenance.maintenanceType);
    final formattedDate = DateFormat.yMMMd().format(maintenance.date);
    final formattedCost = NumberFormat.currency(
      symbol: '', 
      decimalDigits: 2,
    ).format(maintenance.cost);
    
    return GestureDetector(
      onTap: () {
        // Navigate to maintenance details screen using GoRouter
        context.push('/cars/$carId/maintenance/${maintenance.id}');
      },
      child: Card(
        color: context.containerBackgroundColor,
        elevation: 4,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
          side: BorderSide(
            color: context.accentColor.withOpacity(0.3),
            width: 0.5,
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: context.accentColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      iconData,
                      color: context.accentColor,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          maintenanceType,
                          style: TextStyle(
                            color: context.accentColor,
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                        Text(
                          formattedDate,
                          style: TextStyle(
                            color: context.secondaryTextColor,
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: context.accentColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      '${NumberFormat.decimalPattern().format(maintenance.mileage)} km',
                      style: TextStyle(
                        color: context.accentColor,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  IconButton(
                    icon: Icon(
                      Icons.edit,
                      color: context.accentColor,
                      size: 18,
                    ),
                    onPressed: () {
                      context.push('/cars/$carId/maintenance/${maintenance.id}/edit');
                    },
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                    visualDensity: VisualDensity.compact,
                  ),
                ],
              ),
              
              // Cost row
              if (maintenance.cost > 0) ...[
                const SizedBox(height: 12),
                Row(
                  children: [
                    Icon(
                      Icons.attach_money,
                      color: context.accentColor,
                      size: 16,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      '${l10n.cost}: ',
                      style: TextStyle(
                        color: context.secondaryTextColor,
                        fontSize: 14,
                      ),
                    ),
                    Text(
                      formattedCost,
                      style: TextStyle(
                        color: context.secondaryAccentColor,
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ],
              
              // Service Provider
              if (maintenance.serviceProvider.isNotEmpty) ...[
                const SizedBox(height: 8),
                Row(
                  children: [
                    Icon(
                      Icons.store,
                      color: context.accentColor,
                      size: 16,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      '${l10n.serviceProvider}: ',
                      style: TextStyle(
                        color: context.secondaryTextColor,
                        fontSize: 14,
                      ),
                    ),
                    Expanded(
                      child: Text(
                        maintenance.serviceProvider,
                        style: TextStyle(
                          color: context.primaryTextColor,
                          fontSize: 14,
                        ),
                        overflow: TextOverflow.visible,
                        maxLines: 2,
                      ),
                    ),
                  ],
                ),
              ],
              
              // Description
              if (maintenance.description.isNotEmpty) ...[
                const SizedBox(height: 12),
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: context.containerBackgroundColor.withOpacity(0.3),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: context.accentColor.withOpacity(0.2),
                    ),
                  ),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Icon(
                        Icons.description,
                        color: context.accentColor,
                        size: 16,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          maintenance.description,
                          style: TextStyle(
                            color: context.primaryTextColor.withOpacity(0.9),
                            fontSize: 13,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
              
              // Notes
              if (maintenance.notes.isNotEmpty) ...[
                const SizedBox(height: 8),
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: context.containerBackgroundColor.withOpacity(0.3),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: context.accentColor.withOpacity(0.2),
                    ),
                  ),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Icon(
                        Icons.note,
                        color: context.accentColor,
                        size: 16,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          maintenance.notes,
                          style: TextStyle(
                            color: context.secondaryTextColor,
                            fontSize: 12,
                            fontStyle: FontStyle.italic,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
} 