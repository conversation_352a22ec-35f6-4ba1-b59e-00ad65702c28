import 'dart:io';
import 'dart:async';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/models/maintenance_model.dart';
import '../../../auth/data/providers/auth_provider.dart';
import '../../../../core/services/connectivity_service.dart';
import '../services/maintenance_photo_service.dart';
import 'dart:developer' as dev;
import 'package:firebase_auth/firebase_auth.dart';
import '../../../../generated/app_localizations.dart';

final maintenanceRepositoryProvider = Provider<MaintenanceRepository>((ref) {
  final firestore = FirebaseFirestore.instance;
  final auth = ref.watch(authStateChangesProvider);
  final connectivityService = ref.watch(connectivityServiceProvider);
  final photoService = ref.watch(maintenancePhotoServiceProvider);
  return MaintenanceRepository(
    firestore, 
    auth.value?.uid ?? '',
    connectivityService,
    photoService,
  );
});

class MaintenanceRepository {
  final FirebaseFirestore _firestore;
  final String _userId;
  final ConnectivityService _connectivityService;
  final MaintenancePhotoService _photoService;
  S? _l10n;
  
  // Cache for maintenance records
  final Map<String, List<MaintenanceModel>> _cachedMaintenanceByCarId = {};
  final Map<String, DateTime> _lastCacheTimeByCarId = {};
  static const cacheValidityDuration = Duration(minutes: 30);

  MaintenanceRepository(this._firestore, this._userId, this._connectivityService, this._photoService);

  // Set the localization object when needed
  void setL10n(S l10n) {
    _l10n = l10n;
  }

  // Check if cache is valid for a specific car
  bool _isCacheValidForCar(String carId) => 
      _cachedMaintenanceByCarId.containsKey(carId) && 
      _lastCacheTimeByCarId.containsKey(carId) &&
      DateTime.now().difference(_lastCacheTimeByCarId[carId]!) < cacheValidityDuration;

  CollectionReference<MaintenanceModel> get _maintenanceCollection =>
      _firestore.collection('maintenance').withConverter(
            fromFirestore: MaintenanceModel.fromFirestore,
            toFirestore: (MaintenanceModel maintenance, _) => maintenance.toFirestore(),
          );

  // Stream of maintenance records for a specific car
  Stream<List<MaintenanceModel>> getMaintenanceForCar(String carId) {
    if (_userId.isEmpty) {
      return Stream.value([]);
    }
    
    final query = _maintenanceCollection
        .where('carId', isEqualTo: carId)
        .where('userId', isEqualTo: _userId)
        .orderBy('date', descending: true);
    
    return query.snapshots().map((snapshot) {
      final maintenanceRecords = snapshot.docs.map((doc) {
        final data = doc.data();
        return data.copyWith(id: doc.id);
      }).toList();
      
      // Update cache when online
      if (_connectivityService.isConnected) {
        dev.log('MaintenanceRepository: Updating maintenance cache for car $carId');
        _cachedMaintenanceByCarId[carId] = maintenanceRecords;
        _lastCacheTimeByCarId[carId] = DateTime.now();
      }
      
      dev.log('MaintenanceRepository: Returning ${maintenanceRecords.length} maintenance records from ${snapshot.metadata.isFromCache ? "cache" : "server"}');
      
      return maintenanceRecords;
    });
  }

  Future<List<MaintenanceModel>> getMaintenanceForCarFuture(String carId) async {
    if (_userId.isEmpty) {
      return [];
    }
    
    try {
      // If offline and we have a valid cache, use it
      if (!_connectivityService.isConnected && _isCacheValidForCar(carId)) {
        dev.log('MaintenanceRepository: Offline - returning cached maintenance for car $carId');
        return _cachedMaintenanceByCarId[carId]!;
      }
      
      // Otherwise, try to get from Firestore
      final snapshot = await _maintenanceCollection
          .where('carId', isEqualTo: carId)
          .where('userId', isEqualTo: _userId)
          .orderBy('date', descending: true)
          .get();
      
      final maintenanceRecords = snapshot.docs.map((doc) {
        final data = doc.data();
        return data.copyWith(id: doc.id);
      }).toList();
      
      // Update cache
      _cachedMaintenanceByCarId[carId] = maintenanceRecords;
      _lastCacheTimeByCarId[carId] = DateTime.now();
      
      return maintenanceRecords;
    } catch (e) {
      dev.log('MaintenanceRepository: Error getting maintenance for car $carId: $e');
      
      // Fall back to cache if available
      if (_cachedMaintenanceByCarId.containsKey(carId)) {
        dev.log('MaintenanceRepository: Error - falling back to cached maintenance for car $carId');
        return _cachedMaintenanceByCarId[carId]!;
      }
      
      rethrow;
    }
  }

  // Add a new maintenance record
  Future<MaintenanceModel?> addMaintenance(MaintenanceModel maintenance) async {
    // Validate user authentication with more robust checking
    if (_userId.isEmpty) {
      dev.log('MaintenanceRepository: Cannot add maintenance - user ID is empty');
      throw Exception('User authentication error. Please sign in again.');
    }
    
    // Double-check authentication state with Firebase Auth
    try {
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) {
        dev.log('MaintenanceRepository: Firebase Auth reports no current user');
        throw Exception('Authentication error. Please sign in again.');
      }
      
      // Check if the stored user ID matches the current Firebase user
      if (_userId != currentUser.uid) {
        dev.log('MaintenanceRepository: User ID mismatch - Repository: $_userId, Firebase: ${currentUser.uid}');
        throw Exception('User session mismatch. Please sign out and sign in again.');
      }
      
      // Force token refresh to ensure we have a valid token
      dev.log('MaintenanceRepository: Refreshing authentication token');
      await currentUser.getIdToken(true);
      dev.log('MaintenanceRepository: Token refresh successful');
    } catch (authError) {
      dev.log('MaintenanceRepository: Error refreshing auth token: $authError');
      if (authError is FirebaseAuthException) {
        throw Exception('Authentication error: ${authError.message}. Please sign in again.');
      } else {
        throw Exception('Authentication error. Please sign in again.');
      }
    }
    
    // Validate connectivity
    if (!_connectivityService.isConnected) {
      dev.log('MaintenanceRepository: Cannot add maintenance - no internet connection');
      throw Exception('Cannot add maintenance while offline. Please connect to the internet and try again.');
    }
    
    try {
      dev.log('MaintenanceRepository: Adding maintenance for car ${maintenance.carId} with user ID: $_userId');
      
      // Verify again that we have a valid user ID
      if (_userId != maintenance.userId) {
        dev.log('MaintenanceRepository: Mismatch between repository user ID and maintenance user ID');
        dev.log('Repository user ID: $_userId');
        dev.log('Maintenance user ID: ${maintenance.userId}');
        // Use the repository user ID to ensure consistency
        maintenance = maintenance.copyWith(userId: _userId);
      }
      
      // Skip permission test as it might be failing due to Firestore rules
      // Instead, check if we can directly write to maintenance collection
      dev.log('MaintenanceRepository: Bypassing permission test, directly trying to add maintenance record');
      
      // Now try to add the maintenance record
      final docRef = await _maintenanceCollection.add(maintenance.copyWith(
        userId: _userId,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ));
      
      dev.log('MaintenanceRepository: Successfully added maintenance record with ID: ${docRef.id}');
      
      // Update local cache if we have it
      final carId = maintenance.carId;
      if (_cachedMaintenanceByCarId.containsKey(carId)) {
        final newMaintenance = maintenance.copyWith(id: docRef.id);
        _cachedMaintenanceByCarId[carId] = [newMaintenance, ..._cachedMaintenanceByCarId[carId]!];
        _lastCacheTimeByCarId[carId] = DateTime.now();
        dev.log('MaintenanceRepository: Updated local cache for car $carId');
      }
      
      return maintenance.copyWith(id: docRef.id);
    } catch (e) {
      dev.log('MaintenanceRepository: Error adding maintenance: $e');
      
      // Provide more specific error messages based on the exception
      if (e.toString().contains('permission-denied')) {
        throw Exception('You do not have permission to add maintenance. Please sign in again.');
      } else if (e.toString().contains('unauthenticated') || e.toString().contains('UNAUTHENTICATED')) {
        throw Exception('Authentication error. Please sign in again.');
      } else if (e.toString().contains('network') || e.toString().contains('NETWORK')) {
        throw Exception(_l10n?.networkConnectionError ?? 'Network error. Please check your internet connection and try again.');
      } else if (e.toString().contains('RESOURCE_EXHAUSTED')) {
        throw Exception('Service quota exceeded. Please try again later.');
      } else if (e.toString().contains('App Check')) {
        throw Exception('Security verification failed. Please restart the app and try again.');
      } else {
        throw Exception('Failed to add maintenance record: ${e.toString()}');
      }
    }
  }

  // Update an existing maintenance record
  Future<MaintenanceModel?> updateMaintenance(MaintenanceModel maintenance) async {
    if (_userId.isEmpty) {
      throw Exception('User not authenticated');
    }
    
    if (!_connectivityService.isConnected) {
      throw Exception('Cannot update maintenance while offline. Please connect to the internet and try again.');
    }
    
    if (maintenance.id == null) {
      throw ArgumentError('Maintenance ID cannot be null');
    }
    
    try {
      await _maintenanceCollection.doc(maintenance.id).update({
        ...maintenance.toFirestore(),
        'userId': _userId,
        'updatedAt': FieldValue.serverTimestamp(),
      });
      
      // Update local cache if we have it
      final carId = maintenance.carId;
      if (_cachedMaintenanceByCarId.containsKey(carId)) {
        _cachedMaintenanceByCarId[carId] = _cachedMaintenanceByCarId[carId]!.map((m) {
          return m.id == maintenance.id ? maintenance : m;
        }).toList();
        _lastCacheTimeByCarId[carId] = DateTime.now();
      }
      
      return maintenance;
    } catch (e) {
      dev.log('MaintenanceRepository: Error updating maintenance: $e');
      return null;
    }
  }

  // Delete a maintenance record
  Future<bool> deleteMaintenance(String maintenanceId, String carId) async {
    if (_userId.isEmpty) {
      dev.log('MaintenanceRepository: Cannot delete maintenance - user ID is empty');
      throw Exception('User authentication error. Please sign in again.');
    }
    
    if (!_connectivityService.isConnected) {
      dev.log('MaintenanceRepository: Cannot delete maintenance - no internet connection');
      throw Exception('Cannot delete maintenance while offline. Please connect to the internet and try again.');
    }
    
    try {
      // First get the maintenance record to access the photo URLs
      final maintenanceSnapshot = await _maintenanceCollection.doc(maintenanceId).get();
      
      if (!maintenanceSnapshot.exists) {
        dev.log('MaintenanceRepository: Maintenance document not found, it may already be deleted');
        // Update local cache to ensure consistency
        if (_cachedMaintenanceByCarId.containsKey(carId)) {
          _cachedMaintenanceByCarId[carId] = _cachedMaintenanceByCarId[carId]!
              .where((m) => m.id != maintenanceId)
              .toList();
          _lastCacheTimeByCarId[carId] = DateTime.now();
        }
        return true; // Consider it a success if the document doesn't exist
      }
      
      // Verify user is allowed to delete this record
      final data = maintenanceSnapshot.data();
      if (data == null) {
        dev.log('MaintenanceRepository: Maintenance data is null');
        throw Exception('Maintenance record data is corrupted');
      }
      
      if (data.userId != _userId) {
        dev.log('MaintenanceRepository: User ID mismatch - Repository: $_userId, Record: ${data.userId}');
        throw Exception('You do not have permission to delete this maintenance record');
      }
      
      // Get photo URLs from the maintenance record
      final photoUrls = data.photoUrls;
      
      // Track photo deletion tasks
      List<Future<bool>> photoDeleteTasks = [];
      
      // Delete all associated photos first
      if (photoUrls.isNotEmpty) {
        dev.log('MaintenanceRepository: Deleting ${photoUrls.length} photos for maintenance $maintenanceId');
        
        // Create deletion tasks for all photos
        for (final photoUrl in photoUrls) {
          photoDeleteTasks.add(_photoService.deleteMaintenancePhoto(photoUrl).catchError((error) {
            // Log but allow the operation to continue
            dev.log('MaintenanceRepository: Error deleting photo $photoUrl: $error');
            return false; // Mark this specific deletion as failed
          }));
        }
        
        // Wait for all photo deletions to complete or timeout after 10 seconds
        await Future.wait(photoDeleteTasks)
            .timeout(const Duration(seconds: 10), onTimeout: () {
          dev.log('MaintenanceRepository: Photo deletion timed out, continuing with record deletion');
          return List<bool>.filled(photoUrls.length, false);
        });
      }
      
      // Now delete the maintenance document
      await _maintenanceCollection.doc(maintenanceId).delete();
      
      // Update local cache if we have it
      if (_cachedMaintenanceByCarId.containsKey(carId)) {
        _cachedMaintenanceByCarId[carId] = _cachedMaintenanceByCarId[carId]!
            .where((m) => m.id != maintenanceId)
            .toList();
        _lastCacheTimeByCarId[carId] = DateTime.now();
        dev.log('MaintenanceRepository: Updated local cache for car $carId after deletion');
      }
      
      return true;
    } catch (e) {
      // Handle specific errors for better user feedback
      if (e.toString().contains('permission-denied')) {
        throw Exception('You do not have permission to delete this maintenance record.');
      } else if (e.toString().contains('not-found')) {
        // If the document wasn't found, consider it a success
        dev.log('MaintenanceRepository: Document not found, considering deletion successful');
        // Update local cache to ensure consistency
        if (_cachedMaintenanceByCarId.containsKey(carId)) {
          _cachedMaintenanceByCarId[carId] = _cachedMaintenanceByCarId[carId]!
              .where((m) => m.id != maintenanceId)
              .toList();
          _lastCacheTimeByCarId[carId] = DateTime.now();
        }
        return true;
      } else if (e.toString().contains('network') || e.toString().contains('NETWORK')) {
        throw Exception(_l10n?.networkConnectionError ?? 'Network error. Please check your internet connection and try again.');
      } else if (e is SocketException) {
        throw Exception(_l10n?.networkConnectionLost ?? 'Network connection lost. Please check your internet connection and try again.');
      } else if (e is TimeoutException) {
        // If operation timed out, still consider it partially successful
        dev.log('MaintenanceRepository: Operation timed out, but we\'ll consider deletion successful');
        // Update local cache to maintain UI consistency
        if (_cachedMaintenanceByCarId.containsKey(carId)) {
          _cachedMaintenanceByCarId[carId] = _cachedMaintenanceByCarId[carId]!
              .where((m) => m.id != maintenanceId)
              .toList();
          _lastCacheTimeByCarId[carId] = DateTime.now();
        }
        return true;
      }
      
      // Rethrow with clearer error message
      dev.log('MaintenanceRepository: Error deleting maintenance: $e');
      throw Exception('Failed to delete maintenance: ${e.toString().split('Exception:').last.trim()}');
    }
  }

  // Upload a photo for a maintenance record
  Future<String?> uploadMaintenancePhoto(File photoFile, String carId, String maintenanceId) async {
    try {
      if (!_connectivityService.isConnected) {
        throw Exception('No internet connection');
      }

      return await _photoService.uploadMaintenancePhoto(
        imageFile: photoFile,
        userId: _userId,
        carId: carId,
        maintenanceId: maintenanceId,
      );
    } catch (e) {
      dev.log('Error uploading maintenance photo: $e');
      return null;
    }
  }

  // Delete a photo from a maintenance record
  Future<bool> deleteMaintenancePhoto(String photoUrl) async {
    try {
      if (!_connectivityService.isConnected) {
        throw Exception('No internet connection');
      }

      return await _photoService.deleteMaintenancePhoto(photoUrl);
    } catch (e) {
      dev.log('Error deleting maintenance photo: $e');
      return false;
    }
  }

  // Add a photo to an existing maintenance record
  Future<MaintenanceModel?> addPhotoToMaintenance(String maintenanceId, String carId, File photoFile) async {
    try {
      if (!_connectivityService.isConnected) {
        throw Exception('No internet connection');
      }

      // Get current maintenance data
      final maintenanceDoc = await _maintenanceCollection.doc(maintenanceId).get();
      if (!maintenanceDoc.exists) {
        throw Exception('Maintenance record not found');
      }
      
      final maintenance = maintenanceDoc.data()!;
      
      // Upload the photo
      final photoUrl = await uploadMaintenancePhoto(photoFile, carId, maintenanceId);
      if (photoUrl == null) {
        throw Exception('Failed to upload photo');
      }
      
      // Update the maintenance record with the new photo URL
      final updatedPhotoUrls = [...maintenance.photoUrls, photoUrl];
      final updatedMaintenance = maintenance.copyWith(
        photoUrls: updatedPhotoUrls,
        updatedAt: DateTime.now(),
      );
      
      // Save the updated maintenance record
      return await updateMaintenance(updatedMaintenance);
    } catch (e) {
      dev.log('Error adding photo to maintenance: $e');
      return null;
    }
  }

  // Remove a photo from an existing maintenance record
  Future<MaintenanceModel?> removePhotoFromMaintenance(String maintenanceId, String photoUrl) async {
    try {
      if (!_connectivityService.isConnected) {
        throw Exception('No internet connection');
      }

      // Get current maintenance data
      final maintenanceDoc = await _maintenanceCollection.doc(maintenanceId).get();
      if (!maintenanceDoc.exists) {
        throw Exception('Maintenance record not found');
      }
      
      final maintenance = maintenanceDoc.data()!;
      
      // Delete the photo from storage
      final deleted = await deleteMaintenancePhoto(photoUrl);
      if (!deleted) {
        dev.log('Warning: Failed to delete photo from storage, but will update the record anyway');
      }
      
      // Update the maintenance record to remove the photo URL
      final updatedPhotoUrls = maintenance.photoUrls.where((url) => url != photoUrl).toList();
      final updatedMaintenance = maintenance.copyWith(
        photoUrls: updatedPhotoUrls,
        updatedAt: DateTime.now(),
      );
      
      // Save the updated maintenance record
      return await updateMaintenance(updatedMaintenance);
    } catch (e) {
      dev.log('Error removing photo from maintenance: $e');
      return null;
    }
  }
} 