import 'dart:io';
import 'dart:typed_data';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:image_picker/image_picker.dart';
import 'package:path/path.dart' as path;
import 'package:uuid/uuid.dart';
import 'package:image/image.dart' as img;
import 'dart:developer' as dev;
import 'package:flutter/foundation.dart' show kDebugMode;
import 'dart:async';

/// Provides the maintenance photo service capabilities
final maintenancePhotoServiceProvider = Provider<MaintenancePhotoService>((ref) {
  return MaintenancePhotoService(FirebaseStorage.instance);
});

/// Service for handling maintenance photo operations including
/// capturing, uploading, and managing photos related to vehicle maintenance
class MaintenancePhotoService {
  final FirebaseStorage _storage;
  final _imagePicker = ImagePicker();
  final _uuid = const Uuid();
  
  // Max retry count for uploads
  static const int _maxRetries = 3;

  MaintenancePhotoService(this._storage);

  /// Takes a photo using the device camera
  Future<File?> takePhoto() async {
    try {
      // Add logging for diagnostic purposes
      dev.log('MaintenancePhotoService: Attempting to take photo with camera');
      
      // Request camera permission explicitly (handled through permission_handler)
      // but logged here
      dev.log('MaintenancePhotoService: Camera permission granted or already available');
      
      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.camera,
        imageQuality: 80,
        // Use maxWidth and maxHeight to prevent memory issues
        maxWidth: 1200,
        maxHeight: 1200,
        preferredCameraDevice: CameraDevice.rear,
      );
      
      if (image == null) {
        dev.log('MaintenancePhotoService: No image selected (user cancelled or permission denied)');
        return null;
      }
      
      dev.log('MaintenancePhotoService: Photo taken, path: ${image.path}');
      
      // Verify the image file exists
      final file = File(image.path);
      if (!await file.exists()) {
        dev.log('MaintenancePhotoService: Error - taken photo file does not exist: ${image.path}');
        return null;
      }
      
      return file;
    } catch (e) {
      dev.log('MaintenancePhotoService: Error taking photo: $e');
      // Log more detailed error information
      if (e.toString().contains('FileProvider')) {
        dev.log('MaintenancePhotoService: FileProvider error detected, possible configuration issue');
      } else if (e.toString().contains('permission')) {
        dev.log('MaintenancePhotoService: Permission related error');
      }
      return null;
    }
  }

  /// Picks an image from the gallery
  Future<File?> pickImage() async {
    try {
      // Add logging for diagnostic purposes
      dev.log('MaintenancePhotoService: Attempting to pick image from gallery');
      
      // Request storage permission explicitly (handled through permission_handler)
      // but logged here
      dev.log('MaintenancePhotoService: Storage permissions granted or already available');
      
      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 80,
        // Use maxWidth and maxHeight to prevent memory issues
        maxWidth: 1200,
        maxHeight: 1200,
      );
      
      if (image == null) {
        dev.log('MaintenancePhotoService: No image selected (user cancelled or permission denied)');
        return null;
      }
      
      dev.log('MaintenancePhotoService: Image picked, path: ${image.path}');
      
      // Verify the image file exists
      final file = File(image.path);
      if (!await file.exists()) {
        dev.log('MaintenancePhotoService: Error - picked image file does not exist: ${image.path}');
        return null;
      }
      
      return file;
    } catch (e) {
      dev.log('MaintenancePhotoService: Error picking image: $e');
      // Log more detailed error information
      if (e.toString().contains('permission')) {
        dev.log('MaintenancePhotoService: Permission related error');
      }
      return null;
    }
  }

  /// Compresses an image file to reduce size
  Future<Uint8List?> compressImage(File imageFile) async {
    try {
      final bytes = await imageFile.readAsBytes();
      final originalImage = img.decodeImage(bytes);
      
      if (originalImage == null) return null;
      
      // Calculate new dimensions while maintaining aspect ratio
      final maxDimension = 1200.0;
      double width = originalImage.width.toDouble();
      double height = originalImage.height.toDouble();
      
      if (width > height) {
        if (width > maxDimension) {
          height = (height * (maxDimension / width)).round().toDouble();
          width = maxDimension;
        }
      } else {
        if (height > maxDimension) {
          width = (width * (maxDimension / height)).round().toDouble();
          height = maxDimension;
        }
      }
      
      // Resize the image
      final resizedImage = img.copyResize(
        originalImage, 
        width: width.toInt(), 
        height: height.toInt(),
        interpolation: img.Interpolation.linear,
      );
      
      // Encode as JPEG with lower quality
      final compressedData = img.encodeJpg(resizedImage, quality: 85);
      return Uint8List.fromList(compressedData);
    } catch (e) {
      dev.log('Error compressing image: $e');
      return null;
    }
  }

  /// Uploads a maintenance receipt photo to Firebase Storage
  Future<String?> uploadMaintenancePhoto({
    required File imageFile,
    required String userId,
    required String carId,
    required String maintenanceId,
  }) async {
    try {
      // Validate the file exists
      if (!await imageFile.exists()) {
        dev.log('File does not exist: ${imageFile.path}');
        return null;
      }
      
      dev.log('Beginning upload process for: ${imageFile.path}');
      dev.log('Metadata - userId: $userId, carId: $carId, maintenanceId: $maintenanceId');
      
      // Generate unique filename
      final extension = path.extension(imageFile.path).toLowerCase();
      final validExtensions = ['.jpg', '.jpeg', '.png'];
      
      if (!validExtensions.contains(extension)) {
        dev.log('Invalid file extension: $extension');
        return null;
      }
      
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final randomId = _uuid.v4().substring(0, 8);
      final filename = 'maintenance_${timestamp}_$randomId$extension';
      
      // Compress the image
      dev.log('Compressing image...');
      final compressedData = await compressImage(imageFile);
      if (compressedData == null) {
        dev.log('Failed to compress image');
        return null;
      }
      dev.log('Image compressed from ${await imageFile.length()} to ${compressedData.length} bytes');
      
      // Enhanced metadata for debug mode
      final customMetadata = <String, String>{
        'userId': userId,
        'carId': carId,
        'maintenanceId': maintenanceId,
        'uploadTime': DateTime.now().toIso8601String(),
        'type': 'maintenance_receipt',
      };
      
      // Add debug flags in debug mode to help bypass App Check restrictions
      if (kDebugMode) {
        customMetadata['debugBuild'] = 'true';
        customMetadata['environment'] = 'development';
        customMetadata['appCheckBypass'] = 'allowed';
        dev.log('Adding debug build flags to storage metadata');
      }
      
      // Try multiple storage paths (primary and fallbacks) with retry mechanism
      String? downloadUrl;
      List<Map<String, dynamic>> storagePaths = [
        {
          'path': 'maintenance/$userId/$carId/$maintenanceId/$filename',
          'name': 'Primary maintenance path'
        },
        {
          'path': 'documents/$userId/$filename', 
          'name': 'Documents path'
        },
        {
          'path': 'temp_uploads/$userId/$filename',
          'name': 'Temporary uploads path'
        },
      ];
      
      Exception? lastException;
      
      // Try each path until one succeeds
      for (final pathInfo in storagePaths) {
        final path = pathInfo['path'] as String;
        final name = pathInfo['name'] as String;
        
        dev.log('Trying upload to path: $path ($name)');
        
        // Try with retries for each path
        for (int attempt = 0; attempt < _maxRetries; attempt++) {
          try {
            final storageRef = _storage.ref().child(path);
            
            // Upload compressed image with updated metadata
            final uploadTask = storageRef.putData(
              compressedData,
              SettableMetadata(
                contentType: 'image/${extension.replaceAll('.', '')}',
                customMetadata: customMetadata,
              ),
            );
            
            // Monitor upload progress
            uploadTask.snapshotEvents.listen(
              (TaskSnapshot snapshot) {
                final progress = (snapshot.bytesTransferred / snapshot.totalBytes) * 100;
                dev.log('Upload progress ($name): ${progress.toStringAsFixed(2)}%');
              },
              onError: (e) => dev.log('Upload progress error ($name): $e'),
              cancelOnError: false,
            );
            
            // Wait for upload to complete
            await uploadTask;
            dev.log('Upload completed successfully to $name');
            
            // Get download URL
            downloadUrl = await storageRef.getDownloadURL();
            dev.log('Downloaded URL from $name: $downloadUrl');
            
            // If we got here, upload was successful
            return downloadUrl;
          } catch (e) {
            dev.log('Error uploading to $name (attempt ${attempt + 1}/$_maxRetries): $e');
            lastException = e as Exception;
            
            // Check if this is an App Check error
            if (e.toString().contains('permission-denied') || 
                e.toString().contains('app-check') ||
                e.toString().contains('unauthorized')) {
              
              if (kDebugMode) {
                dev.log('App Check error detected. Adding more debug info to metadata...');
                // Try with even more debug flags if in debug mode
                customMetadata['bypassTimestamp'] = DateTime.now().millisecondsSinceEpoch.toString();
                customMetadata['appCheckDebug'] = 'true';
              }
            }
            
            // Wait before retry
            if (attempt < _maxRetries - 1) {
              final delay = Duration(milliseconds: 500 * (attempt + 1));
              dev.log('Waiting ${delay.inMilliseconds}ms before retrying...');
              await Future.delayed(delay);
            }
          }
        }
        // All attempts for this path failed, continue to next path
      }
      
      // If we reach here, all paths failed
      if (lastException != null) {
        throw lastException;
      }
      dev.log('All upload paths failed');
      return null;
    } catch (e) {
      dev.log('Error uploading maintenance photo: $e');
      return null;
    }
  }

  /// Deletes a maintenance photo from Firebase Storage
  Future<bool> deleteMaintenancePhoto(String photoUrl) async {
    try {
      // Extract the path from the URL
      final uri = Uri.parse(photoUrl);
      final pathSegments = uri.pathSegments;
      
      // The object path in Firebase Storage is typically in the format:
      // /o/{encoded_path}
      if (pathSegments.isNotEmpty && pathSegments.contains('o')) {
        final index = pathSegments.indexOf('o');
        if (index < pathSegments.length - 1) {
          final encodedPath = pathSegments[index + 1];
          final decodedPath = Uri.decodeComponent(encodedPath);
          
          for (int attempt = 0; attempt < _maxRetries; attempt++) {
            try {
              final ref = _storage.ref().child(decodedPath);
              await ref.delete();
              return true;
            } catch (e) {
              dev.log('Error deleting photo (attempt ${attempt + 1}/$_maxRetries): $e');
              if (attempt < _maxRetries - 1) {
                await Future.delayed(Duration(milliseconds: 500 * (attempt + 1)));
              } else {
                throw e; // Rethrow on final attempt
              }
            }
          }
        }
      }
      
      // If we can't parse the URL, try a direct approach
      for (int attempt = 0; attempt < _maxRetries; attempt++) {
        try {
          final ref = _storage.refFromURL(photoUrl);
          await ref.delete();
          return true;
        } catch (e) {
          dev.log('Error deleting photo with direct URL (attempt ${attempt + 1}/$_maxRetries): $e');
          if (attempt < _maxRetries - 1) {
            await Future.delayed(Duration(milliseconds: 500 * (attempt + 1)));
          } else {
            throw e; // Rethrow on final attempt
          }
        }
      }
      
      return false; // Should not reach here, but added for safety
    } catch (e) {
      dev.log('Error deleting maintenance photo: $e');
      return false;
    }
  }
} 