import 'package:flutter/material.dart';
import '../../../../core/theme/theme_extensions.dart';
import '../../models/onboarding_page_model.dart';

/// A single page in the onboarding flow
class OnboardingPage extends StatelessWidget {
  /// The page data to display
  final OnboardingPageModel page;

  /// Constructor
  const OnboardingPage({super.key, required this.page});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            context.containerBackgroundColor,
            context.secondaryAccentColor.withOpacity(0.5),
          ],
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const SizedBox(height: 60),
          // Feature Icon with circular background
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              color: context.containerBackgroundColor,
              shape: BoxShape.circle,
              border: Border.all(color: context.accentColor, width: 2),
              boxShadow: [
                BoxShadow(
                  color: context.accentColor.withOpacity(0.3),
                  blurRadius: 10,
                  spreadRadius: 2,
                ),
              ],
            ),
            child: Icon(
              page.icon,
              size: 60,
              color: context.accentColor,
            ),
          ),
          const SizedBox(height: 40),
          // Title
          Text(
            page.title,
            style: TextStyle(
              color: context.accentColor,
              fontSize: 28,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 20),
          // Description
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Text(
              page.description,
              style: TextStyle(
                color: context.secondaryTextColor,
                fontSize: 16,
                height: 1.5,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          const Spacer(),
        ],
      ),
    );
  }
} 