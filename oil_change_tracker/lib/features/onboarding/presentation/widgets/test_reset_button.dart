import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/providers/onboarding_provider.dart';
import '../../../../core/theme/theme_extensions.dart';
import '../../../../generated/app_localizations.dart';

/// A button to reset the onboarding state for testing
/// This should only be used during development
class TestResetOnboardingButton extends ConsumerWidget {
  const TestResetOnboardingButton({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final l10n = S.of(context);
    
    return IconButton(
      onPressed: () {
        ref.read(onboardingCompletedProvider.notifier).resetOnboarding();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(l10n.onboardingReset),
            backgroundColor: context.secondaryAccentColor,
          ),
        );
      },
      tooltip: l10n.resetOnboardingTooltip,
      icon: Icon(
        Icons.restart_alt,
        color: context.secondaryTextColor,
      ),
    );
  }
} 