import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/providers/onboarding_provider.dart';
import '../../../../core/theme/theme_extensions.dart';
import '../../../../generated/app_localizations.dart';
import '../widgets/permissions_page.dart';
import 'dart:developer' as dev;
import '../../../../services/notification_service.dart';
import '../../../../features/ads/presentation/managers/app_open_ad_manager.dart';

class OnboardingScreen extends ConsumerStatefulWidget {
  const OnboardingScreen({super.key});

  @override
  ConsumerState<OnboardingScreen> createState() => _OnboardingScreenState();
}

class _OnboardingScreenState extends ConsumerState<OnboardingScreen>
    with SingleTickerProviderStateMixin {
  final PageController _pageController = PageController();
  int _currentPage = 0;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  // Change to non-late field with null initialization
  List<Widget>? _pages;

  @override
  void initState() {
    super.initState();
    // Setup animations
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 600),
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
        CurvedAnimation(parent: _animationController, curve: Curves.easeOut));

    _animationController.forward();

    // Initialize _pages as an empty list to avoid null issues
    _pages = [];
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    final l10n = S.of(context);

    // Create the simplified 3-page onboarding flow
    final introPages = [
      // Screen 1: Problem & Solution Introduction
      _buildProblemSolutionPage(
        context,
        l10n: l10n,
        gradient: [Color(0xFF8E793E), context.accentColor],
      ),

      // Screen 2: Key Features Overview
      _buildFeaturesOverviewPage(
        context,
        l10n: l10n,
        gradient: [Color(0xFF3E4C5C), Color(0xFF576A84)],
      ),

      // Screen 3: Get Started Call-to-Action
      _buildGetStartedPage(
        context,
        l10n: l10n,
        gradient: [context.accentColor, Color(0xFFD8A25E)],
      ),
    ];

    // Always set pages to ensure localization updates
    setState(() {
      _pages = introPages;
    });
  }

  @override
  void dispose() {
    _pageController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  void _onPageChanged(int page) {
    setState(() {
      _currentPage = page;
    });
    // Add page change animation
    _animationController.reset();
    _animationController.forward();
  }

  // This method is called when the user completes the onboarding
  void _completeOnboarding() async {
    if (_currentPage == _pages!.length - 1) {
      // If we're on the permissions page (last page), request notification permissions
      await _requestNotificationPermission();
    }

    // Explicitly set the ad flag to false to prevent ads from showing after onboarding
    try {
      // Disable app open ads until the user logs in
      final appOpenAdManager = ref.read(appOpenAdManagerProvider.notifier);
      appOpenAdManager.showAdsAfterLogin = false;
      dev.log('OnboardingScreen: Set showAdsAfterLogin to false');
    } catch (e) {
      dev.log('Error setting ad flag: $e');
    }

    // Mark onboarding as completed and navigate to the main screen
    ref.read(onboardingCompletedProvider.notifier).completeOnboarding();
    context.go('/');
  }

  // Add this method to request notification permissions
  Future<void> _requestNotificationPermission() async {
    try {
      // Use the notification service instead of directly using permission_handler
      final notificationService = ref.read(notificationServiceProvider);
      final granted =
          await notificationService.requestNotificationPermissions();

      if (mounted && granted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(S.of(context).notificationsPermissionDesc),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      dev.log('Error requesting notification permission: $e');
    }
  }

  Widget _buildTrialAnnouncementPage(
    BuildContext context, {
    required S l10n,
    required List<Color> gradient,
  }) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            context.containerBackgroundColor,
            context.containerBackgroundColor.withOpacity(0.9),
          ],
        ),
      ),
      child: Stack(
        children: [
          // Background design elements
          Positioned(
            top: -50,
            right: -50,
            child: Container(
              width: 200,
              height: 200,
              decoration: BoxDecoration(
                gradient: RadialGradient(
                  colors: [
                    gradient[0].withOpacity(0.3),
                    gradient[1].withOpacity(0.0)
                  ],
                  radius: 0.8,
                ),
                shape: BoxShape.circle,
              ),
            ),
          ),
          Positioned(
            bottom: 100,
            left: -70,
            child: Container(
              width: 180,
              height: 180,
              decoration: BoxDecoration(
                gradient: RadialGradient(
                  colors: [
                    gradient[1].withOpacity(0.2),
                    gradient[0].withOpacity(0.0)
                  ],
                  radius: 0.7,
                ),
                shape: BoxShape.circle,
              ),
            ),
          ),

          // Content
          Padding(
            padding: const EdgeInsets.fromLTRB(24, 60, 24, 24),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                // App logo at the top
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: context.containerBackgroundColor.withOpacity(0.7),
                    shape: BoxShape.circle,
                    border: Border.all(color: context.accentColor, width: 2),
                    boxShadow: [
                      BoxShadow(
                        color: context.accentColor.withOpacity(0.3),
                        blurRadius: 12,
                        spreadRadius: 1,
                      )
                    ],
                  ),
                  child: Image.asset(
                    'assets/images/app_icon.png',
                    width: 60,
                    height: 60,
                  ),
                ),
                const SizedBox(height: 40),

                // Premium crown icon
                FadeTransition(
                  opacity: _fadeAnimation,
                  child: Container(
                    padding: const EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: gradient,
                      ),
                      borderRadius: BorderRadius.circular(30),
                      boxShadow: [
                        BoxShadow(
                          color: gradient[0].withOpacity(0.4),
                          blurRadius: 15,
                          offset: const Offset(0, 8),
                        )
                      ],
                    ),
                    child: const Icon(
                      Icons.workspace_premium,
                      size: 50,
                      color: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(height: 32),

                // Title
                FadeTransition(
                  opacity: _fadeAnimation,
                  child: Text(
                    'Welcome to Premium!',
                    style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color:
                              Theme.of(context).textTheme.headlineMedium?.color,
                        ),
                    textAlign: TextAlign.center,
                  ),
                ),
                const SizedBox(height: 16),

                // Description
                FadeTransition(
                  opacity: _fadeAnimation,
                  child: Text(
                    'You\'ve been granted a 7-day free trial with full premium features! Enjoy unlimited AI voice commands and chat assistance.',
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          color: Theme.of(context)
                              .textTheme
                              .bodyLarge
                              ?.color
                              ?.withOpacity(0.8),
                          height: 1.5,
                        ),
                    textAlign: TextAlign.center,
                  ),
                ),
                const SizedBox(height: 32),

                // Premium features list
                FadeTransition(
                  opacity: _fadeAnimation,
                  child: Container(
                    padding: const EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      color: context.containerBackgroundColor.withOpacity(0.7),
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(
                        color: context.accentColor.withOpacity(0.3),
                        width: 1,
                      ),
                    ),
                    child: Column(
                      children: [
                        _buildFeatureItem(
                          context,
                          Icons.mic,
                          '75 Voice Commands',
                          'Ask me anything about your car',
                        ),
                        const SizedBox(height: 12),
                        _buildFeatureItem(
                          context,
                          Icons.chat_bubble_outline,
                          '150 AI Chat Messages',
                          'Get maintenance advice & tips',
                        ),
                        const SizedBox(height: 12),
                        _buildFeatureItem(
                          context,
                          Icons.notifications_off,
                          'Ad-Free Experience',
                          'Enjoy uninterrupted tracking',
                        ),
                      ],
                    ),
                  ),
                ),
                const Spacer(),

                // Trial duration highlight
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        context.accentColor.withOpacity(0.2),
                        Color(0xFFD8A25E).withOpacity(0.2),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(25),
                    border: Border.all(
                      color: context.accentColor.withOpacity(0.5),
                      width: 1,
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.timer,
                        color: context.accentColor,
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        '7 Days Free Trial',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              fontWeight: FontWeight.w600,
                              color: context.accentColor,
                            ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 24),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFeatureItem(
    BuildContext context,
    IconData icon,
    String title,
    String description,
  ) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: context.accentColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            color: context.accentColor,
            size: 20,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: Theme.of(context).textTheme.bodyMedium?.color,
                    ),
              ),
              Text(
                description,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context)
                          .textTheme
                          .bodySmall
                          ?.color
                          ?.withOpacity(0.7),
                    ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  // Screen 1: Problem & Solution Introduction
  Widget _buildProblemSolutionPage(
    BuildContext context, {
    required S l10n,
    required List<Color> gradient,
  }) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: gradient,
        ),
      ),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            children: [
              const Spacer(flex: 1),

              // Hero Icon with Animation
              TweenAnimationBuilder<double>(
                duration: const Duration(milliseconds: 800),
                tween: Tween(begin: 0.0, end: 1.0),
                builder: (context, value, child) {
                  return Transform.scale(
                    scale: value,
                    child: Container(
                      width: 120,
                      height: 120,
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.2),
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.1),
                            blurRadius: 20,
                            offset: const Offset(0, 10),
                          ),
                        ],
                      ),
                      child: Icon(
                        Icons.shield_outlined,
                        size: 60,
                        color: Colors.white,
                      ),
                    ),
                  );
                },
              ),

              const SizedBox(height: 40),

              // Title
              Text(
                l10n.onboardingTitle1,
                style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  height: 1.2,
                ),
                textAlign: TextAlign.center,
              ),

              const SizedBox(height: 24),

              // Problem Statement
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: Colors.white.withOpacity(0.2),
                    width: 1,
                  ),
                ),
                child: Column(
                  children: [
                    Icon(
                      Icons.warning_amber_outlined,
                      color: Colors.orange[300],
                      size: 32,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      l10n.onboardingProblem,
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                        color: Colors.white.withOpacity(0.9),
                        fontWeight: FontWeight.w500,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 16),

              // Solution Statement
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.15),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: Colors.white.withOpacity(0.3),
                    width: 1,
                  ),
                ),
                child: Column(
                  children: [
                    Icon(
                      Icons.check_circle_outline,
                      color: Colors.green[300],
                      size: 32,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      l10n.onboardingSolution,
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),

              const Spacer(flex: 2),
            ],
          ),
        ),
      ),
    );
  }

  // Screen 2: Key Features Overview
  Widget _buildFeaturesOverviewPage(
    BuildContext context, {
    required S l10n,
    required List<Color> gradient,
  }) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: gradient,
        ),
      ),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            children: [
              const Spacer(flex: 1),

              // Title
              Text(
                l10n.onboardingTitle2,
                style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  height: 1.2,
                ),
                textAlign: TextAlign.center,
              ),

              const SizedBox(height: 40),

              // Feature 1: Track Multiple Vehicles
              _buildFeatureCard(
                context,
                icon: Icons.directions_car,
                title: l10n.onboardingFeature1,
                description: l10n.onboardingFeature1Desc,
              ),

              const SizedBox(height: 16),

              // Feature 2: Smart Reminders
              _buildFeatureCard(
                context,
                icon: Icons.notifications_active,
                title: l10n.onboardingFeature2,
                description: l10n.onboardingFeature2Desc,
              ),

              const SizedBox(height: 16),

              // Feature 3: Complete Records
              _buildFeatureCard(
                context,
                icon: Icons.folder_open,
                title: l10n.onboardingFeature3,
                description: l10n.onboardingFeature3Desc,
              ),

              const Spacer(flex: 2),
            ],
          ),
        ),
      ),
    );
  }

  // Helper method for feature cards
  Widget _buildFeatureCard(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String description,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.15),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.white.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              shape: BoxShape.circle,
            ),
            child: Icon(
              icon,
              color: Colors.white,
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  description,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.white.withOpacity(0.9),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Screen 3: Get Started Call-to-Action
  Widget _buildGetStartedPage(
    BuildContext context, {
    required S l10n,
    required List<Color> gradient,
  }) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: gradient,
        ),
      ),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            children: [
              const Spacer(flex: 1),

              // Success Icon
              Container(
                width: 120,
                height: 120,
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 20,
                      offset: const Offset(0, 10),
                    ),
                  ],
                ),
                child: Icon(
                  Icons.check_circle_outline,
                  size: 60,
                  color: Colors.white,
                ),
              ),

              const SizedBox(height: 40),

              // Title
              Text(
                l10n.onboardingTitle3,
                style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  height: 1.2,
                ),
                textAlign: TextAlign.center,
              ),

              const SizedBox(height: 32),

              // Benefits Grid
              Row(
                children: [
                  Expanded(
                    child: _buildBenefitCard(
                      context,
                      icon: Icons.play_arrow,
                      title: l10n.onboardingFreeStart,
                      description: l10n.onboardingFreeStartDesc,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildBenefitCard(
                      context,
                      icon: Icons.star,
                      title: l10n.onboardingPremiumBenefits,
                      description: l10n.onboardingPremiumBenefitsDesc,
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 16),

              // Trial Offer
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: Colors.white.withOpacity(0.4),
                    width: 1,
                  ),
                ),
                child: Column(
                  children: [
                    Icon(
                      Icons.timer,
                      color: Colors.white,
                      size: 32,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      l10n.onboardingTrialOffer,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      l10n.onboardingTrialOfferDesc,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Colors.white.withOpacity(0.9),
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),

              const Spacer(flex: 2),
            ],
          ),
        ),
      ),
    );
  }

  // Helper method for benefit cards
  Widget _buildBenefitCard(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String description,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.15),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.white.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            color: Colors.white,
            size: 24,
          ),
          const SizedBox(height: 8),
          Text(
            title,
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            description,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Colors.white.withOpacity(0.9),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildEnhancedPage(
    BuildContext context, {
    required String title,
    required String description,
    required IconData icon,
    required List<Color> gradient,
    String? imagePath,
  }) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            context.containerBackgroundColor,
            context.containerBackgroundColor.withOpacity(0.9),
          ],
        ),
      ),
      child: Stack(
        children: [
          // Background design elements
          Positioned(
            top: -50,
            right: -50,
            child: Container(
              width: 200,
              height: 200,
              decoration: BoxDecoration(
                gradient: RadialGradient(
                  colors: [
                    gradient[0].withOpacity(0.3),
                    gradient[1].withOpacity(0.0)
                  ],
                  radius: 0.8,
                ),
                shape: BoxShape.circle,
              ),
            ),
          ),
          Positioned(
            bottom: 100,
            left: -70,
            child: Container(
              width: 180,
              height: 180,
              decoration: BoxDecoration(
                gradient: RadialGradient(
                  colors: [
                    gradient[1].withOpacity(0.2),
                    gradient[0].withOpacity(0.0)
                  ],
                  radius: 0.7,
                ),
                shape: BoxShape.circle,
              ),
            ),
          ),

          // Content
          Padding(
            padding: const EdgeInsets.fromLTRB(24, 60, 24, 24),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // App logo at the top
                Center(
                  child: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: context.containerBackgroundColor.withOpacity(0.7),
                      shape: BoxShape.circle,
                      border: Border.all(color: context.accentColor, width: 2),
                      boxShadow: [
                        BoxShadow(
                          color: context.accentColor.withOpacity(0.3),
                          blurRadius: 12,
                          spreadRadius: 1,
                        )
                      ],
                    ),
                    child: Image.asset(
                      'assets/images/app_icon.png',
                      width: 60,
                      height: 60,
                    ),
                  ),
                ),
                const SizedBox(height: 40),

                // Icon with fancy background
                FadeTransition(
                  opacity: _fadeAnimation,
                  child: Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: gradient,
                      ),
                      borderRadius: BorderRadius.circular(24),
                      boxShadow: [
                        BoxShadow(
                          color: gradient[0].withOpacity(0.4),
                          blurRadius: 12,
                          offset: const Offset(0, 6),
                        )
                      ],
                    ),
                    child: Icon(
                      icon,
                      color: Colors.white,
                      size: 56,
                    ),
                  ),
                ),
                const SizedBox(height: 40),

                // Title with animation
                FadeTransition(
                  opacity: _fadeAnimation,
                  child: SlideTransition(
                    position: Tween<Offset>(
                      begin: const Offset(0.2, 0),
                      end: Offset.zero,
                    ).animate(_animationController),
                    child: Text(
                      title,
                      style: TextStyle(
                        fontSize: 28,
                        fontWeight: FontWeight.bold,
                        color: context.accentColor,
                        letterSpacing: 0.5,
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 16),

                // Description with animation
                FadeTransition(
                  opacity: _fadeAnimation,
                  child: SlideTransition(
                    position: Tween<Offset>(
                      begin: const Offset(0.3, 0),
                      end: Offset.zero,
                    ).animate(CurvedAnimation(
                      parent: _animationController,
                      curve: const Interval(0.2, 1.0, curve: Curves.easeOut),
                    )),
                    child: Text(
                      description,
                      style: TextStyle(
                        fontSize: 16,
                        color: context.secondaryTextColor,
                        height: 1.5,
                      ),
                    ),
                  ),
                ),

                // Image if provided
                if (imagePath != null) ...[
                  const Spacer(),
                  FadeTransition(
                    opacity: _fadeAnimation,
                    child: Center(
                      child: Image.asset(
                        imagePath,
                        height: 180,
                        fit: BoxFit.contain,
                        errorBuilder: (context, error, stackTrace) =>
                            const SizedBox.shrink(),
                      ),
                    ),
                  ),
                ] else ...[
                  const Spacer(),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  // New method to build a styled permissions page matching other onboarding pages
  Widget _buildPermissionsPage(
    BuildContext context, {
    required S l10n,
    required List<Color> gradient,
  }) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            context.containerBackgroundColor,
            context.containerBackgroundColor.withOpacity(0.9),
          ],
        ),
      ),
      child: Stack(
        children: [
          // Background design elements
          Positioned(
            top: -50,
            right: -50,
            child: Container(
              width: 200,
              height: 200,
              decoration: BoxDecoration(
                gradient: RadialGradient(
                  colors: [
                    gradient[0].withOpacity(0.3),
                    gradient[1].withOpacity(0.0)
                  ],
                  radius: 0.8,
                ),
                shape: BoxShape.circle,
              ),
            ),
          ),
          Positioned(
            bottom: 100,
            left: -70,
            child: Container(
              width: 180,
              height: 180,
              decoration: BoxDecoration(
                gradient: RadialGradient(
                  colors: [
                    gradient[1].withOpacity(0.2),
                    gradient[0].withOpacity(0.0)
                  ],
                  radius: 0.7,
                ),
                shape: BoxShape.circle,
              ),
            ),
          ),

          // Content
          Padding(
            padding: const EdgeInsets.fromLTRB(24, 60, 24, 24),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // App logo at the top
                Center(
                  child: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: context.containerBackgroundColor.withOpacity(0.7),
                      shape: BoxShape.circle,
                      border: Border.all(color: context.accentColor, width: 2),
                      boxShadow: [
                        BoxShadow(
                          color: context.accentColor.withOpacity(0.3),
                          blurRadius: 12,
                          spreadRadius: 1,
                        )
                      ],
                    ),
                    child: Image.asset(
                      'assets/images/app_icon.png',
                      width: 60,
                      height: 60,
                    ),
                  ),
                ),

                const SizedBox(height: 16),

                // Wrap in a container with constraints to prevent overflow
                Expanded(
                  child: PermissionsPage(
                    notificationDesc: l10n.notificationsPermissionDesc,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final l10n = S.of(context);

    // Safety check - return a loading indicator if pages are still empty
    if (_pages!.isEmpty) {
      return Scaffold(
        backgroundColor: context.containerBackgroundColor,
        body: Center(
          child: CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(context.accentColor),
          ),
        ),
      );
    }

    return Scaffold(
      backgroundColor: context.containerBackgroundColor,
      body: Stack(
        children: [
          // Background pattern
          Positioned.fill(
            child: ShaderMask(
              shaderCallback: (Rect bounds) {
                return LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.transparent,
                    context.containerBackgroundColor
                  ],
                  stops: const [0.8, 1.0],
                ).createShader(bounds);
              },
              blendMode: BlendMode.dstIn,
              child: Container(
                decoration: BoxDecoration(
                  color: context.containerBackgroundColor,
                  backgroundBlendMode: BlendMode.overlay,
                ),
                child: CustomPaint(
                  painter: BackgroundPatternPainter(context: context),
                ),
              ),
            ),
          ),

          // Content
          SafeArea(
            child: Stack(
              children: [
                // Skip button with improved styling (only show on non-permission pages)
                if (_currentPage < _pages!.length - 1)
                  Positioned(
                    top: 16,
                    right: 16,
                    child: Container(
                      decoration: BoxDecoration(
                        color:
                            context.containerBackgroundColor.withOpacity(0.7),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: context.accentColor.withOpacity(0.3),
                        ),
                      ),
                      child: TextButton(
                        onPressed: _completeOnboarding,
                        child: Text(
                          l10n.skip,
                          style: TextStyle(
                            color: context.accentColor,
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                  ),

                // Page view
                PageView.builder(
                  controller: _pageController,
                  onPageChanged: _onPageChanged,
                  itemCount: _pages!.length,
                  itemBuilder: (context, index) => _pages![index],
                ),

                // Page indicator and next/done button
                Positioned(
                  bottom: 50,
                  left: 0,
                  right: 0,
                  child: Column(
                    children: [
                      // Page indicator with enhanced styling
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: List.generate(
                          _pages!.length,
                          (index) => AnimatedContainer(
                            duration: const Duration(milliseconds: 300),
                            margin: const EdgeInsets.symmetric(horizontal: 5),
                            height: 10,
                            width: _currentPage == index ? 30 : 10,
                            decoration: BoxDecoration(
                              gradient: _currentPage == index
                                  ? LinearGradient(
                                      colors: [
                                        context.accentColor,
                                        context.accentColor.withOpacity(0.7)
                                      ],
                                    )
                                  : null,
                              color: _currentPage != index
                                  ? context.accentColor.withOpacity(0.3)
                                  : null,
                              borderRadius: BorderRadius.circular(5),
                              boxShadow: _currentPage == index
                                  ? [
                                      BoxShadow(
                                        color: context.accentColor
                                            .withOpacity(0.5),
                                        blurRadius: 6,
                                        spreadRadius: 1,
                                      )
                                    ]
                                  : null,
                            ),
                          ),
                        ),
                      ),

                      const SizedBox(height: 40),

                      // Next/Done button with enhanced styling
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 24),
                        child: ElevatedButton(
                          onPressed: () {
                            if (_currentPage == _pages!.length - 1) {
                              _completeOnboarding();
                            } else {
                              _pageController.nextPage(
                                duration: const Duration(milliseconds: 500),
                                curve: Curves.easeInOut,
                              );
                            }
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: context.secondaryAccentColor,
                            foregroundColor: Colors.white,
                            minimumSize: const Size(double.infinity, 60),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(16),
                            ),
                            elevation: 8,
                            shadowColor:
                                context.secondaryAccentColor.withOpacity(0.5),
                            padding: const EdgeInsets.symmetric(vertical: 14),
                          ),
                          child: Text(
                            _currentPage == _pages!.length - 1
                                ? l10n.getStarted
                                : l10n.next,
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              letterSpacing: 0.5,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

// Custom painter for background pattern
class BackgroundPatternPainter extends CustomPainter {
  final BuildContext context;

  BackgroundPatternPainter({required this.context});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = context.accentColor.withOpacity(0.03)
      ..style = PaintingStyle.fill;

    final linePaint = Paint()
      ..color = context.accentColor.withOpacity(0.05)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.0;

    // Draw subtle background patterns
    for (int i = 0; i < 10; i++) {
      canvas.drawCircle(
        Offset(size.width * (i / 10), size.height * 0.2),
        size.width * 0.1,
        paint,
      );

      canvas.drawLine(
        Offset(0, size.height * (i / 10)),
        Offset(size.width, size.height * (i / 10)),
        linePaint,
      );
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}
