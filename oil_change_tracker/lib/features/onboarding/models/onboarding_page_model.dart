import 'package:flutter/material.dart';

/// Model representing a single onboarding page
class OnboardingPageModel {
  /// Title of the onboarding page
  final String title;
  
  /// Description of the feature being explained
  final String description;
  
  /// Icon to display for the feature
  final IconData icon;
  
  /// Background image path (optional)
  final String? imagePath;
  
  /// Constructor
  OnboardingPageModel({
    required this.title,
    required this.description,
    required this.icon,
    this.imagePath,
  });
} 