import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/models/car_model.dart';
import '../../../car_management/providers/car_provider.dart';
import 'package:intl/intl.dart';
import '../../../../generated/app_localizations.dart';
import '../../../../core/theme/theme_extensions.dart';

class DashboardStatusSlider extends ConsumerStatefulWidget {
  const DashboardStatusSlider({super.key});

  @override
  ConsumerState<DashboardStatusSlider> createState() => _DashboardStatusSliderState();
}

class _DashboardStatusSliderState extends ConsumerState<DashboardStatusSlider> {
  final PageController _pageController = PageController();
  int _currentPage = 0;

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final carsAsync = ref.watch(carsProvider);
    final l10n = S.of(context);

    return Card(
      color: context.containerBackgroundColor,
      elevation: 4,
      surfaceTintColor: Colors.transparent,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(color: context.accentColor, width: 1),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.analytics, color: context.accentColor, size: 24),
                const SizedBox(width: 8),
                Text(
                  l10n.vehicleStatistics,
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    color: context.accentColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 180,
              child: carsAsync.when(
                data: (cars) => _buildStatusSlider(context, cars),
                loading: () => Center(
                  child: CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(context.accentColor),
                  ),
                ),
                error: (error, _) => Center(
                  child: Text(
                    error.toString(),
                    style: TextStyle(color: context.secondaryAccentColor),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusSlider(BuildContext context, List<CarModel> cars) {
    final l10n = S.of(context);
    
    if (cars.isEmpty) {
      return Center(
        child: Text(
          l10n.noCarsAddedYet,
          style: TextStyle(color: context.secondaryAccentColor),
        ),
      );
    }

    final pages = [
      _buildStatisticsPage(context, cars),
      _buildMaintenanceAlertsPage(context, cars),
      _buildUpcomingRemindersPage(context, cars),
      _buildQuickStatusPage(context, cars),
      _buildRecentActivityPage(context, cars),
    ];

    return Column(
      children: [
        Expanded(
          child: PageView.builder(
            controller: _pageController,
            itemCount: pages.length,
            onPageChanged: (index) => setState(() => _currentPage = index),
            itemBuilder: (context, index) => pages[index],
          ),
        ),
        const SizedBox(height: 8),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: List.generate(pages.length, (index) {
            return Container(
              margin: const EdgeInsets.symmetric(horizontal: 4),
              height: 8,
              width: _currentPage == index ? 24 : 8,
              decoration: BoxDecoration(
                color: _currentPage == index 
                  ? context.accentColor 
                  : context.accentColor.withOpacity(0.3),
                borderRadius: BorderRadius.circular(4),
              ),
            );
          }),
        ),
      ],
    );
  }

  Widget _buildStatisticsPage(BuildContext context, List<CarModel> cars) {
    final l10n = S.of(context);
    final totalCars = cars.length;
    final carsNeedingOilChange = cars.where((car) => car.isOilChangeDue).length;
    final totalMileage = cars.fold<int>(0, (sum, car) => sum + car.currentMileage);
    final avgMileage = totalMileage ~/ totalCars;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildStatItem(
            Icons.directions_car,
            l10n.totalCars,
            totalCars.toString(),
          ),
          const SizedBox(height: 8),
          _buildStatItem(
            Icons.oil_barrel,
            l10n.needOilChange,
            carsNeedingOilChange.toString(),
          ),
          const SizedBox(height: 8),
          _buildStatItem(
            Icons.speed,
            l10n.averageMileage,
            NumberFormat.decimalPattern().format(avgMileage),
          ),
        ],
      ),
    );
  }

  Widget _buildMaintenanceAlertsPage(BuildContext context, List<CarModel> cars) {
    final l10n = S.of(context);
    final alerts = cars
        .where((car) => car.isOilChangeDue)
        .map((car) => _buildAlertItem(
              Icons.warning,
              '${car.year} ${car.make} ${car.model}',
              l10n.oilChangeOverdue,
              context.secondaryAccentColor,
            ))
        .toList();

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: alerts.isEmpty
            ? [
                _buildAlertItem(
                  Icons.check_circle,
                  l10n.allGood,
                  l10n.noMaintenanceAlerts,
                  Colors.green,
                ),
              ]
            : alerts,
      ),
    );
  }

  Widget _buildUpcomingRemindersPage(BuildContext context, List<CarModel> cars) {
    final l10n = S.of(context);
    final reminders = cars
        .where((car) => !car.isOilChangeDue)
        .map((car) {
          final daysUntilDue = car.daysUntilNextChange;
          return _buildReminderItem(
            Icons.event,
            '${car.year} ${car.make} ${car.model}',
            l10n.oilChangeDueInDays(daysUntilDue),
            context.accentColor,
          );
        })
        .toList();

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: reminders.isEmpty
            ? [
                _buildReminderItem(
                  Icons.check_circle,
                  l10n.noUpcomingReminders,
                  l10n.allMaintenanceUpToDate,
                  Colors.green,
                ),
              ]
            : reminders,
      ),
    );
  }

  Widget _buildQuickStatusPage(BuildContext context, List<CarModel> cars) {
    final l10n = S.of(context);
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: cars.take(3).map((car) {
          final mileageUntilDue = car.mileageUntilDue;
          final daysUntilDue = car.daysUntilNextChange;
          
          return _buildStatusItem(
            '${car.year} ${car.make} ${car.model}',
            l10n.nextOilChangeStatus(
              NumberFormat.decimalPattern().format(mileageUntilDue),
              daysUntilDue.toString(),
            ),
            mileageUntilDue <= 500 || daysUntilDue <= 7 
              ? context.secondaryAccentColor 
              : context.accentColor,
          );
        }).toList(),
      ),
    );
  }

  Widget _buildRecentActivityPage(BuildContext context, List<CarModel> cars) {
    final l10n = S.of(context);
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: cars.take(3).map((car) {
          return _buildActivityItem(
            Icons.speed,
            '${car.year} ${car.make} ${car.model}',
            l10n.currentMileageStatus(
              NumberFormat.decimalPattern().format(car.currentMileage),
            ),
            DateTime.now(), // This should come from actual activity timestamp
          );
        }).toList(),
      ),
    );
  }

  Widget _buildStatItem(IconData icon, String label, String value) {
    return Row(
      children: [
        Icon(icon, color: context.accentColor, size: 20),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            label,
            style: TextStyle(color: context.secondaryTextColor),
          ),
        ),
        Text(
          value,
          style: TextStyle(
            color: context.secondaryAccentColor,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  Widget _buildAlertItem(IconData icon, String title, String message, Color color) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    color: context.primaryTextColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  message,
                  style: TextStyle(color: color),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReminderItem(IconData icon, String title, String message, Color color) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    color: context.primaryTextColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  message,
                  style: TextStyle(color: color),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusItem(String title, String status, Color color) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: TextStyle(
              color: context.primaryTextColor,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            status,
            style: TextStyle(color: color),
          ),
        ],
      ),
    );
  }

  Widget _buildActivityItem(IconData icon, String title, String activity, DateTime timestamp) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        children: [
          Icon(icon, color: context.accentColor, size: 20),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    color: context.primaryTextColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  activity,
                  style: TextStyle(color: context.secondaryTextColor),
                ),
                Text(
                  DateFormat.yMMMd().add_jm().format(timestamp),
                  style: TextStyle(
                    color: context.secondaryTextColor.withOpacity(0.7),
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
} 