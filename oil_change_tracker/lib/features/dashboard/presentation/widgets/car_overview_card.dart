import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:oil_change_tracker/core/models/car_model.dart';
import '../../../../generated/app_localizations.dart';
import '../../../../core/theme/theme_extensions.dart';

class CarOverviewCard extends StatelessWidget {
  final List<CarModel> cars;

  const CarOverviewCard({
    super.key,
    required this.cars,
  });

  @override
  Widget build(BuildContext context) {
    final l10n = S.of(context);
    
    return Card(
      color: context.containerBackgroundColor,
      elevation: 4,
      surfaceTintColor: Colors.transparent,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(color: context.accentColor, width: 1),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Icon(Icons.directions_car, color: context.accentColor, size: 24),
                    const SizedBox(width: 8),
                    Text(
                      l10n.myCars,
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        color: context.accentColor,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                TextButton.icon(
                  onPressed: () => context.push('/cars'),
                  icon: Icon(Icons.arrow_forward, color: context.secondaryAccentColor),
                  label: Text(
                    l10n.viewAll,
                    style: TextStyle(color: context.secondaryAccentColor),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (cars.isEmpty)
              Center(
                child: Padding(
                  padding: const EdgeInsets.symmetric(vertical: 16.0),
                  child: Column(
                    children: [
                      Icon(Icons.directions_car_outlined, size: 48, color: context.accentColor),
                      const SizedBox(height: 16),
                      Text(
                        l10n.noCarsAddedYet,
                        style: TextStyle(color: context.secondaryAccentColor, fontSize: 16),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        l10n.addFirstCarMessage,
                        style: TextStyle(color: context.secondaryTextColor),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              )
            else
              _buildCarStats(context, l10n),
          ],
        ),
      ),
    );
  }

  Widget _buildCarStats(BuildContext context, S l10n) {
    final needOilChange = cars.where((car) => car.isDueForOilChange).length;
    final totalKmDriven = cars.fold<int>(
      0,
      (sum, car) => sum + car.kilometersSinceLastChange,
    );
    final totalKmRemaining = cars.fold<int>(
      0,
      (sum, car) => sum + car.kilometersUntilNextChange,
    );

    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            _buildStatItem(
              context,
              l10n.totalCars,
              cars.length.toString(),
              Icons.directions_car,
            ),
            _buildStatItem(
              context,
              l10n.needOilChange,
              needOilChange.toString(),
              Icons.oil_barrel,
              isWarning: needOilChange > 0,
            ),
          ],
        ),
        const SizedBox(height: 20),
        _buildKilometerStats(
          context,
          totalKmDriven,
          totalKmRemaining,
          l10n,
        ),
      ],
    );
  }

  Widget _buildStatItem(
    BuildContext context,
    String label,
    String value,
    IconData icon, {
    bool isWarning = false,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: context.containerBackgroundColor.withOpacity(0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: context.accentColor),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            size: 32,
            color: isWarning ? context.secondaryAccentColor : context.accentColor,
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
              color: isWarning ? context.secondaryAccentColor : context.secondaryTextColor,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            label,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: context.secondaryTextColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildKilometerStats(
    BuildContext context,
    int totalKmDriven,
    int totalKmRemaining,
    S l10n,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: context.containerBackgroundColor.withOpacity(0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: context.accentColor),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            l10n.oilChangeProgress,
            style: TextStyle(
              color: context.accentColor,
              fontWeight: FontWeight.bold,
              fontSize: 16,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              _buildKilometerItem(
                context,
                l10n.driven,
                totalKmDriven,
                Icons.speed,
                context.secondaryAccentColor,
              ),
              Container(
                height: 50,
                width: 1,
                color: context.accentColor,
              ),
              _buildKilometerItem(
                context,
                l10n.remaining,
                totalKmRemaining,
                Icons.update,
                context.accentColor,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildKilometerItem(
    BuildContext context,
    String label,
    int value,
    IconData icon,
    Color color,
  ) {
    return Expanded(
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            '$value km',
            style: TextStyle(
              color: color,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            label,
            style: TextStyle(
              color: context.secondaryTextColor,
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }
} 