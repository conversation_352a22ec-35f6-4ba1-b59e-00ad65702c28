import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/models/car_model.dart';
import '../../../../generated/app_localizations.dart';

class DueCarItem extends StatelessWidget {
  final CarModel car;

  const DueCarItem({
    super.key,
    required this.car,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final l10n = S.of(context);

    return ListTile(
      contentPadding: EdgeInsets.zero,
      title: Text(
        '${car.year} ${car.make} ${car.model}',
        style: theme.textTheme.titleSmall,
      ),
      subtitle: Text(
        car.daysUntilNextChange <= 0
            ? l10n.overdueDays(-car.daysUntilNextChange)
            : l10n.daysUntilNextChange(car.daysUntilNextChange),
        style: theme.textTheme.bodySmall?.copyWith(
          color: car.daysUntilNextChange <= 0
              ? theme.colorScheme.error
              : theme.colorScheme.onSurfaceVariant,
        ),
      ),
      trailing: Icon(
        Icons.warning,
        color: theme.colorScheme.error,
      ),
      onTap: () => context.push('/cars/${car.id}'),
    );
  }
} 