import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/models/car_model.dart';
import '../../../../generated/app_localizations.dart';
import '../../../car_management/providers/car_provider.dart';
import '../../../../core/theme/theme_extensions.dart';

class UpcomingMaintenanceCard extends StatelessWidget {
  final List<CarModel> cars;

  const UpcomingMaintenanceCard({
    super.key,
    required this.cars,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context); // Get theme
    final l10n = S.of(context);
    final upcomingCars = cars
        .where((car) => !car.isDueForOilChange)
        .toList()
      ..sort((a, b) => b.kilometersUntilNextChange.compareTo(a.kilometersUntilNextChange));

    return Card(
      color: theme.cardTheme.color ?? context.containerBackgroundColor, // Use theme card color
      elevation: theme.cardTheme.elevation ?? 4, // Use theme elevation
      shape: theme.cardTheme.shape ?? RoundedRectangleBorder( // Use theme shape
        borderRadius: BorderRadius.circular(16),
        // side: BorderSide(color: context.accentColor, width: 1), // Keep custom border or remove if theme handles it
      ),
      margin: theme.cardTheme.margin ?? const EdgeInsets.symmetric(horizontal: 8.0, vertical: 8.0), // Consistent margin
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Icon(Icons.update, color: theme.colorScheme.primary, size: 24), // Use theme primary color
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    l10n.upcomingMaintenance,
                    style: theme.textTheme.titleMedium?.copyWith( // Use theme text style
                      color: theme.colorScheme.primary,
                      fontWeight: FontWeight.bold,
                    ),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            if (upcomingCars.isEmpty)
              _buildEmptyState(context, l10n, theme) // Pass theme
            else
              _buildMaintenanceList(context, l10n, upcomingCars, theme), // Pass theme
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context, S l10n, ThemeData theme) { // Add theme parameter
    return Center(
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: theme.colorScheme.secondary.withOpacity(0.1), // Use theme secondary color
              shape: BoxShape.circle,
              border: Border.all(color: theme.colorScheme.secondary.withOpacity(0.5)),
            ),
            child: Icon(
              Icons.check_circle_outline, // Changed icon for better visual
              size: 48,
              color: theme.colorScheme.secondary, // Use theme secondary
            ),
          ),
          const SizedBox(height: 16),
          Text(
            l10n.noUpcomingMaintenance,
            style: theme.textTheme.titleSmall?.copyWith( // Use theme text style
              color: theme.colorScheme.onSurface,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            l10n.allCarsUpToDate,
            style: theme.textTheme.bodyMedium?.copyWith( // Use theme text style
              color: theme.colorScheme.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildMaintenanceList(
    BuildContext context,
    S l10n,
    List<CarModel> upcomingCars,
    ThemeData theme, // Add theme parameter
  ) {
    return Column(
      children: [
        ...upcomingCars.take(3).map(
          (car) => Column(
            children: [
              _UpcomingMaintenanceItem(car: car, l10n: l10n),
              if (car != upcomingCars.take(3).last)
                Divider(color: theme.dividerColor.withOpacity(0.5)), // Use theme divider color
            ],
          ),
        ),
        if (upcomingCars.length > 3) ...[
          Divider(color: theme.dividerColor.withOpacity(0.5)), // Use theme divider color
          Center(
            child: TextButton.icon(
              onPressed: () => context.push('/cars'),
              icon: Icon(Icons.arrow_forward, color: theme.colorScheme.secondary), // Use theme secondary
              label: Text(
                l10n.viewAll,
                style: TextStyle(color: theme.colorScheme.secondary), // Use theme secondary
              ),
            ),
          ),
        ],
      ],
    );
  }
}

class _UpcomingMaintenanceItem extends ConsumerWidget {
  final CarModel car;
  final S l10n;

  const _UpcomingMaintenanceItem({
    required this.car,
    required this.l10n,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context); // Get theme
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceContainerHighest.withOpacity(0.5), // Use a subtle surface variant
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: theme.colorScheme.outline.withOpacity(0.3)), // Use outline color
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              CircleAvatar(
                backgroundColor: theme.colorScheme.primaryContainer, // Use primary container
                child: Icon(Icons.directions_car, color: theme.colorScheme.onPrimaryContainer, size: 20), // Generic car icon
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '${car.year} ${car.make} ${car.model}',
                      style: theme.textTheme.titleSmall?.copyWith( // Use theme text style
                        color: theme.colorScheme.onSurface,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      car.kilometersUntilNextChange > 0
                          ? l10n.kmRemaining(car.kilometersUntilNextChange)
                          : l10n.oilChangeOverdue,
                      style: theme.textTheme.bodySmall?.copyWith( // Use theme text style
                        color: car.kilometersUntilNextChange > 0
                            ? theme.colorScheme.onSurfaceVariant
                            : theme.colorScheme.error, // Use error color for overdue
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
              IconButton(
                icon: Icon(Icons.edit_road_outlined, color: theme.colorScheme.secondary), // Use theme secondary
                onPressed: () => _showMileageUpdateDialog(context, ref),
                tooltip: l10n.updateMileage,
              ),
              IconButton(
                icon: Icon(Icons.chevron_right, color: theme.colorScheme.secondary), // Use theme secondary
                onPressed: () {
                  if (car.id != null) {
                     context.push('/cars/${car.id}');
                  } else {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(content: Text(l10n.errorLoadingCarDetails)), // Or a more specific error
                    );
                  }
                },
              ),
            ],
          ),
          const SizedBox(height: 12),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    l10n.kmDriven(car.kilometersSinceLastChange),
                    style: theme.textTheme.bodySmall?.copyWith(color: theme.colorScheme.onSurfaceVariant, fontSize: 12),
                  ),
                  Text(
                    l10n.kmRemaining(car.kilometersUntilNextChange),
                    style: theme.textTheme.bodySmall?.copyWith(color: theme.colorScheme.primary, fontSize: 12, fontWeight: FontWeight.bold),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              ClipRRect(
                borderRadius: BorderRadius.circular(4),
                child: LinearProgressIndicator(
                  value: car.oilChangeProgress.clamp(0.0, 1.0), // Ensure progress is between 0 and 1
                  backgroundColor: theme.colorScheme.surfaceContainerHighest, // Use surface variant for background
                  valueColor: AlwaysStoppedAnimation<Color>(
                    car.oilChangeProgress >= 1.0 ? theme.colorScheme.error :
                    (car.oilChangeProgress > 0.8 ? theme.colorScheme.secondary : theme.colorScheme.primary),
                  ),
                  minHeight: 8, // Slightly thicker
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Future<void> _showMileageUpdateDialog(BuildContext context, WidgetRef ref) async {
    final theme = Theme.of(context); // Get theme for dialog
    final controller = TextEditingController(text: car.currentMileage.toString());
    final formKey = GlobalKey<FormState>();

    return showDialog(
      context: context,
      builder: (dialogContext) => AlertDialog( // Use dialogContext
        backgroundColor: theme.dialogBackgroundColor,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
          // side: BorderSide(color: theme.colorScheme.outline, width: 1), // Optional: use outline color
        ),
        title: Text(
          l10n.updateMileage,
          style: theme.textTheme.titleLarge?.copyWith(color: theme.colorScheme.onSurface),
        ),
        content: Form(
          key: formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                '${car.year} ${car.make} ${car.model}',
                style: theme.textTheme.titleMedium?.copyWith(
                  color: theme.colorScheme.onSurfaceVariant,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: controller,
                keyboardType: TextInputType.number,
                inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                style: TextStyle(color: theme.colorScheme.onSurface),
                decoration: InputDecoration(
                  labelText: l10n.currentMileage,
                  labelStyle: TextStyle(color: theme.colorScheme.primary),
                  hintText: l10n.pleaseEnterCurrentMileage,
                  hintStyle: TextStyle(color: theme.colorScheme.onSurfaceVariant.withOpacity(0.7)),
                  filled: true,
                  fillColor: theme.colorScheme.surfaceContainerHighest.withOpacity(0.1),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8.0),
                    borderSide: BorderSide(color: theme.colorScheme.outline.withOpacity(0.5)),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8.0),
                    borderSide: BorderSide(color: theme.colorScheme.outline.withOpacity(0.3)),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8.0),
                    borderSide: BorderSide(color: theme.colorScheme.primary, width: 1.5),
                  ),
                  errorBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8.0),
                    borderSide: BorderSide(color: theme.colorScheme.error),
                  ),
                  focusedErrorBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8.0),
                    borderSide: BorderSide(color: theme.colorScheme.error, width: 1.5),
                  ),
                  suffixText: 'km',
                  suffixStyle: TextStyle(color: theme.colorScheme.onSurfaceVariant),
                  contentPadding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 10.0),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return l10n.pleaseEnterMileage;
                  }
                  final mileage = int.tryParse(value);
                  if (mileage == null) {
                    return l10n.invalidMileage;
                  }
                  if (mileage <= car.currentMileage) {
                    return l10n.errorLastMileageGreater;
                  }
                  return null;
                },
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(dialogContext), // Use dialogContext
            child: Text(
              l10n.cancel,
              style: TextStyle(color: theme.colorScheme.primary),
            ),
          ),
          ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: theme.colorScheme.primary,
              foregroundColor: theme.colorScheme.onPrimary,
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8.0)),
            ),
            onPressed: () async {
              if (formKey.currentState?.validate() ?? false) {
                final newMileage = int.parse(controller.text);
                Navigator.pop(dialogContext); // Use dialogContext
                
                try {
                  // Ensure car.id is not null before proceeding
                  if (car.id == null) {
                    if (dialogContext.mounted) { // Check mount status of dialogContext
                        ScaffoldMessenger.of(dialogContext).showSnackBar(
                          SnackBar(
                            content: Text(l10n.errorGeneric("Car ID is missing")), // Changed to positional argument
                            backgroundColor: theme.colorScheme.error,
                          ),
                        );
                    }
                    return;
                  }

                  final carNotifier = ref.read(carNotifierProvider(car.id!).notifier);
                  await carNotifier.updateCarMileage(car.id!, newMileage, context); // Use original context for SnackBar if needed after dialog closes
                  
                  // Show success on the original context if dialog is dismissed
                  if (context.mounted) {
                     ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(l10n.mileageUpdated),
                        backgroundColor: Colors.green, // Consider using theme success color
                      ),
                    );
                  }
                } catch (e) {
                  if (context.mounted) { // Check mount status of original context
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('${l10n.error}: $e'),
                        backgroundColor: theme.colorScheme.error,
                      ),
                    );
                  }
                }
              }
            },
            child: Text(l10n.update),
          ),
        ],
      ),
    );
  }
} 