import 'package:flutter/material.dart';
import '../../../../generated/app_localizations.dart';

class EditCarScreen extends StatefulWidget {
  const EditCarScreen({super.key});

  // ... (existing code)
  
  @override
  State<EditCarScreen> createState() => _EditCarScreenState();
}

class _EditCarScreenState extends State<EditCarScreen> {
  // ... (existing code)
  bool imageUploadFailed = false;
  bool carUpdatedSuccessfully = false;
  
  @override
  Widget build(BuildContext context) {
    final l10n = S.of(context);
    // ... (existing code)

    return Scaffold(
      // ... (existing code)

      body: SingleChildScrollView(
        child: Column(
          children: [
            // ... (existing code)

            if (imageUploadFailed)
              AlertDialog(
                title: Text(l10n.errorUploadingImage),
                content: Text(l10n.carSavedAnyway),
              ),

            if (carUpdatedSuccessfully)
              AlertDialog(
                title: Text(l10n.success),
                content: Text(l10n.carSavedAnyway),
              ),

            // ... (rest of the existing code)
          ],
        ),
      ),
    );
  }
} 