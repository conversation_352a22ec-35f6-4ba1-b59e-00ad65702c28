import 'package:flutter/material.dart';
import '../../../../core/theme/theme_extensions.dart';
import '../../../../generated/app_localizations.dart';

class QuickPromptChips extends StatelessWidget {
  final Function(String)? onPromptSelected;

  const QuickPromptChips({
    super.key,
    this.onPromptSelected,
  });

  List<String> _localizedPrompts(S l10n) => [
        l10n.chatPromptEngineNoStart,
        l10n.chatPromptAbsLight,
        l10n.chatPromptOilLight,
        l10n.chatPromptEngineNoise,
        l10n.chatPromptVibration,
        l10n.chatPromptBrakes,
        l10n.chatPromptHighTemp,
        l10n.chatPromptBattery,
      ];

  @override
  Widget build(BuildContext context) {
    final l10n = S.of(context);
    final quickPrompts = _localizedPrompts(l10n);
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            l10n.chatFaqTitle,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: context.primaryTextColor,
            ),
          ),
          const SizedBox(height: 12),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: quickPrompts.map((prompt) {
              return GestureDetector(
                onTap: () => onPromptSelected?.call(prompt),
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 8,
                  ),
                  decoration: BoxDecoration(
                    color: context.accentColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: context.accentColor.withOpacity(0.3),
                    ),
                  ),
                  child: Text(
                    prompt,
                    style: TextStyle(
                      fontSize: 14,
                      color: context.accentColor,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }
}
