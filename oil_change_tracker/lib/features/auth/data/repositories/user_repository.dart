import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:oil_change_tracker/core/models/user_model.dart';
import 'package:oil_change_tracker/core/utils/firebase_collections.dart';
import 'dart:developer' as dev;

final userRepositoryProvider = Provider<UserRepository>((ref) {
  return UserRepository(
    firestore: FirebaseFirestore.instance,
    auth: FirebaseAuth.instance,
  );
});

class UserRepository {
  final FirebaseFirestore _firestore;
  final FirebaseAuth _auth;

  UserRepository({
    required FirebaseFirestore firestore,
    required FirebaseAuth auth,
  })  : _firestore = firestore,
        _auth = auth;

  CollectionReference<Map<String, dynamic>> get _usersCollection =>
      _firestore.collection(FirebaseCollections.users);

  Stream<UserModel?> watchCurrentUser() {
    final user = _auth.currentUser;
    if (user == null) return Stream.value(null);

    return _usersCollection
        .doc(user.uid)
        .snapshots()
        .map((doc) => doc.exists ? UserModel.fromFirestore(doc) : null);
  }

  Future<UserModel?> getCurrentUser() async {
    final user = _auth.currentUser;
    if (user == null) return null;

    final doc = await _usersCollection.doc(user.uid).get();
    return doc.exists ? UserModel.fromFirestore(doc) : null;
  }

  Future<void> createUser(UserModel user) async {
    if (user.id == null) throw Exception('User ID cannot be null');
    
    try {
      dev.log('UserRepository: Creating user document for id: ${user.id}');
      
      // Enhance the user data with timestamp fields if not present
      final userData = user.toFirestore();
      
      if (!userData.containsKey('createdAt')) {
        userData['createdAt'] = FieldValue.serverTimestamp();
      }
      
      if (!userData.containsKey('lastSignIn')) {
        userData['lastSignIn'] = FieldValue.serverTimestamp();
      }
      
      // Set with merge to ensure we don't overwrite existing data
      await _usersCollection.doc(user.id).set(userData, SetOptions(merge: true));
      
      // Verify document was created
      final docSnapshot = await _usersCollection.doc(user.id).get();
      if (!docSnapshot.exists) {
        dev.log('UserRepository: Document not found after creation attempt');
        throw Exception('Failed to create user document');
      }
      
      dev.log('UserRepository: User document created successfully');
    } catch (e) {
      dev.log('UserRepository: Error creating user document: $e');
      rethrow; // Re-throw to allow calling code to handle the error
    }
  }

  Future<void> updateUser(UserModel user) async {
    if (user.id == null) throw Exception('User ID cannot be null');
    await _usersCollection.doc(user.id).update(user.toFirestore());
  }

  Future<void> deleteUser(String id) async {
    await _usersCollection.doc(id).delete();
  }

  Future<void> addCarToUser(String userId, String carId) async {
    await _usersCollection.doc(userId).update({
      'carIds': FieldValue.arrayUnion([carId]),
    });
  }

  Future<void> removeCarFromUser(String userId, String carId) async {
    await _usersCollection.doc(userId).update({
      'carIds': FieldValue.arrayRemove([carId]),
    });
  }

  Future<void> updateUserProfile(UserModel user) async {
    try {
      await _firestore.collection(FirebaseCollections.users).doc(user.id).set(
            user.toFirestore(),
            SetOptions(merge: true),
          );
    } catch (e) {
      dev.log('Error updating user profile: $e');
      rethrow;
    }
  }

  Future<void> saveUserFcmToken(String userId, String token) async {
    try {
      // Direct update to Firestore without using the model
      await _firestore.collection(FirebaseCollections.users).doc(userId).set({
        'fcmToken': token,
        'lastTokenUpdate': FieldValue.serverTimestamp(),
      }, SetOptions(merge: true));
      
      dev.log('FCM token saved for user: $userId');
    } catch (e) {
      dev.log('Error saving FCM token: $e');
      // Don't rethrow as this shouldn't block app functionality
    }
  }

  Stream<List<String>> getUserTokensForNotifications(List<String> userIds) {
    return _firestore
        .collection(FirebaseCollections.users)
        .where('id', whereIn: userIds)
        .where('fcmToken', isNull: false)
        .snapshots()
        .map((snapshot) {
          return snapshot.docs
              .map((doc) => doc.data()['fcmToken'] as String?)
              .where((token) => token != null && token.isNotEmpty)
              .map((token) => token!)
              .toList();
        });
  }

  Future<void> updateLastLogin(String userId) async {
    try {
      await _firestore.collection(FirebaseCollections.users).doc(userId).set({
        'lastLoginAt': FieldValue.serverTimestamp(),
      }, SetOptions(merge: true));
      
      dev.log('Updated last login timestamp for user: $userId');
    } catch (e) {
      dev.log('Error updating last login timestamp: $e');
    }
  }
} 