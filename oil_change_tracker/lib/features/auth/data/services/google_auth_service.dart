import 'package:firebase_auth/firebase_auth.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'dart:developer' as dev;

class GoogleAuthService {
  final FirebaseAuth _firebaseAuth;
  late final GoogleSignIn _googleSignIn;

  GoogleAuthService({FirebaseAuth? firebaseAuth}) : 
    _firebaseAuth = firebaseAuth ?? FirebaseAuth.instance {
    // Initialize GoogleSignIn with the appropriate configuration
    _googleSignIn = GoogleSignIn(
      // Don't specify scopes here as they're included by default
      // In debug mode, silent sign-in can cause issues
      signInOption: SignInOption.standard,
      // No server client ID needed for mobile
    );
  }

  Future<UserCredential?> signInWithGoogle() async {
    try {
      dev.log('GoogleAuthService: Starting Google Sign In flow');
      
      // Clear previous sign-in state to avoid issues
      await _googleSignIn.signOut();
      
      // Start fresh sign-in flow
      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();
      
      if (googleUser == null) {
        dev.log('GoogleAuthService: User cancelled Google Sign In');
        return null;
      }
      
      dev.log('GoogleAuthService: Selected account: ${googleUser.email}');
      
      // Get the authentication details
      final GoogleSignInAuthentication googleAuth = await googleUser.authentication;
      
      // Create a Firebase credential
      final credential = GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );
      
      dev.log('GoogleAuthService: Signing in with Firebase');
      
      try {
        // Sign in with Firebase using the credential
        final userCredential = await _firebaseAuth.signInWithCredential(credential);
        
        dev.log('GoogleAuthService: Firebase sign in successful');
        
        return userCredential;
      } catch (e) {
        dev.log('GoogleAuthService: Error during Firebase sign in: $e');
        
        // Check if the error is related to PigeonUserDetails
        if (e.toString().contains('PigeonUserDetails')) {
          dev.log('GoogleAuthService: Detected PigeonUserDetails error, checking current auth state');
          
          // Check if the user is actually signed in despite the error
          final currentUser = _firebaseAuth.currentUser;
          if (currentUser != null) {
            dev.log('GoogleAuthService: User is authenticated (${currentUser.uid}) despite the error');
            
            // Create a safe UserCredential
            return _createSafeUserCredential(currentUser);
          }
        }
        
        // If we can't recover, rethrow
        rethrow;
      }
    } catch (e) {
      dev.log('GoogleAuthService: Error during Google Sign In: $e');
      // Sign out on error to ensure a clean state
      await signOut();
      rethrow;
    }
  }
  
  // Creates a safe UserCredential that avoids PigeonUserDetails issues
  UserCredential _createSafeUserCredential(User user) {
    dev.log('GoogleAuthService: Creating safe UserCredential');
    return MockUserCredential(user);
  }
  
  Future<void> signOut() async {
    await Future.wait([
      _firebaseAuth.signOut(),
      _googleSignIn.signOut(),
    ]);
  }
}

// Mock implementation of UserCredential that doesn't trigger PigeonUserDetails errors
class MockUserCredential implements UserCredential {
  final User _user;
  
  MockUserCredential(this._user);
  
  @override
  User get user => _user;
  
  // We need to implement all the other properties, but we'll just return null
  // since they aren't used in our app
  @override
  AdditionalUserInfo? get additionalUserInfo => null;
  
  @override
  AuthCredential? get credential => null;
  
  @override
  dynamic noSuchMethod(Invocation invocation) => null;
} 