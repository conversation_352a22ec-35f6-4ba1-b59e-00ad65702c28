import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/models/user_model.dart';
import '../../data/providers/google_auth_provider.dart';
import '../../data/repositories/user_repository.dart';
import 'dart:developer' as dev;

/// State notifier for authentication
class AuthNotifier extends StateNotifier<AsyncValue<UserModel?>> {
  final FirebaseAuth _auth;
  final Ref _ref;

  AuthNotifier(this._auth, this._ref)
      : super(const AsyncValue.loading()) {
    _initialize();
  }

  void _initialize() {
    _auth.authStateChanges().listen((user) {
      if (user == null) {
        state = const AsyncValue.data(null);
      } else {
        state = AsyncValue.data(UserModel.fromFirebaseUser(user));
      }
    });
  }

  Future<UserCredential?> signInWithGoogle() async {
    try {
      state = const AsyncValue.loading();
      
      dev.log('AuthNotifier: Beginning Google Sign In');
      
      // Use the GoogleAuthService for sign in
      final googleAuthService = _ref.read(googleAuthServiceProvider);
      
      try {
        final userCredential = await googleAuthService.signInWithGoogle();
        
        if (userCredential == null) {
          // User cancelled sign in
          state = const AsyncValue.data(null);
          return null;
        }
        
        final user = userCredential.user;
        if (user == null) {
          throw Exception('Failed to sign in with Google: No user returned');
        }
        
        // Update user in Firestore
        final userRepository = _ref.read(userRepositoryProvider);
        final userModel = UserModel.fromFirebaseUser(user);
        await userRepository.createUser(userModel);
        
        // State will be updated via the auth state changes listener
        return userCredential;
      } catch (e) {
        dev.log('AuthNotifier: Caught error during Google Sign In: $e');
        
        // Check if this is the PigeonUserDetails error
        if (e.toString().contains('PigeonUserDetails')) {
          dev.log('AuthNotifier: This is a PigeonUserDetails error, checking if user is signed in');
          
          // Check if the user is actually authenticated despite the error
          final currentUser = _auth.currentUser;
          if (currentUser != null) {
            dev.log('AuthNotifier: User is authenticated (${currentUser.uid}) despite the error');
            
            // Update the user in Firestore since we can't return a UserCredential
            final userRepository = _ref.read(userRepositoryProvider);
            final userModel = UserModel.fromFirebaseUser(currentUser);
            await userRepository.createUser(userModel);
            
            // Update state with the current user
            state = AsyncValue.data(userModel);
            
            // Return a mock UserCredential to signal success
            return MockUserCredential(currentUser);
          }
        }
        
        // Re-throw the error if we couldn't recover
        rethrow;
      }
    } catch (e) {
      dev.log('AuthNotifier: Error during Google Sign In: $e');
      state = AsyncValue.error(e, StackTrace.current);
      rethrow;
    }
  }

  Future<void> signOut() async {
    try {
      state = const AsyncValue.loading();
      
      // Use the GoogleAuthService for sign out
      final googleAuthService = _ref.read(googleAuthServiceProvider);
      await googleAuthService.signOut();
      
      // State will be updated via the auth state changes listener
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
      rethrow;
    }
  }
}

/// Provider for the auth notifier
final authNotifierProvider =
    StateNotifierProvider<AuthNotifier, AsyncValue<UserModel?>>((ref) {
  final auth = FirebaseAuth.instance;
  return AuthNotifier(auth, ref);
});

// Mock implementation of UserCredential for handling PigeonUserDetails errors
class MockUserCredential implements UserCredential {
  final User _user;
  
  MockUserCredential(this._user);
  
  @override
  User get user => _user;
  
  @override
  AdditionalUserInfo? get additionalUserInfo => null;
  
  @override
  AuthCredential? get credential => null;
  
  @override
  dynamic noSuchMethod(Invocation invocation) => null;
} 