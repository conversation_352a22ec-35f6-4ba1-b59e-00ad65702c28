import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/utils/form_validators.dart';
import '../../../../generated/app_localizations.dart';
import '../../../auth/providers/auth_provider.dart';
import '../../../../core/theme/theme_extensions.dart';
import 'dart:developer' as dev;

class ForgotPasswordScreen extends ConsumerStatefulWidget {
  const ForgotPasswordScreen({super.key});

  @override
  ConsumerState<ForgotPasswordScreen> createState() => _ForgotPasswordScreenState();
}

class _ForgotPasswordScreenState extends ConsumerState<ForgotPasswordScreen> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  bool _isLoading = false;
  String? _errorMessage;
  bool _resetSent = false;
  bool _isGoogleUser = false;

  @override
  void initState() {
    super.initState();
    // Check if the user is signed in with Google
    _checkAuthProvider();
  }

  void _checkAuthProvider() {
    final user = ref.read(authProvider).asData?.value;
    if (user != null) {
      final isGoogleUser = user.providerData?.any((info) {
        return info['providerId'] == 'google.com';
      }) ?? false;
      
      setState(() {
        _isGoogleUser = isGoogleUser;
        if (isGoogleUser) {
          _emailController.text = user.email;
        }
      });
      
      if (isGoogleUser) {
        dev.log('ForgotPasswordScreen: User is signed in with Google');
      }
    }
  }

  @override
  void dispose() {
    _emailController.dispose();
    super.dispose();
  }

  Future<void> _resetPassword() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      await ref.read(authProvider.notifier).resetPassword(
        _emailController.text.trim(),
      );

      if (mounted) {
        setState(() {
          _resetSent = true;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = e.toString().contains('user-not-found') 
              ? S.of(context).emailNotRegistered 
              : S.of(context).errorOccurred;
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final l10n = S.of(context);
    
    // Get screen height to ensure proper sizing
    final screenHeight = MediaQuery.of(context).size.height;

    return Scaffold(
      backgroundColor: context.containerBackgroundColor,
      appBar: AppBar(
        title: Text(
          l10n.forgotPassword,
          style: TextStyle(
            color: context.accentColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: context.containerBackgroundColor,
        elevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: context.accentColor),
          onPressed: () => context.pop(),
        ),
      ),
      body: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              context.containerBackgroundColor,
              context.secondaryAccentColor.withOpacity(0.8),
            ],
            stops: const [0.3, 1.0],
          ),
        ),
        child: SafeArea(
          child: SingleChildScrollView(
            child: ConstrainedBox(
              constraints: BoxConstraints(
                minHeight: screenHeight - AppBar().preferredSize.height - MediaQuery.of(context).padding.top - MediaQuery.of(context).padding.bottom,
              ),
              child: Padding(
                padding: const EdgeInsets.all(24.0),
                child: _resetSent 
                    ? _buildSuccessMessage(context, l10n) 
                    : _isGoogleUser 
                        ? _buildGoogleUserMessage(context, l10n)
                        : _buildResetForm(context, l10n),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildResetForm(BuildContext context, S l10n) {
    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          const SizedBox(height: 20),
          Icon(
            Icons.lock_reset,
            size: 80,
            color: context.accentColor,
          ),
          const SizedBox(height: 32),
          Text(
            l10n.forgotPasswordDescription,
            style: TextStyle(
              fontSize: 16,
              color: context.primaryTextColor,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 32),
          
          // Error message if present
          if (_errorMessage != null)
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.red.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.red.shade300),
              ),
              child: Text(
                _errorMessage!,
                style: TextStyle(color: Colors.red.shade300),
                textAlign: TextAlign.center,
              ),
            ),
          if (_errorMessage != null)
            const SizedBox(height: 24),
            
          TextFormField(
            controller: _emailController,
            style: TextStyle(color: context.primaryTextColor),
            enabled: !_isLoading,
            decoration: InputDecoration(
              labelText: l10n.email,
              labelStyle: TextStyle(color: context.secondaryTextColor),
              prefixIcon: Icon(Icons.email, color: context.accentColor),
              enabledBorder: OutlineInputBorder(
                borderSide: BorderSide(color: context.secondaryAccentColor.withOpacity(0.3)),
                borderRadius: BorderRadius.circular(12),
              ),
              focusedBorder: OutlineInputBorder(
                borderSide: BorderSide(color: context.accentColor),
                borderRadius: const BorderRadius.all(Radius.circular(12)),
              ),
              errorBorder: OutlineInputBorder(
                borderSide: BorderSide(color: Colors.red.shade300),
                borderRadius: BorderRadius.circular(12),
              ),
              focusedErrorBorder: OutlineInputBorder(
                borderSide: BorderSide(color: Colors.red.shade300),
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            keyboardType: TextInputType.emailAddress,
            textInputAction: TextInputAction.done,
            validator: FormValidators.validateEmail,
          ),
          const SizedBox(height: 32),
          ElevatedButton(
            onPressed: _isLoading ? null : _resetPassword,
            style: ElevatedButton.styleFrom(
              backgroundColor: context.accentColor,
              foregroundColor: context.isDarkMode ? Colors.black : Colors.white,
              minimumSize: const Size(double.infinity, 56),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              elevation: 4,
            ),
            child: _isLoading
                ? SizedBox(
                    height: 24,
                    width: 24,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(context.isDarkMode ? Colors.black : Colors.white),
                    ),
                  )
                : Text(
                    l10n.resetPassword,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildSuccessMessage(BuildContext context, S l10n) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        const SizedBox(height: 40),
        Icon(
          Icons.check_circle,
          size: 100,
          color: Colors.green,
        ),
        const SizedBox(height: 32),
        Text(
          l10n.resetPasswordEmailSent,
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: context.accentColor,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 24),
        Text(
          l10n.resetPasswordCheckEmail,
          style: TextStyle(
            fontSize: 16,
            color: context.primaryTextColor,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 40),
        ElevatedButton(
          onPressed: () => context.pop(),
          style: ElevatedButton.styleFrom(
            backgroundColor: context.accentColor,
            foregroundColor: context.isDarkMode ? Colors.black : Colors.white,
            minimumSize: const Size(double.infinity, 56),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
          child: Text(
            l10n.backToLogin,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildGoogleUserMessage(BuildContext context, S l10n) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        const SizedBox(height: 20),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: context.accentColor.withOpacity(0.1),
          ),
          child: Icon(
            Icons.info_outline,
            size: 80,
            color: context.accentColor,
          ),
        ),
        const SizedBox(height: 32),
        Text(
          'Google Account Password Reset',
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: context.primaryTextColor,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 20),
        Text(
          'You are signed in with Google. You cannot reset your Google password through this app.',
          style: TextStyle(
            fontSize: 16,
            color: context.primaryTextColor,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 16),
        Text(
          'Please visit your Google Account settings to manage your password.',
          style: TextStyle(
            fontSize: 16,
            color: context.primaryTextColor,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 40),
        ElevatedButton(
          onPressed: () => context.pop(),
          style: ElevatedButton.styleFrom(
            backgroundColor: context.accentColor,
            foregroundColor: context.isDarkMode ? Colors.black : Colors.white,
            minimumSize: const Size(double.infinity, 56),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            elevation: 4,
          ),
          child: Text(
            'Go Back',
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ],
    );
  }
} 