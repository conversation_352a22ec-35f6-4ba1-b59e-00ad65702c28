import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/utils/form_validators.dart';
import '../../../../generated/app_localizations.dart';
import '../../providers/auth_provider.dart';
import '../../../../core/providers/auth_providers.dart' as core_auth;
import '../../../../core/services/auth_service.dart' show AuthProcess;
import 'dart:developer' as dev;
import '../../../../core/theme/theme_extensions.dart';

class SignupScreen extends ConsumerStatefulWidget {
  const SignupScreen({super.key});

  @override
  ConsumerState<SignupScreen> createState() => _SignupScreenState();
}

class _SignupScreenState extends ConsumerState<SignupScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  bool _obscurePassword = true;
  bool _obscureConfirmPassword = true;
  String? _errorMessage;
  bool _isGoogleSigningIn = false;

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  Future<void> _handleSignup() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _errorMessage = null);

    try {
      await ref.read(authProvider.notifier).signUpWithEmail(
            name: _nameController.text.trim(),
            email: _emailController.text.trim(),
            password: _passwordController.text,
          );

      if (!mounted) return;

      // Mark as new signup for welcome subscription screen
      ref.read(core_auth.isNewSignupProvider.notifier).state = true;

      dev.log('Signup successful - navigating to email verification screen');
      context.go('/email-verification');
    } catch (e) {
      if (!mounted) return;
      _handleAuthError('Email sign-up failed', e.toString(), e);
    }
  }

  void _handleAuthError(
      String logMessage, String? userMessage, Object? exception) {
    // Log the error for debugging
    if (exception != null) {
      dev.log('SignupScreen ERROR: $logMessage', error: exception);
    } else {
      dev.log('SignupScreen ERROR: $logMessage - $userMessage');
    }

    // Show error to user
    setState(
        () => _errorMessage = userMessage ?? 'An unexpected error occurred');
  }

  Future<void> _handleGoogleSignIn() async {
    final authService = ref.read(core_auth.authServiceProvider);
    final authProcess = ref.watch(core_auth.authProcessProvider);

    // Don't start if already in process
    if (authProcess != AuthProcess.idle || _isGoogleSigningIn) return;

    setState(() {
      _errorMessage = null;
      _isGoogleSigningIn = true; // Set local loading flag immediately
    });

    try {
      dev.log('SignupScreen: Starting Google Sign-In');

      final result = await authService.signInWithGoogle();

      if (!mounted) return;

      if (result.success) {
        // Check if this is a new user (for Google sign-in)
        if (result.isNewUser) {
          // Mark as new signup for welcome subscription screen
          ref.read(core_auth.isNewSignupProvider.notifier).state = true;

          // Navigate to welcome subscription screen for new Google users
          context.go('/welcome-subscription');
        } else {
          // Existing Google user, go to dashboard
          context.go('/dashboard');
        }
        // Keep the loading state active as we navigate away
      } else {
        // Display error message and reset loading state
        _handleAuthError('Google Sign-In failed', result.errorMessage, null);
        setState(() => _isGoogleSigningIn = false);
      }
    } catch (e) {
      if (mounted) {
        _handleAuthError('Unexpected error during Google Sign-In',
            'An unexpected error occurred. Please try again.', e);
        setState(() => _isGoogleSigningIn = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final l10n = S.of(context);
    final authProcess = ref.watch(core_auth.authProcessProvider);
    final isLoading = authProcess != AuthProcess.idle || _isGoogleSigningIn;

    return Scaffold(
      backgroundColor: context.containerBackgroundColor,
      body: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              context.containerBackgroundColor,
              context.secondaryAccentColor.withOpacity(0.8),
            ],
          ),
        ),
        child: SafeArea(
          child: LayoutBuilder(
            builder: (context, constraints) {
              return SingleChildScrollView(
                child: ConstrainedBox(
                  constraints: BoxConstraints(
                    minHeight: constraints.maxHeight,
                  ),
                  child: IntrinsicHeight(
                    child: Padding(
                      padding: const EdgeInsets.all(24.0),
                      child: Column(
                        mainAxisSize: MainAxisSize.max,
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        children: [
                          const SizedBox(height: 32),
                          Align(
                            alignment: Alignment.center,
                            child: Container(
                              height: 100,
                              width: 100,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                border: Border.all(
                                  color: context.accentColor,
                                  width: 3,
                                ),
                              ),
                              child: Icon(
                                Icons.local_gas_station,
                                size: 50,
                                color: context.accentColor,
                              ),
                            ),
                          ),
                          const SizedBox(height: 32),
                          Text(
                            l10n.signup,
                            style: TextStyle(
                              fontSize: 32,
                              fontWeight: FontWeight.bold,
                              color: context.accentColor,
                            ),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 32),

                          // Error message if present
                          if (_errorMessage != null)
                            Container(
                              padding: const EdgeInsets.all(12),
                              decoration: BoxDecoration(
                                color: Colors.red.withOpacity(0.1),
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(color: Colors.red.shade300),
                              ),
                              child: Text(
                                _errorMessage!,
                                style: TextStyle(color: Colors.red.shade300),
                                textAlign: TextAlign.center,
                              ),
                            ),
                          if (_errorMessage != null) const SizedBox(height: 24),

                          // Form
                          Expanded(
                            child: Form(
                              key: _formKey,
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.stretch,
                                children: [
                                  TextFormField(
                                    controller: _nameController,
                                    style: TextStyle(
                                        color: context.primaryTextColor),
                                    enabled: !isLoading,
                                    decoration: InputDecoration(
                                      labelText: l10n.name,
                                      labelStyle: TextStyle(
                                          color: context.secondaryTextColor),
                                      prefixIcon: Icon(Icons.person,
                                          color: context.accentColor),
                                      enabledBorder: OutlineInputBorder(
                                        borderSide: BorderSide(
                                            color: context.secondaryAccentColor
                                                .withOpacity(0.3)),
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                      focusedBorder: OutlineInputBorder(
                                        borderSide: BorderSide(
                                            color: context.accentColor),
                                        borderRadius: const BorderRadius.all(
                                            Radius.circular(12)),
                                      ),
                                      errorBorder: OutlineInputBorder(
                                        borderSide: BorderSide(
                                            color: Colors.red.shade300),
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                      focusedErrorBorder: OutlineInputBorder(
                                        borderSide: BorderSide(
                                            color: Colors.red.shade300),
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                    ),
                                    textInputAction: TextInputAction.next,
                                    validator: (value) {
                                      if (value == null ||
                                          value.trim().isEmpty) {
                                        return l10n.nameRequired;
                                      }
                                      return null;
                                    },
                                  ),
                                  const SizedBox(height: 16),
                                  TextFormField(
                                    controller: _emailController,
                                    style: TextStyle(
                                        color: context.primaryTextColor),
                                    enabled: !isLoading,
                                    decoration: InputDecoration(
                                      labelText: l10n.email,
                                      labelStyle: TextStyle(
                                          color: context.secondaryTextColor),
                                      prefixIcon: Icon(Icons.email,
                                          color: context.accentColor),
                                      enabledBorder: OutlineInputBorder(
                                        borderSide: BorderSide(
                                            color: context.secondaryAccentColor
                                                .withOpacity(0.3)),
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                      focusedBorder: OutlineInputBorder(
                                        borderSide: BorderSide(
                                            color: context.accentColor),
                                        borderRadius: const BorderRadius.all(
                                            Radius.circular(12)),
                                      ),
                                      errorBorder: OutlineInputBorder(
                                        borderSide: BorderSide(
                                            color: Colors.red.shade300),
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                      focusedErrorBorder: OutlineInputBorder(
                                        borderSide: BorderSide(
                                            color: Colors.red.shade300),
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                    ),
                                    keyboardType: TextInputType.emailAddress,
                                    textInputAction: TextInputAction.next,
                                    validator: FormValidators.validateEmail,
                                  ),
                                  const SizedBox(height: 16),
                                  TextFormField(
                                    controller: _passwordController,
                                    style: TextStyle(
                                        color: context.primaryTextColor),
                                    enabled: !isLoading,
                                    decoration: InputDecoration(
                                      labelText: l10n.password,
                                      labelStyle: TextStyle(
                                          color: context.secondaryTextColor),
                                      prefixIcon: Icon(Icons.lock,
                                          color: context.accentColor),
                                      suffixIcon: IconButton(
                                        icon: Icon(
                                          _obscurePassword
                                              ? Icons.visibility
                                              : Icons.visibility_off,
                                          color: context.secondaryTextColor,
                                        ),
                                        onPressed: () {
                                          setState(() => _obscurePassword =
                                              !_obscurePassword);
                                        },
                                      ),
                                      enabledBorder: OutlineInputBorder(
                                        borderSide: BorderSide(
                                            color: context.secondaryAccentColor
                                                .withOpacity(0.3)),
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                      focusedBorder: OutlineInputBorder(
                                        borderSide: BorderSide(
                                            color: context.accentColor),
                                        borderRadius: const BorderRadius.all(
                                            Radius.circular(12)),
                                      ),
                                      errorBorder: OutlineInputBorder(
                                        borderSide: BorderSide(
                                            color: Colors.red.shade300),
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                      focusedErrorBorder: OutlineInputBorder(
                                        borderSide: BorderSide(
                                            color: Colors.red.shade300),
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                    ),
                                    obscureText: _obscurePassword,
                                    textInputAction: TextInputAction.next,
                                    validator: FormValidators.validatePassword,
                                  ),
                                  const SizedBox(height: 16),
                                  TextFormField(
                                    controller: _confirmPasswordController,
                                    style: TextStyle(
                                        color: context.primaryTextColor),
                                    enabled: !isLoading,
                                    decoration: InputDecoration(
                                      labelText: l10n.confirmPassword,
                                      labelStyle: TextStyle(
                                          color: context.secondaryTextColor),
                                      prefixIcon: Icon(Icons.lock,
                                          color: context.accentColor),
                                      suffixIcon: IconButton(
                                        icon: Icon(
                                          _obscureConfirmPassword
                                              ? Icons.visibility
                                              : Icons.visibility_off,
                                          color: context.secondaryTextColor,
                                        ),
                                        onPressed: () {
                                          setState(() =>
                                              _obscureConfirmPassword =
                                                  !_obscureConfirmPassword);
                                        },
                                      ),
                                      enabledBorder: OutlineInputBorder(
                                        borderSide: BorderSide(
                                            color: context.secondaryAccentColor
                                                .withOpacity(0.3)),
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                      focusedBorder: OutlineInputBorder(
                                        borderSide: BorderSide(
                                            color: context.accentColor),
                                        borderRadius: const BorderRadius.all(
                                            Radius.circular(12)),
                                      ),
                                      errorBorder: OutlineInputBorder(
                                        borderSide: BorderSide(
                                            color: Colors.red.shade300),
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                      focusedErrorBorder: OutlineInputBorder(
                                        borderSide: BorderSide(
                                            color: Colors.red.shade300),
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                    ),
                                    obscureText: _obscureConfirmPassword,
                                    validator: (value) =>
                                        FormValidators.validateConfirmPassword(
                                      value,
                                      _passwordController.text,
                                    ),
                                  ),
                                  const SizedBox(height: 32),
                                  ElevatedButton(
                                    onPressed: isLoading ? null : _handleSignup,
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: context.accentColor,
                                      foregroundColor: context.isDarkMode
                                          ? Colors.black
                                          : Colors.white,
                                      minimumSize:
                                          const Size(double.infinity, 56),
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                      elevation: 4,
                                    ),
                                    child: isLoading &&
                                            authProcess ==
                                                AuthProcess.emailSignUp
                                        ? const SizedBox(
                                            height: 24,
                                            width: 24,
                                            child: CircularProgressIndicator(
                                              strokeWidth: 2,
                                              valueColor:
                                                  AlwaysStoppedAnimation<Color>(
                                                      Colors.black),
                                            ),
                                          )
                                        : Text(
                                            l10n.signup,
                                            style: const TextStyle(
                                              fontSize: 18,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                  ),
                                  const SizedBox(height: 16),
                                  Row(
                                    children: [
                                      Expanded(
                                        child: Divider(
                                          color: Colors.white.withOpacity(0.3),
                                          thickness: 1,
                                        ),
                                      ),
                                      Padding(
                                        padding: const EdgeInsets.symmetric(
                                            horizontal: 16),
                                        child: Text(
                                          l10n.orContinueWith,
                                          style: TextStyle(
                                            color:
                                                Colors.white.withOpacity(0.7),
                                          ),
                                        ),
                                      ),
                                      Expanded(
                                        child: Divider(
                                          color: Colors.white.withOpacity(0.3),
                                          thickness: 1,
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 16),
                                  OutlinedButton.icon(
                                    onPressed:
                                        isLoading ? null : _handleGoogleSignIn,
                                    style: OutlinedButton.styleFrom(
                                      foregroundColor: context.accentColor,
                                      minimumSize:
                                          const Size(double.infinity, 56),
                                      side: BorderSide(
                                          color: context.accentColor, width: 2),
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                    ),
                                    icon: _isGoogleSigningIn
                                        ? SizedBox(
                                            height: 24,
                                            width: 24,
                                            child: CircularProgressIndicator(
                                              strokeWidth: 2,
                                              valueColor:
                                                  AlwaysStoppedAnimation<Color>(
                                                      context.accentColor),
                                            ),
                                          )
                                        : Image.asset(
                                            'assets/images/google_logo.png',
                                            height: 24,
                                          ),
                                    label: Text(
                                      _isGoogleSigningIn
                                          ? 'Signing in...'
                                          : l10n.signInWithGoogle,
                                      style: const TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ),
                                  const SizedBox(height: 24),
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Text(
                                        l10n.alreadyHaveAccount,
                                        style: TextStyle(
                                          color: Colors.white.withOpacity(0.7),
                                        ),
                                      ),
                                      TextButton(
                                        onPressed: isLoading
                                            ? null
                                            : () => context.push('/login'),
                                        style: TextButton.styleFrom(
                                          foregroundColor: context.accentColor,
                                        ),
                                        child: Text(l10n.login),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ),
    );
  }
}
