import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:oil_change_tracker/core/theme/theme_extensions.dart';
import 'package:oil_change_tracker/services/firebase_functions_service.dart';
import 'package:oil_change_tracker/services/notification_service.dart';
import '../../../../generated/app_localizations.dart';

/// A widget for testing the oil_notify Firebase Functions
class OilNotifyTestWidget extends ConsumerWidget {
  const OilNotifyTestWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final notificationService = ref.watch(notificationServiceProvider);
    final firebaseFunctionsService = ref.watch(firebaseFunctionsServiceProvider);
    final l10n = S.of(context);

    return Card(
      margin: const EdgeInsets.symmetric(vertical: 8.0),
      color: context.containerBackgroundColor,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.oil_barrel,
                  color: context.accentColor,
                ),
                const SizedBox(width: 8),
                Text(
                  'Oil Notify Functions',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: context.primaryTextColor,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              'Test oil change notification delivery with these functions',
              style: TextStyle(
                fontSize: 14,
                color: context.secondaryTextColor,
              ),
            ),
            const SizedBox(height: 16),
            
            // Buttons
            Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                ElevatedButton(
                  onPressed: () => _sendOilChangeNotification(
                    context: context,
                    notificationService: notificationService,
                    firebaseFunctionsService: firebaseFunctionsService,
                    l10n: l10n,
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: context.secondaryAccentColor.withOpacity(0.8),
                    foregroundColor: context.primaryTextColor,
                  ),
                  child: Text(l10n.recordOilChange),
                ),
                const SizedBox(height: 12),
                ElevatedButton(
                  onPressed: () => _triggerScheduledReminders(
                    context: context,
                    firebaseFunctionsService: firebaseFunctionsService,
                    l10n: l10n,
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.orange.withOpacity(0.8),
                    foregroundColor: context.primaryTextColor,
                  ),
                  child: Text(l10n.reminders),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
  
  Future<void> _sendOilChangeNotification({
    required BuildContext context,
    required NotificationService notificationService,
    required FirebaseFunctionsService firebaseFunctionsService,
    required S l10n,
  }) async {
    final TextEditingController carNameController = TextEditingController(text: 'My Test Car');
    final TextEditingController dueDateController = TextEditingController(
      text: DateFormat('MM/dd/yyyy').format(DateTime.now().add(const Duration(days: 7))),
    );
    
    // Show dialog to get car details
    await showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: context.containerBackgroundColor,
        title: Text(
          'Test Oil Change Notification',
          style: TextStyle(color: context.primaryTextColor),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: carNameController,
              decoration: InputDecoration(
                labelText: 'Car Name',
                hintText: 'Enter car name',
                labelStyle: TextStyle(color: context.secondaryTextColor),
                enabledBorder: UnderlineInputBorder(
                  borderSide: BorderSide(color: context.borderColor),
                ),
              ),
              style: TextStyle(color: context.primaryTextColor),
            ),
            const SizedBox(height: 12),
            TextField(
              controller: dueDateController,
              decoration: InputDecoration(
                labelText: 'Due Date',
                hintText: 'MM/DD/YYYY',
                labelStyle: TextStyle(color: context.secondaryTextColor),
                enabledBorder: UnderlineInputBorder(
                  borderSide: BorderSide(color: context.borderColor),
                ),
              ),
              style: TextStyle(color: context.primaryTextColor),
              keyboardType: TextInputType.datetime,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              'Cancel',
              style: TextStyle(color: context.secondaryTextColor),
            ),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: context.secondaryAccentColor,
            ),
            child: Text('Send'),
          ),
        ],
      ),
    ).then((send) async {
      if (send == true) {
        // Get FCM token
        final token = await notificationService.getFCMToken();
        
        if (token == null) {
          if (context.mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text(l10n.errorFcmToken)),
            );
          }
          return;
        }
        
        // Show sending indicator
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(l10n.loading)),
          );
        }
        
        // Send notification via Firebase Function
        final success = await firebaseFunctionsService.sendOilChangeNotification(
          token: token,
          carName: carNameController.text,
          dueDate: dueDateController.text,
        );
        
        // Show result
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                success
                    ? l10n.oilChangeNotificationSentSuccessfully
                    : l10n.failedToSendOilChangeNotification,
              ),
              backgroundColor: success ? Colors.green : Colors.red,
            ),
          );
        }
      }
    });
  }
  
  Future<void> _triggerScheduledReminders({
    required BuildContext context,
    required FirebaseFunctionsService firebaseFunctionsService,
    required S l10n,
  }) async {
    // Show confirmation dialog
    final confirm = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: context.containerBackgroundColor,
        title: Text(
          'Trigger Scheduled Reminders',
          style: TextStyle(color: context.primaryTextColor),
        ),
        content: Text(
          'This will check for cars due for oil changes in the next 2 weeks and send notifications to their owners if enabled in their settings. Continue?',
          style: TextStyle(color: context.secondaryTextColor),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text(
              'Cancel',
              style: TextStyle(color: context.secondaryTextColor),
            ),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange,
            ),
            child: const Text('Trigger'),
          ),
        ],
      ),
    );
    
    if (confirm == true) {
      // Show sending indicator
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Triggering scheduled reminders...'),
          ),
        );
      }
      
      // Call Firebase Function
      final success = await firebaseFunctionsService.triggerScheduledReminders();
      
      // Show result
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              success
                  ? l10n.scheduledRemindersTriggeredSuccessfully
                  : l10n.failedToTriggerScheduledReminders,
            ),
            backgroundColor: success ? Colors.green : Colors.red,
          ),
        );
      }
    }
  }
} 