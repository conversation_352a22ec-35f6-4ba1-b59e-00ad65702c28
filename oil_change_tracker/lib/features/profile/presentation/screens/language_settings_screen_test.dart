import 'package:flutter/material.dart';
import '../../../../generated/app_localizations.dart';

class LanguageSettingsScreenTest extends StatefulWidget {
  @override
  _LanguageSettingsScreenTestState createState() => _LanguageSettingsScreenTestState();
}

class _LanguageSettingsScreenTestState extends State<LanguageSettingsScreenTest> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(S.of(context).language),
      ),
    );
  }
} 