import 'package:flutter/material.dart';
import '../../../../generated/app_localizations.dart';

class NotificationSettingsScreenTest extends StatefulWidget {
  @override
  _NotificationSettingsScreenTestState createState() => _NotificationSettingsScreenTestState();
}

class _NotificationSettingsScreenTestState extends State<NotificationSettingsScreenTest> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(S.of(context).notificationSettings),
      ),
    );
  }
} 