import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:settings_ui/settings_ui.dart';
import '../../../../core/theme/theme_extensions.dart';
import '../../../../generated/app_localizations.dart';
import '../../../../core/services/notification_service.dart';
import '../../providers/user_settings_provider.dart';
import '../widgets/oil_notify_test_widget.dart';

class NotificationSettingsScreen extends ConsumerWidget {
  const NotificationSettingsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final userSettingsAsync = ref.watch(userSettingsNotifierProvider);
    final l10n = S.of(context);

    return Scaffold(
      backgroundColor: context.containerBackgroundColor,
      appBar: AppBar(
        backgroundColor: context.containerBackgroundColor,
        title: Text(
          l10n.notificationSettings,
          style: TextStyle(color: context.accentColor),
        ),
      ),
      body: userSettingsAsync.when(
        loading: () => Center(child: CircularProgressIndicator(color: context.accentColor)),
        error: (error, stack) => Center(
          child: Text(
            'Error loading settings: $error',
            style: TextStyle(color: context.primaryTextColor),
          ),
        ),
        data: (userSettings) {
          if (userSettings == null) {
            return Center(
              child: Text(
                'No settings found',
                style: TextStyle(color: context.primaryTextColor),
              ),
            );
          }
          
          return SettingsList(
            lightTheme: SettingsThemeData(
              settingsListBackground: context.containerBackgroundColor,
              settingsSectionBackground: context.containerBackgroundColor,
              titleTextColor: context.accentColor,
              dividerColor: context.secondaryAccentColor,
            ),
            darkTheme: SettingsThemeData(
              settingsListBackground: context.containerBackgroundColor,
              settingsSectionBackground: context.containerBackgroundColor,
              titleTextColor: context.accentColor,
              dividerColor: context.secondaryAccentColor,
            ),
            sections: [
              SettingsSection(
                title: Text(l10n.reminders),
                tiles: [
                  SettingsTile.switchTile(
                    title: Text(
                      l10n.oilChangeReminders,
                      style: TextStyle(color: context.primaryTextColor),
                    ),
                    description: Text(
                      l10n.oilChangeRemindersDesc,
                      style: TextStyle(color: context.secondaryTextColor),
                    ),
                    leading: Icon(
                      Icons.oil_barrel,
                      color: context.accentColor,
                    ),
                    initialValue: userSettings.oilChangeReminders,
                    onToggle: (value) {
                      ref.read(userSettingsNotifierProvider.notifier).toggleOilChangeReminders();
                    },
                    activeSwitchColor: context.secondaryAccentColor,
                  ),
                  SettingsTile.switchTile(
                    title: Text(
                      l10n.maintenanceReminders,
                      style: TextStyle(color: context.primaryTextColor),
                    ),
                    description: Text(
                      l10n.maintenanceRemindersDesc,
                      style: TextStyle(color: context.secondaryTextColor),
                    ),
                    leading: Icon(
                      Icons.build,
                      color: context.accentColor,
                    ),
                    initialValue: userSettings.maintenanceReminders,
                    onToggle: (value) {
                      ref.read(userSettingsNotifierProvider.notifier).toggleMaintenanceReminders();
                    },
                    activeSwitchColor: context.secondaryAccentColor,
                  ),
                  SettingsTile.switchTile(
                    title: Text(
                      l10n.mileageReminders,
                      style: TextStyle(color: context.primaryTextColor),
                    ),
                    description: Text(
                      l10n.mileageRemindersDesc,
                      style: TextStyle(color: context.secondaryTextColor),
                    ),
                    leading: Icon(
                      Icons.speed,
                      color: context.accentColor,
                    ),
                    initialValue: userSettings.mileageReminders,
                    onToggle: (value) {
                      ref.read(userSettingsNotifierProvider.notifier).toggleMileageReminders();
                    },
                    activeSwitchColor: context.secondaryAccentColor,
                  ),
                ],
              ),
              // Developer settings section - This should be hidden in production
              if (l10n.language == 'English')
                SettingsSection(
                  title: const Text('Developer Settings'),
                  tiles: [
                    SettingsTile(
                      title: Text(
                        'Firebase Cloud Messaging Token',
                        style: TextStyle(color: context.primaryTextColor),
                      ),
                      description: Text(
                        'Test and debug notification tokens',
                        style: TextStyle(color: context.secondaryTextColor),
                      ),
                      leading: Icon(
                        Icons.developer_mode,
                        color: context.accentColor,
                      ),
                      trailing: ElevatedButton.icon(
                        onPressed: () async {
                          // Get the NotificationService and manually refresh token
                          final notificationService = ref.read(notificationServiceProvider);
                          final token = await notificationService.getAndSaveFCMToken();
                          
                          // Show result
                          if (token != null) {
                            if (context.mounted) {
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  content: Text(l10n.tokenRefreshed(token.substring(0, 10))),
                                  duration: const Duration(seconds: 5),
                                ),
                              );
                            }
                          } else {
                            if (context.mounted) {
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  content: Text(l10n.failedToRefreshToken),
                                  backgroundColor: context.secondaryAccentColor,
                                  duration: const Duration(seconds: 3),
                                ),
                              );
                            }
                          }
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: context.accentColor,
                          foregroundColor: context.containerBackgroundColor,
                        ),
                        icon: const Icon(Icons.refresh),
                        label: Text(l10n.refreshToken),
                      ),
                    ),
                    SettingsTile(
                      title: Text(
                        'Oil Notify Test',
                        style: TextStyle(color: context.primaryTextColor),
                      ),
                      description: Text(
                        'Test the oil_notify Firebase Function',
                        style: TextStyle(color: context.secondaryTextColor),
                      ),
                      leading: Icon(
                        Icons.notifications_active,
                        color: context.accentColor,
                      ),
                    ),
                  ],
                ),
            ],
          );
        },
      ),
      bottomSheet: l10n.language == 'English'
          ? const Padding(
              padding: EdgeInsets.all(16.0),
              child: OilNotifyTestWidget(),
            )
          : null,
    );
  }
} 