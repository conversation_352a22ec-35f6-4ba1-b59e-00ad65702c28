import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/theme/theme_extensions.dart';
import '../../../../generated/app_localizations.dart';
import '../../../../core/providers/locale_provider.dart';

class LanguageSettingsScreen extends ConsumerWidget {
  const LanguageSettingsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final l10n = S.of(context);
    final currentLocale = ref.watch(localeProvider);

    return Scaffold(
      backgroundColor: context.containerBackgroundColor,
      appBar: AppBar(
        title: Text(
          l10n.language,
          style: TextStyle(
            color: context.accentColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: context.containerBackgroundColor,
        elevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: context.accentColor),
          onPressed: () => context.pop(),
        ),
      ),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          _buildLanguageOption(
            context,
            title: l10n.english,
            isSelected: currentLocale.languageCode == 'en',
            onTap: () {
              ref.read(localeProvider.notifier).setLocale(const Locale('en'));
              context.pop();
            },
          ),
          const SizedBox(height: 8),
          _buildLanguageOption(
            context,
            title: l10n.arabic,
            isSelected: currentLocale.languageCode == 'ar',
            onTap: () {
              ref.read(localeProvider.notifier).setLocale(const Locale('ar'));
              context.pop();
            },
          ),
        ],
      ),
    );
  }

  Widget _buildLanguageOption(
    BuildContext context, {
    required String title,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: context.containerBackgroundColor,
        border: Border.all(color: context.secondaryAccentColor.withOpacity(0.3)),
        borderRadius: BorderRadius.circular(12),
      ),
      child: ListTile(
        title: Text(
          title,
          style: TextStyle(
            color: context.primaryTextColor,
            fontWeight: FontWeight.w500,
          ),
        ),
        trailing: isSelected
            ? Icon(Icons.check_circle, color: context.accentColor)
            : Icon(Icons.circle_outlined, color: context.accentColor.withOpacity(0.7)),
        onTap: onTap,
      ),
    );
  }
} 