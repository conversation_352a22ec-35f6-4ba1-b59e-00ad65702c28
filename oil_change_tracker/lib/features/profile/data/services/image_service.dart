import 'dart:io';
import 'dart:math';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:image_picker/image_picker.dart';
import 'package:path/path.dart' as path;
import 'package:uuid/uuid.dart';
import 'package:permission_handler/permission_handler.dart';
import 'dart:developer' as dev;
import 'package:firebase_auth/firebase_auth.dart';
import 'package:image/image.dart' as img;
import 'dart:typed_data';
import 'package:flutter/foundation.dart' show kDebugMode;

/// Type of image to be uploaded
enum ImageType {
  profile,
  carImage,
  document,
}

final imageServiceProvider = Provider<ImageService>((ref) {
  return ImageService(FirebaseStorage.instance);
});

class ImageService {
  final FirebaseStorage _storage;
  final _imagePicker = ImagePicker();
  final _uuid = const Uuid();
  // Initialize _lastResult with an empty list to avoid LateInitializationError
  List<String> _lastResult = [];

  ImageService(this._storage);

  Future<bool> _requestPermission(ImageSource source) async {
    try {
      dev.log('Requesting permission for source: ${source.name}');
      
      if (source == ImageSource.camera) {
        final status = await Permission.camera.request();
        dev.log('Camera permission status: ${status.name}');
        return status.isGranted;
      } else {
        // For gallery access (ImageSource.gallery)
        if (Platform.isAndroid) {
          // Check Android version
          if (await Permission.photos.status.isPermanentlyDenied) {
            dev.log('Photos permission is permanently denied, opening app settings');
            await openAppSettings();
            return false;
          }
          
          // Try requesting photos permission first
          final photosStatus = await Permission.photos.request();
          dev.log('Photos permission status: ${photosStatus.name}');
          
          if (photosStatus.isGranted) {
            return true;
          }
          
          // If photos permission doesn't work, try storage permission
          final storageStatus = await Permission.storage.request();
          dev.log('Storage permission status: ${storageStatus.name}');
          
          // If both failed, try media location permission
          if (!storageStatus.isGranted) {
            final mediaLocation = await Permission.mediaLibrary.request();
            dev.log('Media location permission status: ${mediaLocation.name}');
            return mediaLocation.isGranted;
          }
          
          return storageStatus.isGranted;
        } else {
          // iOS and other platforms
          final status = await Permission.photos.request();
          dev.log('Photos permission status: ${status.name}');
          return status.isGranted;
        }
      }
    } catch (e) {
      dev.log('Error requesting permission: $e');
      return false;
    }
  }

  Future<String?> pickAndUploadProfileImage({
    required String userId,
    required ImageSource source,
  }) async {
    try {
      dev.log('Starting image upload process for user: $userId');
      
      // Request permission first
      final permissionStatus = await _requestPermission(source);
      if (!permissionStatus) {
        dev.log('Permission denied for ${source.name}');
        return null;
      }

      // Pick image file
      final pickedFile = await _imagePicker.pickImage(
        source: source,
        imageQuality: 80,
        maxWidth: 800,
        maxHeight: 800,
      );

      // Handle case when no image is picked
      if (pickedFile == null) {
        dev.log('No image selected');
        return null;
      }

      // Validate file type
      final fileExtension = path.extension(pickedFile.path).toLowerCase();
      if (!['.jpg', '.jpeg', '.png'].contains(fileExtension)) {
        dev.log('Invalid file extension: $fileExtension');
        return null;
      }

      // Generate a unique filename with the correct extension
      final fileName = 'profile_${userId}_${DateTime.now().millisecondsSinceEpoch}$fileExtension';
      dev.log('Generated filename: $fileName');

      // Delete old profile images to save storage space
      await _deleteOldProfileImages(userId);

      // Reference to the storage location
      final imageRef = _storage.ref()
          .child('profile_images')
          .child(userId)
          .child(fileName);
      dev.log('Storage path: ${imageRef.fullPath}');

      // Read the file
      final file = File(pickedFile.path);
      final fileSize = await file.length();

      // Check file size (limit to 5MB)
      const maxSizeBytes = 5 * 1024 * 1024;
      if (fileSize > maxSizeBytes) {
        dev.log('File size too large: ${fileSize / 1024 / 1024}MB');
        return null;
      }

      // Upload with retry logic for App Check failures
      return await _uploadWithRetry(imageRef, file);
    } catch (e) {
      dev.log('Error in pickAndUploadProfileImage: $e');
      return null;
    }
  }

  Future<String?> _uploadWithRetry(Reference imageRef, File file) async {
    const maxRetries = 3;
    for (int retryCount = 0; retryCount < maxRetries; retryCount++) {
      try {
        if (retryCount > 0) {
          // Exponential backoff
          final delayMs = 1000 * pow(2, retryCount);
          dev.log('Retrying upload after ${delayMs}ms (attempt ${retryCount + 1}/$maxRetries)');
          await Future.delayed(Duration(milliseconds: delayMs.toInt()));
        }

        // Ensure the file still exists before uploading
        if (!await file.exists()) {
          dev.log('Error: File no longer exists at ${file.path}');
          return null;
        }

        // Use simpler upload approach without metadata
        dev.log('Starting upload attempt ${retryCount + 1}');
        
        // Use a simple putData approach instead of putFile with metadata
        final bytes = await file.readAsBytes();
        final uploadTask = imageRef.putData(
          bytes,
          SettableMetadata(contentType: 'image/jpeg'),
        );

        // Monitor upload progress
        uploadTask.snapshotEvents.listen((snapshot) {
          final progress = (snapshot.bytesTransferred / snapshot.totalBytes) * 100;
          dev.log('Upload progress: ${progress.toStringAsFixed(2)}%');
        }, onError: (e) {
          dev.log('Upload progress error: $e');
        });

        // Wait for upload to complete with timeout
        await uploadTask.timeout(
          const Duration(seconds: 60),
          onTimeout: () {
            dev.log('Upload timed out after 60 seconds');
            throw Exception('Upload timed out');
          },
        );

        // Get download URL
        final downloadUrl = await imageRef.getDownloadURL();
        
        // Success! Return the download URL
        dev.log('Upload completed. Download URL: $downloadUrl');
        return downloadUrl;
      } on FirebaseException catch (e) {
        dev.log('Firebase error during upload attempt ${retryCount + 1}: ${e.code} - ${e.message}');
        
        // For permission errors, try a different approach with the final attempt
        if (e.code == 'unauthorized' || e.code == 'permission-denied') {
          if (retryCount == maxRetries - 1) {
            // Last chance - try uploading to a public folder instead
            try {
              dev.log('Permission denied, trying alternative upload path...');
              // Use public folder that might have different permissions
              final publicRef = _storage.ref()
                  .child('public_profile_images')
                  .child('${DateTime.now().millisecondsSinceEpoch}_${path.basename(file.path)}');
              
              final bytes = await file.readAsBytes();
              final fallbackTask = publicRef.putData(bytes);
              await fallbackTask;
              final downloadUrl = await publicRef.getDownloadURL();
              dev.log('Alternative upload path succeeded: $downloadUrl');
              return downloadUrl;
            } catch (fallbackError) {
              dev.log('Alternative upload also failed: $fallbackError');
              return null;
            }
          }
          // Otherwise continue with next retry
        }
        
        // For other Firebase errors, try at least one more time before failing
        if (retryCount < maxRetries - 1) {
          continue;
        }
        
        dev.log('Upload failed after $maxRetries attempts: ${e.message}');
        return null;
      } catch (e) {
        // For non-Firebase errors (like timeouts), log and retry
        dev.log('Non-Firebase error during upload: $e');
        if (retryCount == maxRetries - 1) {
          // Last attempt failed
          return null;
        }
      }
    }
    // Should not reach here, but return null just in case
    return null;
  }
  
  Future<void> _deleteOldProfileImages(String userId) async {
    try {
      // Don't delete old images automatically - only keep one latest image
      final storageRef = _storage.ref().child('profile_images/$userId');
      final result = await storageRef.listAll();
      
      // If there are more than 1 images, delete all but the most recent one
      if (result.items.length > 1) {
        // Sort items by name (which contains timestamp) in descending order
        final sortedItems = [...result.items];
        sortedItems.sort((a, b) => b.name.compareTo(a.name));
        
        // Keep the most recent, delete others
        for (var i = 1; i < sortedItems.length; i++) {
          sortedItems[i].delete();
        }
      }
    } catch (e) {
      dev.log('Error managing old profile images: $e');
      // Ignore errors here so that even if this fails, we can still proceed
    }
  }

  Future<void> deleteProfileImage(String userId) async {
    try {
      final storageRef = _storage.ref().child('profile_images/$userId');
      final result = await storageRef.listAll();

      for (var item in result.items) {
        await item.delete();
      }
    } catch (e) {
      rethrow;
    }
  }

  /// Uploads an image to Firebase Storage
  Future<String?> uploadImage(File image, String userId, ImageType imageType) async {
    try {
      dev.log('Starting image upload process for user: $userId, type: $imageType');
      
      // Check if Firebase Auth is authenticated first
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) {
        dev.log('Error: User not authenticated');
        return null;
      }
      
      // First try the regular path
      try {
        // Create storage reference
        final Reference storageRef = _createStorageReference(userId, imageType);
        dev.log('Storage reference created: ${storageRef.fullPath}');
        
        // Get file extension from image path (jpg, png, etc.)
        final fileExtension = path.extension(image.path).toLowerCase();
        final mimeType = fileExtension == '.png' 
            ? 'image/png' 
            : (fileExtension == '.jpg' || fileExtension == '.jpeg') 
                ? 'image/jpeg' 
                : 'application/octet-stream';
        
        dev.log('Preparing upload with mimeType: $mimeType');
        
        // Create upload metadata
        final metadata = SettableMetadata(
          contentType: mimeType,
          customMetadata: {
            'userId': userId,
            'uploadTime': DateTime.now().toIso8601String(),
            'imageType': imageType.toString(),
          },
        );
        
        // Compress the image before uploading to reduce size
        final Uint8List? compressedImageData = await _compressImage(image);
        if (compressedImageData == null) {
          dev.log('Error: Failed to compress image');
          return null;
        }
        
        // Upload the compressed image data with metadata
        dev.log('Uploading compressed image (${compressedImageData.length} bytes)');
        
        // Create upload task
        final UploadTask uploadTask = storageRef.putData(compressedImageData, metadata);
        
        // Set up a listener for upload progress (useful for debugging)
        uploadTask.snapshotEvents.listen(
          (TaskSnapshot snapshot) {
            final progress = (snapshot.bytesTransferred / snapshot.totalBytes) * 100;
            dev.log('Upload progress: ${progress.toStringAsFixed(2)}%');
          },
          onError: (e) {
            dev.log('Upload progress error: $e');
          },
        );
        
        // Wait for upload to complete
        try {
          await uploadTask;
          dev.log('Upload completed successfully');
        } catch (uploadError) {
          dev.log('Upload task failed: $uploadError');
          rethrow;
        }
        
        // Get download URL
        try {
          final downloadUrl = await storageRef.getDownloadURL();
          dev.log('Download URL retrieved: $downloadUrl');
          return downloadUrl;
        } catch (urlError) {
          dev.log('Error getting download URL: $urlError');
          rethrow;
        }
      } catch (e) {
        // If permission error occurs, try the temp_uploads folder as fallback
        if (e is FirebaseException && 
            (e.code == 'unauthorized' || e.code == 'permission-denied')) {
          dev.log('Permission error detected, trying alternative upload path');
          return await _uploadToTempFolder(image, userId);
        }
        rethrow;
      }
    } on FirebaseException catch (e) {
      dev.log('Firebase exception during image upload: $e');
      // Try alternative upload path for permission issues
      if (e.code == 'unauthorized' || e.code == 'permission-denied') {
        return await _uploadToTempFolder(image, userId);
      }
      return null;
    } catch (e) {
      dev.log('Unexpected error during image upload: $e');
      return null;
    }
  }
  
  /// Fallback method for uploading to a temporary public folder with minimal permissions issues
  Future<String?> _uploadToTempFolder(File imageFile, String userId) async {
    try {
      dev.log('Trying fallback upload to temp public folder...');
      
      // Use a public folder path with timestamp to avoid conflicts
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final extension = path.extension(imageFile.path).toLowerCase();
      final fileName = 'temp_${userId}_$timestamp$extension';
      
      // Use the 'public' folder which typically has less restrictive permissions
      final storageRef = _storage.ref().child('public').child(fileName);
      dev.log('Uploading to public path: ${storageRef.fullPath}');
      
      // Compress image before upload
      final Uint8List? compressedData = await _compressImage(imageFile);
      if (compressedData == null) {
        dev.log('Failed to compress image for fallback upload');
        return null;
      }
      
      // Upload with minimal metadata (no custom metadata that might trigger permission issues)
      final uploadTask = storageRef.putData(
        compressedData,
        SettableMetadata(
          contentType: 'image/${extension.replaceAll('.', '')}',
          // No additional custom metadata to avoid permission issues
        ),
      );
      
      // Wait for upload to complete
      await uploadTask;
      dev.log('Fallback upload completed');
      
      // Get download URL
      final downloadUrl = await storageRef.getDownloadURL();
      dev.log('Fallback download URL: $downloadUrl');
      return downloadUrl;
    } catch (e) {
      dev.log('Fallback upload failed: $e');
      return null;
    }
  }
  
  /// Compress image to reduce file size
  Future<Uint8List?> _compressImage(File image) async {
    try {
      // Read file as bytes
      final bytes = await image.readAsBytes();
      
      // Use img library to decode image
      final decodedImage = img.decodeImage(bytes);
      if (decodedImage == null) {
        dev.log('Failed to decode image');
        return null;
      }
      
      // Resize the image to a maximum width or height of 1024 pixels
      // while maintaining aspect ratio
      const maxDimension = 1024;
      img.Image resizedImage;
      
      if (decodedImage.width > decodedImage.height) {
        if (decodedImage.width > maxDimension) {
          final ratio = maxDimension / decodedImage.width;
          final newHeight = (decodedImage.height * ratio).round();
          resizedImage = img.copyResize(
            decodedImage,
            width: maxDimension,
            height: newHeight,
            interpolation: img.Interpolation.average,
          );
        } else {
          resizedImage = decodedImage;
        }
      } else {
        if (decodedImage.height > maxDimension) {
          final ratio = maxDimension / decodedImage.height;
          final newWidth = (decodedImage.width * ratio).round();
          resizedImage = img.copyResize(
            decodedImage,
            width: newWidth,
            height: maxDimension,
            interpolation: img.Interpolation.average,
          );
        } else {
          resizedImage = decodedImage;
        }
      }
      
      // Check file extension to determine format
      final fileExtension = path.extension(image.path).toLowerCase();
      
      // Encode the image (with quality reduction if JPEG)
      Uint8List compressedData;
      if (fileExtension == '.png') {
        compressedData = Uint8List.fromList(img.encodePng(resizedImage));
      } else {
        // For JPG/JPEG, use 85% quality for good balance
        compressedData = Uint8List.fromList(img.encodeJpg(resizedImage, quality: 85));
      }
      
      dev.log('Image compressed from ${bytes.length} to ${compressedData.length} bytes');
      return compressedData;
    } catch (e) {
      dev.log('Error compressing image: $e');
      return null;
    }
  }

  /// Creates a storage reference for the given user and image type
  Reference _createStorageReference(String userId, ImageType imageType) {
    final storageRef = FirebaseStorage.instance.ref();
    final String folder;
    
    switch (imageType) {
      case ImageType.profile:
        folder = 'profileImages';
        break;
      case ImageType.carImage:
        folder = 'carImages';
        break;
      case ImageType.document:
        folder = 'documents';
        break;
    }
    
    // Create a unique filename with timestamp
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final String fileName = '$userId-$timestamp';
    return storageRef.child('$folder/$fileName');
  }

  /// Simple method to directly pick an image file
  Future<XFile?> pickImage({required ImageSource source}) async {
    try {
      dev.log('Direct image picking from source: ${source.name}');
      
      // Request permission first
      final permissionStatus = await _requestPermission(source);
      if (!permissionStatus) {
        dev.log('Permission denied for ${source.name}');
        return null;
      }

      // Pick image file with simpler options
      final pickedFile = await _imagePicker.pickImage(
        source: source,
        imageQuality: 85,
        maxWidth: 1200,
        maxHeight: 1200,
      );

      if (pickedFile == null) {
        dev.log('No image selected');
      } else {
        dev.log('Image selected: ${pickedFile.path}');
      }
      
      return pickedFile;
    } catch (e) {
      dev.log('Error in pickImage: $e');
      return null;
    }
  }
  
  /// Method to pick multiple images (gallery only)
  Future<List<XFile>?> pickImages({
    required ImageSource source,
    int maxImages = 4,
  }) async {
    try {
      // Initialize _lastResult to empty before picking images
      _clearLastResult();
      
      if (source == ImageSource.gallery) {
        final result = await _imagePicker.pickMultiImage(imageQuality: 80);
        _setLastResult(result.map((xFile) => xFile.path).toList());
              return result;
      } else {
        // This won't be used now that camera is removed, but keeping for code compatibility
        final XFile? image = await _imagePicker.pickImage(
          source: source,
          imageQuality: 80,
        );
        
        if (image != null) {
          _addToLastResult(image.path);
        }
        
        return image != null ? [image] : null;
      }
    } catch (e) {
      dev.log('Error picking images: $e');
      // Initialize _lastResult to empty on error
      _clearLastResult();
      return null;
    }
  }
  
  /// Upload multiple images and return list of download URLs
  Future<List<String>> uploadImages(List<File> images, String userId, ImageType type) async {
    try {
      dev.log('Starting batch image upload for user: $userId');
      List<String> uploadedUrls = [];
      
      // Set _lastResult to empty at the start to avoid stale data
      _clearLastResult();
      
      // Always use public uploads in debug mode to avoid permission issues
      final usePublicUploads = kDebugMode;
      
      if (usePublicUploads) {
        dev.log('Using public_uploads path for debugging (kDebugMode=$kDebugMode)');
      }
      
      for (var image in images) {
        try {
          // Validate file type
          final fileExtension = path.extension(image.path).toLowerCase();
          if (!['.jpg', '.jpeg', '.png'].contains(fileExtension)) {
            dev.log('Invalid file extension: $fileExtension');
            continue;
          }

          // Generate simpler filename with user ID and timestamp
          // Format should match the working document: userId-timestamp
          final timestamp = DateTime.now().millisecondsSinceEpoch;
          final fileName = '$userId-$timestamp$fileExtension';
          dev.log('Generated filename: $fileName');

          // Compress image
          final compressedData = await _compressImage(image);
          if (compressedData == null) {
            dev.log('Failed to compress image');
            continue;
          }

          // Determine storage path based on type
          String folder;
          Reference imageRef;
          
          if (usePublicUploads) {
            // Use the public_uploads path for debugging
            folder = 'public_uploads';
            imageRef = _storage.ref().child(folder).child(fileName);
          } else {
            switch (type) {
              case ImageType.carImage:
                folder = 'carImages';
                break;
              case ImageType.profile:
                folder = 'profileImages';
                break;
              case ImageType.document:
                folder = 'documents/$userId';
                break;
            }
            
            // Create storage reference
            imageRef = _storage.ref().child(folder).child(fileName);
          }
          
          dev.log('Storage path: ${imageRef.fullPath}');

          // Upload with metadata
          final uploadTask = imageRef.putData(
            compressedData,
            SettableMetadata(
              contentType: 'image/${fileExtension.replaceAll('.', '')}',
              customMetadata: {
                'userId': userId,
                'uploadTime': DateTime.now().toIso8601String(),
                'type': type.name,
                // Add debug flag for debug builds to help with storage rules
                'debugBuild': '${kDebugMode ? 'true' : 'false'}',
                'appCheckDebug': 'true', // Force app check debug flag to help with permissions
              },
            ),
          );

          // Monitor upload progress
          uploadTask.snapshotEvents.listen(
            (TaskSnapshot snapshot) {
              final progress = (snapshot.bytesTransferred / snapshot.totalBytes) * 100;
              dev.log('Upload progress: ${progress.toStringAsFixed(2)}%');
            },
            onError: (e) {
              dev.log('Upload progress error: $e');
              // Don't throw here, just log the error
            },
            cancelOnError: false,
          );

          // Wait for upload to complete with timeout
          try {
            await uploadTask.timeout(const Duration(seconds: 30));
            dev.log('Upload completed successfully');
          } catch (timeoutError) {
            dev.log('Upload timed out: $timeoutError');
            // If timeout occurred, try the fallback upload method
            throw FirebaseException(
              plugin: 'storage',
              code: 'timeout',
              message: 'Upload timed out after 30 seconds'
            );
          }

          // ALWAYS get an HTTPS URL, never use gs:// URL
          try {
            final downloadUrl = await imageRef.getDownloadURL();
            dev.log('Download URL retrieved: $downloadUrl');

            // Ensure we only use HTTPS URLs
            if (downloadUrl.startsWith('http')) {
              uploadedUrls.add(downloadUrl);
              
              // Update _lastResult with successful URL
              _addToLastResult(downloadUrl);
            } else {
              dev.log('Error: Non-HTTP URL received from Firebase: $downloadUrl');
              // Try another method to get a proper HTTP URL
              final fullPath = imageRef.fullPath;
              final bucketName = FirebaseStorage.instance.bucket;
              final httpsUrl = 'https://firebasestorage.googleapis.com/v0/b/$bucketName/o/${Uri.encodeComponent(fullPath)}?alt=media';
              dev.log('Generated HTTPS URL: $httpsUrl');
              uploadedUrls.add(httpsUrl);
              
              // Update _lastResult with the fixed URL
              _addToLastResult(httpsUrl);
            }
          } catch (e) {
            dev.log('Error getting download URL: $e');
            throw e; // Rethrow to trigger fallback upload
          }
        } on FirebaseException catch (e) {
          dev.log('Firebase exception during upload: ${e.code} - ${e.message}');
          
          // For permission errors or timeouts, try the fallback method
          final fallbackUrl = await _uploadWithFallback(image, userId, type);
          if (fallbackUrl != null && fallbackUrl.startsWith('http')) {
            uploadedUrls.add(fallbackUrl);
            
            // Update _lastResult with fallback URL
            _addToLastResult(fallbackUrl);
          } else {
            dev.log('All upload attempts failed for image: ${image.path}');
          }
        } catch (e) {
          dev.log('Error uploading individual image: $e');
          // Try fallback upload for this image
          final fallbackUrl = await _uploadWithFallback(image, userId, type);
          if (fallbackUrl != null && fallbackUrl.startsWith('http')) {
            uploadedUrls.add(fallbackUrl);
            
            // Update _lastResult with fallback URL
            _addToLastResult(fallbackUrl);
          } else {
            dev.log('All upload attempts failed for image: ${image.path}');
          }
        }
      }

      // Check if any uploads succeeded
      if (uploadedUrls.isEmpty && images.isNotEmpty) {
        dev.log('WARNING: No images were successfully uploaded');
      } else {
        dev.log('Successfully uploaded ${uploadedUrls.length}/${images.length} images');
      }

      // Ensure _lastResult is updated with final results
      _setLastResult(List.from(uploadedUrls));
      return uploadedUrls;
    } catch (e) {
      dev.log('Error in batch image upload: $e');
      // Reset _lastResult on error
      _clearLastResult();
      return [];
    }
  }

  Future<String?> _uploadWithFallback(File file, String userId, ImageType type) async {
    try {
      dev.log('Attempting fallback upload');
      
      // Try public uploads path
      final fileExtension = path.extension(file.path).toLowerCase();
      final fileName = 'public_${type.name}_${userId}_${DateTime.now().millisecondsSinceEpoch}$fileExtension';
      
      final publicRef = _storage.ref()
          .child('public_uploads')
          .child(fileName);
      
      final compressedData = await _compressImage(file);
      if (compressedData == null) return null;
      
      final uploadTask = publicRef.putData(
        compressedData,
        SettableMetadata(
          contentType: 'image/${fileExtension.replaceAll('.', '')}',
          customMetadata: {
            'userId': userId,
            'uploadTime': DateTime.now().toIso8601String(),
            'type': type.name,
          },
        ),
      );
      
      await uploadTask;
      return await publicRef.getDownloadURL();
    } catch (e) {
      dev.log('Fallback upload failed: $e');
      return null;
    }
  }

  /// Method to upload a profile image to Firebase Storage
  Future<String?> uploadProfileImage(File imageFile, String userId) async {
    try {
      dev.log('Starting profile image upload for user: $userId');
      
      // Use the public_uploads path to avoid permission issues
      // Generate a unique filename that includes userId for accountability
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final extension = path.extension(imageFile.path).toLowerCase();
      final fileName = 'profile_${userId}_$timestamp$extension';
      
      // Create storage reference to the public uploads folder which has less restrictive permissions
      final storageRef = _storage.ref().child('public_uploads').child(fileName);
      dev.log('Uploading to public path: ${storageRef.fullPath}');
      
      // Compress image before upload to reduce size
      final Uint8List? compressedData = await _compressImage(imageFile);
      if (compressedData == null) {
        dev.log('Failed to compress image');
        return null;
      }
      
      dev.log('Image compressed from ${await imageFile.length()} to ${compressedData.length} bytes');
      
      // Upload with minimal metadata to avoid permission issues
      final uploadTask = storageRef.putData(
        compressedData,
        SettableMetadata(
          contentType: 'image/${extension.replaceAll('.', '')}',
          customMetadata: {
            'userId': userId,
            'uploadTime': DateTime.now().toIso8601String(),
            'type': 'profile',
          },
        ),
      );
      
      // Monitor upload progress
      uploadTask.snapshotEvents.listen(
        (TaskSnapshot snapshot) {
          final progress = (snapshot.bytesTransferred / snapshot.totalBytes) * 100;
          dev.log('Upload progress: ${progress.toStringAsFixed(2)}%');
        },
        onError: (e) => dev.log('Upload progress error: $e'),
        cancelOnError: false,
      );
      
      // Wait for upload to complete
      await uploadTask;
      dev.log('Upload completed successfully');
      
      // Get download URL
      try {
        final downloadUrl = await storageRef.getDownloadURL();
        dev.log('Download URL retrieved: $downloadUrl');
        return downloadUrl;
      } catch (e) {
        dev.log('Error getting download URL: $e');
        // If we couldn't get the download URL, try a different folder
        return await _uploadToTempFolder(imageFile, userId);
      }
    } catch (e) {
      dev.log('Error uploading profile image: $e');
      // Try fallback method as last resort
      return await _uploadToTempFolder(imageFile, userId);
    }
  }

  // Helper method to clear the _lastResult list
  void _clearLastResult() {
    _lastResult.clear();
  }
  
  // Helper method to update the _lastResult list with a new value
  void _addToLastResult(String url) {
    _lastResult.add(url);
  }
  
  // Helper method to replace _lastResult with a new list
  void _setLastResult(List<String> urls) {
    _lastResult.clear();
    _lastResult.addAll(urls);
  }

  // Add a getter to safely access the last result
  List<String> get lastResult => List.unmodifiable(_lastResult);
} 