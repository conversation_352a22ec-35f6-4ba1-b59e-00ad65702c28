import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/models/user_settings_model.dart';
import '../../../auth/data/providers/auth_provider.dart';
import 'dart:developer' as dev;

final userSettingsRepositoryProvider = Provider<UserSettingsRepository>((ref) {
  final firestore = FirebaseFirestore.instance;
  final authState = ref.watch(authStateChangesProvider);
  
  // Return a repository with empty userId if auth state is loading or error
  if (authState.isLoading || authState.hasError) {
    return UserSettingsRepository(firestore, '');
  }
  
  // Return a repository with the user's ID if authenticated
  final userId = authState.value?.uid;
  if (userId == null) {
    return UserSettingsRepository(firestore, '');
  }
  
  return UserSettingsRepository(firestore, userId);
});

class UserSettingsRepository {
  final FirebaseFirestore _firestore;
  final String _userId;
  UserSettingsModel? _cachedSettings;
  DateTime? _lastCacheTime;

  UserSettingsRepository(this._firestore, this._userId);

  CollectionReference<UserSettingsModel> get _settingsCollection =>
      _firestore.collection('user_settings').withConverter(
            fromFirestore: UserSettingsModel.fromFirestore,
            toFirestore: (UserSettingsModel settings, _) => settings.toFirestore(),
          );

  Stream<UserSettingsModel?> watchUserSettings() {
    if (_userId.isEmpty) {
      return Stream.value(null);
    }

    return _settingsCollection
        .where('userId', isEqualTo: _userId)
        .limit(1)
        .snapshots()
        .map((snapshot) {
          if (snapshot.docs.isEmpty) {
            // If no settings exist, create default settings
            createDefaultSettings();
            return null;
          }
          return snapshot.docs.first.data();
        });
  }

  Future<UserSettingsModel?> getUserSettings() async {
    if (_userId.isEmpty) {
      return null;
    }

    final snapshot = await _settingsCollection
        .where('userId', isEqualTo: _userId)
        .limit(1)
        .get();

    if (snapshot.docs.isEmpty) {
      // If no settings exist, create default settings
      await createDefaultSettings();
      final newSnapshot = await _settingsCollection
          .where('userId', isEqualTo: _userId)
          .limit(1)
          .get();
      return newSnapshot.docs.isEmpty ? null : newSnapshot.docs.first.data();
    }

    return snapshot.docs.first.data();
  }

  Future<void> updateUserSettings(UserSettingsModel settings) async {
    if (_userId.isEmpty) {
      throw Exception('User not authenticated');
    }

    final now = DateTime.now();
    final updatedSettings = settings.copyWith(
      userId: _userId,
      updatedAt: now,
    );

    if (settings.id != null) {
      await _settingsCollection.doc(settings.id).update(updatedSettings.toFirestore());
    } else {
      final snapshot = await _settingsCollection
          .where('userId', isEqualTo: _userId)
          .limit(1)
          .get();
      
      if (snapshot.docs.isNotEmpty) {
        // Update existing settings
        await _settingsCollection.doc(snapshot.docs.first.id)
            .update(updatedSettings.toFirestore());
      } else {
        // Create new settings
        final newSettings = updatedSettings.copyWith(createdAt: now);
        await _settingsCollection.add(newSettings);
      }
    }
  }

  Future<void> createDefaultSettings() async {
    if (_userId.isEmpty) return;
    
    try {
      final now = DateTime.now();
      final defaultSettings = UserSettingsModel(
        userId: _userId,
        oilChangeReminders: true,
        maintenanceReminders: true,
        mileageReminders: true,
        promotionalNotifications: true,
        preferredLanguage: 'en',
        createdAt: now,
        updatedAt: now,
      );
      
      await _settingsCollection.doc(_userId).set(defaultSettings);
      
      _cachedSettings = defaultSettings;
      _lastCacheTime = DateTime.now();
    } catch (e) {
      dev.log('UserSettingsRepository: Error creating default settings: $e');
      rethrow;
    }
  }
} 