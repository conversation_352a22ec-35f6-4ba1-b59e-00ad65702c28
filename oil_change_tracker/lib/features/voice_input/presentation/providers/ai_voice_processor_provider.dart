import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/services/api_key_service.dart';
import '../../processors/ai_voice_processor.dart';

/// Provider for the AIVoiceProcessor.
///
/// This provider depends on the existing [apiKeyServiceProvider] to get the
/// necessary API keys for the [AIVoiceProcessor].
final aiVoiceProcessorProvider = Provider<AIVoiceProcessor>((ref) {
  final apiKeyService = ref.watch(apiKeyServiceProvider);
  return AIVoiceProcessor(apiKeyService);
});
