// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'voice_recording_provider.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$VoiceRecordingState {
  bool get isRecording => throw _privateConstructorUsedError;
  bool get isProcessing => throw _privateConstructorUsedError;
  bool get hasPermission => throw _privateConstructorUsedError;
  bool get listening => throw _privateConstructorUsedError;
  bool get noSpeechDetected => throw _privateConstructorUsedError;
  VoiceRecording? get currentRecording => throw _privateConstructorUsedError;
  String? get transcribedText => throw _privateConstructorUsedError;
  String? get error => throw _privateConstructorUsedError;
  String? get infoMessage => throw _privateConstructorUsedError;

  /// Create a copy of VoiceRecordingState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $VoiceRecordingStateCopyWith<VoiceRecordingState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $VoiceRecordingStateCopyWith<$Res> {
  factory $VoiceRecordingStateCopyWith(
          VoiceRecordingState value, $Res Function(VoiceRecordingState) then) =
      _$VoiceRecordingStateCopyWithImpl<$Res, VoiceRecordingState>;
  @useResult
  $Res call(
      {bool isRecording,
      bool isProcessing,
      bool hasPermission,
      bool listening,
      bool noSpeechDetected,
      VoiceRecording? currentRecording,
      String? transcribedText,
      String? error,
      String? infoMessage});

  $VoiceRecordingCopyWith<$Res>? get currentRecording;
}

/// @nodoc
class _$VoiceRecordingStateCopyWithImpl<$Res, $Val extends VoiceRecordingState>
    implements $VoiceRecordingStateCopyWith<$Res> {
  _$VoiceRecordingStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of VoiceRecordingState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isRecording = null,
    Object? isProcessing = null,
    Object? hasPermission = null,
    Object? listening = null,
    Object? noSpeechDetected = null,
    Object? currentRecording = freezed,
    Object? transcribedText = freezed,
    Object? error = freezed,
    Object? infoMessage = freezed,
  }) {
    return _then(_value.copyWith(
      isRecording: null == isRecording
          ? _value.isRecording
          : isRecording // ignore: cast_nullable_to_non_nullable
              as bool,
      isProcessing: null == isProcessing
          ? _value.isProcessing
          : isProcessing // ignore: cast_nullable_to_non_nullable
              as bool,
      hasPermission: null == hasPermission
          ? _value.hasPermission
          : hasPermission // ignore: cast_nullable_to_non_nullable
              as bool,
      listening: null == listening
          ? _value.listening
          : listening // ignore: cast_nullable_to_non_nullable
              as bool,
      noSpeechDetected: null == noSpeechDetected
          ? _value.noSpeechDetected
          : noSpeechDetected // ignore: cast_nullable_to_non_nullable
              as bool,
      currentRecording: freezed == currentRecording
          ? _value.currentRecording
          : currentRecording // ignore: cast_nullable_to_non_nullable
              as VoiceRecording?,
      transcribedText: freezed == transcribedText
          ? _value.transcribedText
          : transcribedText // ignore: cast_nullable_to_non_nullable
              as String?,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
      infoMessage: freezed == infoMessage
          ? _value.infoMessage
          : infoMessage // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }

  /// Create a copy of VoiceRecordingState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $VoiceRecordingCopyWith<$Res>? get currentRecording {
    if (_value.currentRecording == null) {
      return null;
    }

    return $VoiceRecordingCopyWith<$Res>(_value.currentRecording!, (value) {
      return _then(_value.copyWith(currentRecording: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$VoiceRecordingStateImplCopyWith<$Res>
    implements $VoiceRecordingStateCopyWith<$Res> {
  factory _$$VoiceRecordingStateImplCopyWith(_$VoiceRecordingStateImpl value,
          $Res Function(_$VoiceRecordingStateImpl) then) =
      __$$VoiceRecordingStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool isRecording,
      bool isProcessing,
      bool hasPermission,
      bool listening,
      bool noSpeechDetected,
      VoiceRecording? currentRecording,
      String? transcribedText,
      String? error,
      String? infoMessage});

  @override
  $VoiceRecordingCopyWith<$Res>? get currentRecording;
}

/// @nodoc
class __$$VoiceRecordingStateImplCopyWithImpl<$Res>
    extends _$VoiceRecordingStateCopyWithImpl<$Res, _$VoiceRecordingStateImpl>
    implements _$$VoiceRecordingStateImplCopyWith<$Res> {
  __$$VoiceRecordingStateImplCopyWithImpl(_$VoiceRecordingStateImpl _value,
      $Res Function(_$VoiceRecordingStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of VoiceRecordingState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isRecording = null,
    Object? isProcessing = null,
    Object? hasPermission = null,
    Object? listening = null,
    Object? noSpeechDetected = null,
    Object? currentRecording = freezed,
    Object? transcribedText = freezed,
    Object? error = freezed,
    Object? infoMessage = freezed,
  }) {
    return _then(_$VoiceRecordingStateImpl(
      isRecording: null == isRecording
          ? _value.isRecording
          : isRecording // ignore: cast_nullable_to_non_nullable
              as bool,
      isProcessing: null == isProcessing
          ? _value.isProcessing
          : isProcessing // ignore: cast_nullable_to_non_nullable
              as bool,
      hasPermission: null == hasPermission
          ? _value.hasPermission
          : hasPermission // ignore: cast_nullable_to_non_nullable
              as bool,
      listening: null == listening
          ? _value.listening
          : listening // ignore: cast_nullable_to_non_nullable
              as bool,
      noSpeechDetected: null == noSpeechDetected
          ? _value.noSpeechDetected
          : noSpeechDetected // ignore: cast_nullable_to_non_nullable
              as bool,
      currentRecording: freezed == currentRecording
          ? _value.currentRecording
          : currentRecording // ignore: cast_nullable_to_non_nullable
              as VoiceRecording?,
      transcribedText: freezed == transcribedText
          ? _value.transcribedText
          : transcribedText // ignore: cast_nullable_to_non_nullable
              as String?,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
      infoMessage: freezed == infoMessage
          ? _value.infoMessage
          : infoMessage // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$VoiceRecordingStateImpl implements _VoiceRecordingState {
  const _$VoiceRecordingStateImpl(
      {this.isRecording = false,
      this.isProcessing = false,
      this.hasPermission = false,
      this.listening = false,
      this.noSpeechDetected = false,
      this.currentRecording,
      this.transcribedText,
      this.error,
      this.infoMessage});

  @override
  @JsonKey()
  final bool isRecording;
  @override
  @JsonKey()
  final bool isProcessing;
  @override
  @JsonKey()
  final bool hasPermission;
  @override
  @JsonKey()
  final bool listening;
  @override
  @JsonKey()
  final bool noSpeechDetected;
  @override
  final VoiceRecording? currentRecording;
  @override
  final String? transcribedText;
  @override
  final String? error;
  @override
  final String? infoMessage;

  @override
  String toString() {
    return 'VoiceRecordingState(isRecording: $isRecording, isProcessing: $isProcessing, hasPermission: $hasPermission, listening: $listening, noSpeechDetected: $noSpeechDetected, currentRecording: $currentRecording, transcribedText: $transcribedText, error: $error, infoMessage: $infoMessage)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$VoiceRecordingStateImpl &&
            (identical(other.isRecording, isRecording) ||
                other.isRecording == isRecording) &&
            (identical(other.isProcessing, isProcessing) ||
                other.isProcessing == isProcessing) &&
            (identical(other.hasPermission, hasPermission) ||
                other.hasPermission == hasPermission) &&
            (identical(other.listening, listening) ||
                other.listening == listening) &&
            (identical(other.noSpeechDetected, noSpeechDetected) ||
                other.noSpeechDetected == noSpeechDetected) &&
            (identical(other.currentRecording, currentRecording) ||
                other.currentRecording == currentRecording) &&
            (identical(other.transcribedText, transcribedText) ||
                other.transcribedText == transcribedText) &&
            (identical(other.error, error) || other.error == error) &&
            (identical(other.infoMessage, infoMessage) ||
                other.infoMessage == infoMessage));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      isRecording,
      isProcessing,
      hasPermission,
      listening,
      noSpeechDetected,
      currentRecording,
      transcribedText,
      error,
      infoMessage);

  /// Create a copy of VoiceRecordingState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$VoiceRecordingStateImplCopyWith<_$VoiceRecordingStateImpl> get copyWith =>
      __$$VoiceRecordingStateImplCopyWithImpl<_$VoiceRecordingStateImpl>(
          this, _$identity);
}

abstract class _VoiceRecordingState implements VoiceRecordingState {
  const factory _VoiceRecordingState(
      {final bool isRecording,
      final bool isProcessing,
      final bool hasPermission,
      final bool listening,
      final bool noSpeechDetected,
      final VoiceRecording? currentRecording,
      final String? transcribedText,
      final String? error,
      final String? infoMessage}) = _$VoiceRecordingStateImpl;

  @override
  bool get isRecording;
  @override
  bool get isProcessing;
  @override
  bool get hasPermission;
  @override
  bool get listening;
  @override
  bool get noSpeechDetected;
  @override
  VoiceRecording? get currentRecording;
  @override
  String? get transcribedText;
  @override
  String? get error;
  @override
  String? get infoMessage;

  /// Create a copy of VoiceRecordingState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$VoiceRecordingStateImplCopyWith<_$VoiceRecordingStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
