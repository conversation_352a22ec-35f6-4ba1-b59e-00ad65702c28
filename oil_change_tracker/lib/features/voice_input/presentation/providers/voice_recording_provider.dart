import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:record/record.dart';
import 'package:firebase_remote_config/firebase_remote_config.dart';

import '../../../../core/services/api_key_service.dart';
import '../../../../core/utils/logger.dart';
import '../../application/use_cases/record_and_transcribe_use_case.dart';
import '../../domain/entities/voice_recording.dart';
import '../../domain/repositories/voice_recording_repository.dart';
import '../../infrastructure/repositories/openai_whisper_repository.dart';
import '../../infrastructure/repositories/record_package_recording_repository.dart';
import 'dart:async';

part 'voice_recording_provider.freezed.dart';

/// Provider for API key service
final apiKeyServiceProvider = Provider<ApiKeyService>((ref) {
  return ApiKeyService(FirebaseRemoteConfig.instance);
});

/// Provider for the voice recording repository
final voiceRecordingRepositoryProvider =
    Provider<VoiceRecordingRepository>((ref) {
  final repo = RecordPackageRecordingRepository();
  ref.onDispose(() => repo.dispose());
  return repo;
});

/// Provider for the speech recognition repository
final speechRecognitionRepositoryProvider = Provider((ref) {
  final apiKeyService = ref.watch(apiKeyServiceProvider);
  return OpenAIWhisperRepository(apiKeyService);
});

/// Provider for the record and transcribe use case
final recordAndTranscribeUseCaseProvider = Provider((ref) {
  final recordingRepository = ref.watch(voiceRecordingRepositoryProvider);
  final speechRepository = ref.watch(speechRecognitionRepositoryProvider);
  return RecordAndTranscribeUseCase(recordingRepository, speechRepository);
});

@freezed
class VoiceRecordingState with _$VoiceRecordingState {
  const factory VoiceRecordingState({
    @Default(false) bool isRecording,
    @Default(false) bool isProcessing,
    @Default(false) bool hasPermission,
    @Default(false) bool listening,
    @Default(false) bool noSpeechDetected,
    VoiceRecording? currentRecording,
    String? transcribedText,
    String? error,
    String? infoMessage,
  }) = _VoiceRecordingState;
}

/// Manages the state of the voice recording process.
class VoiceRecordingNotifier extends StateNotifier<VoiceRecordingState> {
  final RecordAndTranscribeUseCase _useCase;
  StreamSubscription<Amplitude>? _amplitudeSubscription;
  Timer? _silenceTimer;

  VoiceRecordingNotifier(this._useCase) : super(const VoiceRecordingState()) {
    _init();
  }

  Future<void> _init() async {
    final hasPerm = await _useCase.recordingRepository.hasPermission();
    state = state.copyWith(hasPermission: hasPerm);
    AppLogger.info(
        'VoiceRecordingNotifier: Initialized (permission: $hasPerm)');
  }

  Future<void> requestPermission() async {
    final granted = await _useCase.recordingRepository.requestPermission();
    state = state.copyWith(hasPermission: granted);
  }

  Future<void> startRecording({
    required String languageCode,
    String? formType,
  }) async {
    if (state.isRecording) return;
    if (!state.hasPermission) {
      await requestPermission();
      if (!state.hasPermission) {
        state = state.copyWith(error: 'Microphone permission is required.');
        return;
      }
    }

    // Set specific info message based on form type
    String infoMessage;
    if (formType == 'oil_change') {
      infoMessage = 'Say something like "Changed Castrol oil at 150,000 km"';
    } else if (formType == 'maintenance') {
      infoMessage = 'Say something like "Brake service for 2500 EGP"';
    } else {
      infoMessage = 'Say something like "Add oil change, 150000 km"';
    }

    state = state.copyWith(
        isRecording: true,
        error: null,
        listening: true,
        noSpeechDetected: false,
        infoMessage: infoMessage);
    try {
      final recording =
          await _useCase.startRecording(languageCode: languageCode);
      state = state.copyWith(currentRecording: recording);
      _startSilenceDetection();
      AppLogger.info(
          'VoiceRecordingNotifier: Recording started (${recording.id})');
    } catch (e, stack) {
      AppLogger.error('Failed to start recording', e, stack);
      state = state.copyWith(isRecording: false, error: e.toString());
    }
  }

  Future<void> stopRecording() async {
    if (!state.isRecording || state.currentRecording == null) return;
    _cancelSilenceDetection();

    state = state.copyWith(
        isRecording: false, isProcessing: true, listening: false);
    try {
      final recording =
          await _useCase.stopAndTranscribe(state.currentRecording!.id);
      state = state.copyWith(
        isProcessing: false,
        currentRecording: recording,
        transcribedText: recording.transcription,
      );
      AppLogger.info(
          'VoiceRecordingNotifier: Recording stopped and transcribed. Text: "${recording.transcription}"');
    } catch (e, stack) {
      AppLogger.error('Failed to stop and transcribe', e, stack);
      state = state.copyWith(isProcessing: false, error: e.toString());
    }
  }

  void _startSilenceDetection() {
    _amplitudeSubscription =
        _useCase.recordingRepository.amplitudeStream.listen((amp) {
      if (amp.current > -40) {
        // -40 dBFS is a reasonable threshold for speech
        _resetSilenceTimer();
      }
    });
    _resetSilenceTimer(); // Start the timer initially
  }

  void _resetSilenceTimer() {
    _silenceTimer?.cancel();
    _silenceTimer = Timer(const Duration(seconds: 4), () {
      if (state.isRecording) {
        AppLogger.info('Silence detected, stopping recording.');
        stopRecording();
        handleNoSpeechDetected();
      }
    });
  }

  void _cancelSilenceDetection() {
    _amplitudeSubscription?.cancel();
    _silenceTimer?.cancel();
  }

  void handleNoSpeechDetected() {
    state = state.copyWith(
      isRecording: false,
      isProcessing: false,
      listening: false,
      noSpeechDetected: true,
      infoMessage: 'No voice detected. Please try again.',
    );
  }

  void clearError() {
    state = state.copyWith(error: null);
  }

  void reset() {
    if (state.isRecording && state.currentRecording != null) {
      _useCase.recordingRepository.cancelRecording(state.currentRecording!.id);
    }
    _cancelSilenceDetection();
    state = const VoiceRecordingState(hasPermission: true);
    _init();
  }
}

/// Provides the notifier for managing the voice recording state.
final voiceRecordingProvider =
    StateNotifierProvider<VoiceRecordingNotifier, VoiceRecordingState>((ref) {
  final useCase = ref.watch(recordAndTranscribeUseCaseProvider);
  return VoiceRecordingNotifier(useCase);
});
