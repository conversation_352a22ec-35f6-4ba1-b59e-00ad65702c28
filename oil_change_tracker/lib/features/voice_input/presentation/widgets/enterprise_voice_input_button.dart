import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../features/subscription/providers/feature_gate_provider.dart';
import '../../../../features/subscription/presentation/screens/subscription_screen.dart';
import '../../../../features/subscription/presentation/widgets/premium_features_list.dart';
import '../../../../generated/app_localizations.dart';
import '../providers/voice_recording_provider.dart';
import 'listening_bottom_sheet.dart';
import '../../models/voice_form_type.dart';

/// A voice input button that uses the new cloud-based workflow.
class EnterpriseVoiceInputButton extends ConsumerStatefulWidget {
  final bool enabled;
  final Function(String) onTranscriptionComplete;
  final String languageCode;
  final VoiceFormType formType;

  const EnterpriseVoiceInputButton({
    super.key,
    this.enabled = true,
    required this.onTranscriptionComplete,
    this.languageCode = 'en-US',
    required this.formType,
  });

  @override
  ConsumerState<EnterpriseVoiceInputButton> createState() =>
      _EnterpriseVoiceInputButtonState();
}

class _EnterpriseVoiceInputButtonState
    extends ConsumerState<EnterpriseVoiceInputButton> {
  bool _sheetShown = false;
  BuildContext? _sheetContext;

  void _showBottomSheet() {
    if (_sheetShown) return;
    _sheetShown = true;
    showModalBottomSheet(
      context: context,
      isDismissible: false,
      enableDrag: false,
      builder: (ctx) {
        _sheetContext = ctx;
        return ListeningBottomSheet(
          formType: widget.formType,
          onCancel: _handleVoiceInput,
          onStopAndSend: _handleVoiceInput,
        );
      },
    ).whenComplete(() {
      _sheetShown = false;
      _sheetContext = null;
    });
  }

  void _closeSheet() {
    if (_sheetShown && _sheetContext != null) {
      Navigator.of(_sheetContext!).pop();
    }
  }

  @override
  void initState() {
    super.initState();
    // Reset the provider state when the widget is initialized
    Future.microtask(() => ref.read(voiceRecordingProvider.notifier).reset());
  }

  Future<void> _handleVoiceInput() async {
    if (!widget.enabled) return;

    // Check if the user has access to voice input feature
    final hasVoiceAccess =
        ref.read(featureGateProvider(PremiumFeature.voiceInput));

    if (!hasVoiceAccess) {
      // Show subscription promotion dialog
      _showSubscriptionPromotion();
      return;
    }

    final voiceNotifier = ref.read(voiceRecordingProvider.notifier);
    final voiceState = ref.read(voiceRecordingProvider);

    if (voiceState.isRecording) {
      await voiceNotifier.stopRecording();
      // The transcription result will be handled by the listener below.
    } else {
      await voiceNotifier.startRecording(
        languageCode: widget.languageCode,
        formType: widget.formType == VoiceFormType.oilChange
            ? 'oil_change'
            : 'maintenance',
      );
    }
  }

  // Show subscription promotion dialog
  void _showSubscriptionPromotion() {
    final l10n = S.of(context);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(l10n.premiumFeature),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(l10n.premiumRequired),
            const SizedBox(height: 16),
            Text(l10n.upgradeToRemoveAds),
            const SizedBox(height: 12),
            const SimplePremiumFeaturesList(),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(l10n.cancel),
          ),
          FilledButton(
            onPressed: () {
              Navigator.pop(context);
              Navigator.push(
                  context,
                  MaterialPageRoute(
                      builder: (_) => const SubscriptionScreen()));
            },
            child: Text(l10n.upgradeNow),
          ),
        ],
      ),
    );
  }

  void _showErrorFeedback(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message,
            style: TextStyle(color: Theme.of(context).colorScheme.onError)),
        backgroundColor: Theme.of(context).colorScheme.error,
      ),
    );
    ref.read(voiceRecordingProvider.notifier).clearError();
  }

  @override
  Widget build(BuildContext context) {
    ref.listen<VoiceRecordingState>(voiceRecordingProvider, (previous, next) {
      if (previous?.error != next.error && next.error != null) {
        _showErrorFeedback(next.error!);
      }
      if (previous?.transcribedText != next.transcribedText &&
          next.transcribedText != null) {
        if (next.transcribedText!.isNotEmpty) {
          widget.onTranscriptionComplete(next.transcribedText!);
        }
      }

      // Handle overlay visibility
      if (next.listening && !_sheetShown) {
        _showBottomSheet();
      } else if (!next.listening && _sheetShown) {
        _closeSheet();
      }
    });

    final voiceState = ref.watch(voiceRecordingProvider);
    final isRecording = voiceState.isRecording;
    final isProcessing = voiceState.isProcessing;
    final isListening = voiceState.listening;
    final noSpeechDetected = voiceState.noSpeechDetected;
    final infoMessage = voiceState.infoMessage;

    return Stack(
      alignment: Alignment.center,
      children: [
        Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (noSpeechDetected)
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                margin: const EdgeInsets.only(bottom: 12),
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(0.7),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  infoMessage ??
                      (isListening ? 'Listening...' : 'No voice detected.'),
                  style: const TextStyle(color: Colors.white, fontSize: 16),
                ),
              ),
            GestureDetector(
              onTap: _handleVoiceInput,
              child: AnimatedContainer(
                duration: const Duration(milliseconds: 300),
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: isRecording
                      ? Theme.of(context).colorScheme.error
                      : Theme.of(context).colorScheme.primary,
                  boxShadow: [
                    if (isRecording || isProcessing)
                      BoxShadow(
                        color: Theme.of(context)
                            .colorScheme
                            .primary
                            .withOpacity(0.5),
                        spreadRadius: isRecording ? 8 : 12,
                        blurRadius: 16,
                      ),
                  ],
                ),
                child: isProcessing
                    ? const CircularProgressIndicator(
                        valueColor: AlwaysStoppedAnimation(Colors.white),
                      )
                    : Icon(
                        isRecording ? Icons.stop : Icons.mic,
                        color: Colors.white,
                        size: 32,
                      ),
              ),
            ),
          ],
        ),
        // Overlay handled via BottomSheet
      ],
    );
  }

  @override
  void dispose() {
    _closeSheet();
    super.dispose();
  }
}
