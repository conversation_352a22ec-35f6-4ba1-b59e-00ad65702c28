import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/theme/theme_extensions.dart';
import '../../../../generated/app_localizations.dart';
import '../../../car_management/providers/car_provider.dart';

// Extension to add missing localization strings temporarily
extension VoiceCommandsHelpLocalizationExtension on S {
  String get voiceCommandsHelp => 'Voice Commands Help';
  String get voiceCommandsHelpDescription =>
      'Try these example commands with the voice input feature:';

  // Dynamic example methods that take car info
  String exampleAddOilChange(String make, String model) =>
      'Add oil change for my $make $model';
  String exampleAddMaintenance(String make, String model) =>
      'Add brake service for my $make $model';
  String exampleAddCar(int year, String make, String model) =>
      'Add my $year $make $model';
  String exampleUpdateMileage(int mileage, String make, String model) =>
      'Update mileage to $mileage for my $make $model';
}

/// A widget that displays example voice commands to help users
class VoiceCommandsHelp extends ConsumerWidget {
  /// Creates a widget that displays example voice commands
  const VoiceCommandsHelp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final s = S.of(context);
    final carsAsync = ref.watch(carsProvider);

    // Default values if no cars are available - use user's locale/region appropriate defaults
    final defaultMake = 'Ford';
    final defaultModel = 'Mustang';
    final defaultYear = DateTime.now().year;
    final defaultMileage = 50000;

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.mic,
                  color: context.accentColor,
                ),
                const SizedBox(width: 8),
                Text(
                  s.voiceCommandsHelp,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              s.voiceCommandsHelpDescription,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 16),
            carsAsync.when(
              data: (cars) {
                if (cars.isEmpty) {
                  // No cars yet – encourage the user to add one.
                  return Padding(
                    padding: const EdgeInsets.only(top: 12.0),
                    child: Text(
                      'Add your first car to unlock voice commands!',
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                  );
                }

                final firstCar = cars[0];
                final secondCar = cars.length > 1 ? cars[1] : firstCar;

                return Column(
                  children: [
                    _buildCommandExample(
                      context,
                      Icons.oil_barrel,
                      s.exampleAddOilChange(firstCar.make, firstCar.model),
                    ),
                    const SizedBox(height: 12),
                    _buildCommandExample(
                      context,
                      Icons.build,
                      s.exampleAddMaintenance(secondCar.make, secondCar.model),
                    ),
                    const SizedBox(height: 12),
                    _buildCommandExample(
                      context,
                      Icons.directions_car,
                      s.exampleAddCar(
                          DateTime.now().year, secondCar.make, secondCar.model),
                    ),
                    const SizedBox(height: 12),
                    _buildCommandExample(
                      context,
                      Icons.speed,
                      s.exampleUpdateMileage((firstCar.currentMileage) + 5000,
                          firstCar.make, firstCar.model),
                    ),
                  ],
                );
              },
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (_, __) => const SizedBox.shrink(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCommandExample(
      BuildContext context, IconData icon, String text) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(
          icon,
          color: context.secondaryAccentColor,
          size: 20,
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Text(
            text,
            style: Theme.of(context).textTheme.bodyMedium,
          ),
        ),
      ],
    );
  }
}
