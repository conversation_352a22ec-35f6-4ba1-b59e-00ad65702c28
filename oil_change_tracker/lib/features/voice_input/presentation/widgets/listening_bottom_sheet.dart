import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../models/voice_form_type.dart';
import '../../../../generated/app_localizations.dart';
import '../providers/voice_recording_provider.dart';
import '../../services/voice_command_service.dart';

/// Bottom sheet shown while listening to voice input
class ListeningBottomSheet extends ConsumerStatefulWidget {
  final VoiceFormType formType;
  final VoidCallback onCancel;

  /// Called when user taps "Stop and Send". Parent should stop recording and
  /// handle processing. The bottom-sheet itself does not directly interact
  /// with recording providers anymore.
  final VoidCallback onStopAndSend;

  const ListeningBottomSheet({
    super.key,
    required this.formType,
    required this.onCancel,
    required this.onStopAndSend,
  });

  @override
  ConsumerState<ListeningBottomSheet> createState() =>
      _ListeningBottomSheetState();
}

class _ListeningBottomSheetState extends ConsumerState<ListeningBottomSheet>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  bool _isRecording = false;
  bool _isProcessing = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(seconds: 1),
      vsync: this,
    )..repeat(reverse: true);

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final voiceState = ref.watch(voiceRecordingProvider);
    final l10n = S.of(context);
    final isArabic = Localizations.localeOf(context).languageCode == 'ar';

    return Container(
      height: MediaQuery.of(context).size.height *
          0.75, // Make it bigger - 75% of screen height
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          // Drag handle
          Container(
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.outline.withOpacity(0.4),
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          const SizedBox(height: 24),

          // Animated mic icon
          AnimatedBuilder(
            animation: _scaleAnimation,
            builder: (context, child) {
              return Transform.scale(
                scale: _scaleAnimation.value,
                child: Container(
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color:
                        Theme.of(context).colorScheme.primary.withOpacity(0.1),
                  ),
                  child: Icon(
                    Icons.mic,
                    size: 48,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
              );
            },
          ),

          const SizedBox(height: 24),

          // Status text
          Text(
            voiceState.infoMessage ??
                (isArabic
                    ? 'اضغط على "بدء التسجيل" للبدء'
                    : 'Tap "Start Recording" to begin'),
            style: Theme.of(context).textTheme.titleMedium,
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 24),

          // Examples card - Make it expanded to fill available space
          Expanded(
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Theme.of(context)
                    .colorScheme
                    .primaryContainer
                    .withOpacity(0.3),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: Theme.of(context).colorScheme.primary.withOpacity(0.2),
                ),
              ),
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.lightbulb_outline,
                          size: 20,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          isArabic ? 'أمثلة للمساعدة:' : 'Example commands:',
                          style: Theme.of(context)
                              .textTheme
                              .titleSmall
                              ?.copyWith(
                                color: Theme.of(context).colorScheme.primary,
                                fontWeight: FontWeight.bold,
                              ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    Text(
                      _getFormTypeHint(widget.formType, l10n, isArabic),
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color:
                                Theme.of(context).colorScheme.onSurfaceVariant,
                            height: 1.5,
                          ),
                      textAlign: isArabic ? TextAlign.right : TextAlign.left,
                    ),
                  ],
                ),
              ),
            ),
          ),

          const SizedBox(height: 24),

          if (!_isRecording) ...[
            SizedBox(
              width: double.infinity,
              child: FilledButton.icon(
                onPressed: () {
                  final voiceService = ref.read(voiceCommandServiceProvider);
                  voiceService.startRecording(
                    VoiceContext(
                      originScreen: widget.formType,
                      languageCode: isArabic ? 'ar-EG' : 'en-US',
                    ),
                  );
                  setState(() {
                    _isRecording = true;
                  });
                },
                icon: const Icon(Icons.mic),
                label: Text(isArabic ? 'بدء التسجيل' : 'Start Recording'),
                style: FilledButton.styleFrom(
                  backgroundColor: Theme.of(context).colorScheme.primary,
                  foregroundColor: Theme.of(context).colorScheme.onPrimary,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
                ),
              ),
            ),
            const SizedBox(height: 12),
          ],

          // Stop and Send button (only show when recording)
          if (_isRecording) ...[
            SizedBox(
              width: double.infinity,
              child: FilledButton.icon(
                onPressed: () {
                  setState(() {
                    _isRecording = false;
                    _isProcessing = true;
                  });
                  widget
                      .onStopAndSend(); // sheet will be dismissed by caller after processing
                },
                icon: const Icon(Icons.stop),
                label: Text(isArabic ? 'إيقاف وإرسال' : 'Stop and Send'),
                style: FilledButton.styleFrom(
                  backgroundColor: Theme.of(context).colorScheme.error,
                  foregroundColor: Theme.of(context).colorScheme.onError,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
                ),
              ),
            ),
            const SizedBox(height: 12),
          ],

          if (_isProcessing) ...[
            const SizedBox(height: 12),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const CircularProgressIndicator(),
                const SizedBox(width: 12),
                Text(isArabic ? 'جارٍ المعالجة...' : 'Processing...'),
              ],
            ),
          ] else ...[
            // Cancel button
            TextButton(
              onPressed: () {
                final voiceService = ref.read(voiceCommandServiceProvider);
                voiceService.cancelRecording();
                widget.onCancel();
                if (context.mounted) Navigator.of(context).pop();
              },
              child: Text(l10n.cancel),
            ),
          ],
        ],
      ),
    );
  }

  String _getFormTypeHint(VoiceFormType type, S l10n, bool isArabic) {
    switch (type) {
      case VoiceFormType.oilChange:
        if (isArabic) {
          return 'أمثلة:\n'
              '• "غيرت زيت كاسترول 5W30 عند 150 ألف كم بسعر 1200 جنيه"\n'
              '• "تغيير زيت موبيل ١ مع فلتر هواء عند 85000 كيلو"\n'
              '• "بدلت الزيت والفلتر بتكلفة 1500 جنيه عند 120 ألف كم"\n'
              '• "صيانة زيت شل هيليكس بلس عند 95000 كيلومتر"';
        } else {
          return 'Examples:\n'
              '• "Changed Castrol 5W30 oil at 150,000 km for 1200 EGP"\n'
              '• "Oil change Mobil 1 with air filter at 85,000 km"\n'
              '• "Changed oil and filter for 1500 EGP at 120,000 km"\n'
              '• "Shell Helix Plus oil service at 95,000 kilometers"';
        }
      case VoiceFormType.maintenance:
        if (isArabic) {
          return 'أمثلة:\n'
              '• "صيانة فرامل عند ميكانيكي الثقة بسعر 2500 جنيه"\n'
              '• "تغيير بطارية جديدة عند 90 ألف كم بتكلفة 800 ريال"\n'
              '• "إصلاح كمبريسر التكييف عند ورشة النجمة"\n'
              '• "فحص وصيانة فلتر هواء بسعر 350 جنيه عند 75000 كيلو"';
        } else {
          return 'Examples:\n'
              '• "Brake service at AutoZone for 2500 EGP"\n'
              '• "New battery replacement at 90,000 km for 800 SAR"\n'
              '• "AC compressor repair at Star Workshop"\n'
              '• "Air filter inspection and service for 350 EGP at 75,000 km"';
        }
    }
  }
}
