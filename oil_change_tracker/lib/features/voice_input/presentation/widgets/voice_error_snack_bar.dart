import 'package:flutter/material.dart';
import '../../../../generated/app_localizations.dart';

/// Error snack bar for voice-related errors
class VoiceErrorSnackBar {
  static void show({
    required BuildContext context,
    required String message,
    String? actionLabel,
    VoidCallback? onAction,
  }) {
    final l10n = S.of(context);
    final theme = Theme.of(context);

    // Clean up the message for user display
    String displayMessage = message;
    if (message.contains('Exception:')) {
      displayMessage = message.split('Exception:').last.trim();
    }

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(
              Icons.error_outline,
              color: theme.colorScheme.onError,
              size: 20,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                displayMessage,
                style: TextStyle(
                  color: theme.colorScheme.onError,
                ),
              ),
            ),
          ],
        ),
        backgroundColor: theme.colorScheme.error,
        behavior: SnackBarBehavior.fixed,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        action: actionLabel != null
            ? SnackBarAction(
                label: actionLabel,
                textColor: theme.colorScheme.onError,
                onPressed: onAction ?? () {},
              )
            : null,
        duration: const Duration(seconds: 4),
      ),
    );
  }
}
