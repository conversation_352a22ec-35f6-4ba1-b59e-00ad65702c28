import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'dart:async';

import '../../../../features/subscription/providers/feature_gate_provider.dart';
import '../../../../features/subscription/presentation/screens/subscription_screen.dart';
import '../../../../features/subscription/presentation/widgets/premium_features_list.dart';
import '../../../../generated/app_localizations.dart';
import '../../services/voice_command_service.dart';
import '../../models/voice_form_type.dart';
import 'listening_bottom_sheet.dart';
import 'voice_error_snack_bar.dart';

/// Unified voice mic button with pulse animation and quota checking
class VoiceMicButton extends ConsumerStatefulWidget {
  final VoiceFormType formType;
  final Function(String transcription, Map<String, dynamic>? extractedData)
      onComplete;
  final String languageCode;
  final bool enabled;

  /// When set to `true`, the recording will start automatically once the
  /// widget finishes its first build cycle. Useful for flows where the user
  /// should start speaking immediately after opening the voice dialog.
  final bool autoStart;

  const VoiceMicButton({
    super.key,
    required this.formType,
    required this.onComplete,
    this.languageCode = 'en-US',
    this.enabled = true,
    this.autoStart = false,
  });

  @override
  ConsumerState<VoiceMicButton> createState() => _VoiceMicButtonState();
}

class _VoiceMicButtonState extends ConsumerState<VoiceMicButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _pulseAnimation;
  bool _isRecording = false;
  bool _isProcessing = false;
  bool _sheetShown = false;
  BuildContext? _sheetContext;
  StreamSubscription? _recordingSubscription;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );
    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.3,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    // Remove auto-start functionality - user must manually start recording
  }

  @override
  void dispose() {
    _animationController.dispose();
    _recordingSubscription?.cancel();
    _closeSheet();
    super.dispose();
  }

  void _startPulseAnimation() {
    _animationController.repeat(reverse: true);
  }

  void _stopPulseAnimation() {
    _animationController.stop();
    _animationController.reset();
  }

  void _showSheet() {
    if (_sheetShown) return;
    _sheetShown = true;

    showModalBottomSheet(
      context: context,
      isDismissible: true,
      enableDrag: true,
      isScrollControlled: true,
      builder: (ctx) {
        _sheetContext = ctx;
        return ListeningBottomSheet(
          formType: widget.formType,
          onCancel: _handleCancel,
          onStopAndSend: () async {
            await _stopRecording();
          },
        );
      },
    ).whenComplete(() {
      _sheetShown = false;
      _sheetContext = null;
    });
  }

  void _closeSheet() {
    if (_sheetShown &&
        _sheetContext != null &&
        Navigator.of(_sheetContext!).canPop()) {
      Navigator.of(_sheetContext!).pop();
      _sheetShown = false;
      _sheetContext = null;
    }
  }

  Future<void> _handleTap() async {
    if (!widget.enabled || _isProcessing) return;

    // Check premium access
    final hasVoiceAccess =
        ref.read(featureGateProvider(PremiumFeature.voiceInput));
    if (!hasVoiceAccess) {
      _showSubscriptionPromotion();
      return;
    }

    if (_isRecording) {
      await _stopRecording();
    } else {
      // Just show the sheet, let user start recording manually
      _showSheet();
    }
  }

  Future<void> _startRecording() async {
    final voiceService = ref.read(voiceCommandServiceProvider);

    try {
      if (mounted) {
      setState(() {
        _isRecording = true;
      });
      }

      _startPulseAnimation();
      _showSheet();

      final context = VoiceContext(
        originScreen: widget.formType,
        languageCode: widget.languageCode,
      );

      await voiceService.startRecording(context);
    } catch (e) {
      VLOG('ui', 'Failed to start recording: $e');
      if (mounted) {
      setState(() {
        _isRecording = false;
      });
      }
      _stopPulseAnimation();
      _closeSheet();

      if (mounted) {
        VoiceErrorSnackBar.show(
          context: this.context,
          message: 'Failed to start recording: ${e.toString()}',
        );
      }
    }
  }

  Future<void> _stopRecording() async {
    final voiceService = ref.read(voiceCommandServiceProvider);

    if (mounted) {
    setState(() {
      _isRecording = false;
      _isProcessing = true;
    });
    }

    _stopPulseAnimation();

    try {
      final context = VoiceContext(
        originScreen: widget.formType,
        languageCode: widget.languageCode,
      );

      final result = await voiceService.stopAndProcess(context);

      _closeSheet();

      if (result.success && result.transcription != null) {
        widget.onComplete(result.transcription!, result.extractedData);
      } else {
        if (mounted) {
          VoiceErrorSnackBar.show(
            context: this.context,
            message: result.error ?? 'Could not understand the command',
          );
        }
      }
    } catch (e) {
      VLOG('ui', 'Failed to process recording: $e');
      _closeSheet();

      if (mounted) {
        VoiceErrorSnackBar.show(
          context: this.context,
          message: 'Failed to process recording: ${e.toString()}',
        );
      }
    } finally {
      if (mounted) {
      setState(() {
        _isProcessing = false;
      });
      }
    }
  }

  Future<void> _handleCancel() async {
    final voiceService = ref.read(voiceCommandServiceProvider);

    if (_isRecording) {
      await voiceService.cancelRecording();
      if (mounted) {
      setState(() {
        _isRecording = false;
      });
      }
      _stopPulseAnimation();
    }

    _closeSheet();
  }

  void _showSubscriptionPromotion() {
    final l10n = S.of(context);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(l10n.premiumFeature),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(l10n.premiumRequired),
            const SizedBox(height: 16),
            Text(l10n.upgradeToRemoveAds),
            const SizedBox(height: 12),
            const SimplePremiumFeaturesList(),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(l10n.cancel),
          ),
          FilledButton(
            onPressed: () {
              Navigator.pop(context);
              Navigator.push(
                  context,
                  MaterialPageRoute(
                      builder: (_) => const SubscriptionScreen()));
            },
            child: Text(l10n.upgradeNow),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return FloatingActionButton(
      onPressed: _handleTap,
      backgroundColor: _isRecording
          ? Theme.of(context).colorScheme.error
          : Theme.of(context).colorScheme.primary,
      child: AnimatedBuilder(
        animation: _pulseAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _isRecording ? _pulseAnimation.value : 1.0,
            child: _isProcessing
                ? const CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation(Colors.white),
                    strokeWidth: 2,
                  )
                : Icon(
                    _isRecording ? Icons.stop : Icons.mic,
                    color: Colors.white,
                    size: 28,
                  ),
          );
        },
      ),
    );
  }
}
