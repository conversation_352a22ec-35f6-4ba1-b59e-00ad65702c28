import 'package:flutter/material.dart';
import 'dart:math' as math;

/// A widget that displays an animation for voice input
class VoiceAnimation extends StatefulWidget {
  /// The scale of the animation
  final double scale;

  /// Creates a voice animation widget
  const VoiceAnimation({super.key, this.scale = 1.0});

  @override
  State<VoiceAnimation> createState() => _VoiceAnimationState();
}

class _VoiceAnimationState extends State<VoiceAnimation>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  final List<Animation<double>> _animations = [];
  final List<double> _heights = [];
  final int _barCount = 5;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    )..repeat();

    // Create random heights and animations for each bar
    final random = math.Random();
    for (int i = 0; i < _barCount; i++) {
      _heights.add(0.3 + random.nextDouble() * 0.7);
      _animations.add(
        Tween<double>(
          begin: 0.3,
          end: 0.9,
        ).animate(
          CurvedAnimation(
            parent: _controller,
            curve: Interval(
              i * 0.1,
              0.1 + i * 0.1,
              curve: Curves.easeInOut,
            ),
          ),
        ),
      );
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return SizedBox(
          height: 60 * widget.scale,
          width: 60 * widget.scale,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: List.generate(
              _barCount,
              (index) => _buildBar(index),
            ),
          ),
        );
      },
    );
  }

  Widget _buildBar(int index) {
    final height = _heights[index] * _animations[index].value;
    return Container(
      width: 4 * widget.scale,
      height: 40 * height * widget.scale,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primary,
        borderRadius: BorderRadius.circular(5),
      ),
    );
  }
} 