import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../generated/app_localizations.dart';
import '../../../car_management/providers/car_provider.dart';
import 'voice_commands_help.dart';

/// Overlay that appears while voice recognition is active.
/// Displays a microphone with pulse animation, a cancel button and
/// localized example commands to guide the user.
class VoiceListeningOverlay extends StatefulWidget {
  /// Called when user taps the cancel button.
  final VoidCallback onCancel;

  /// Whether the overlay should be visible.
  final bool visible;

  const VoiceListeningOverlay({
    super.key,
    required this.onCancel,
    required this.visible,
  });

  @override
  State<VoiceListeningOverlay> createState() => _VoiceListeningOverlayState();
}

class _VoiceListeningOverlayState extends State<VoiceListeningOverlay>
    with SingleTickerProviderStateMixin {
  late final AnimationController _controller;
  late final Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    )..repeat(reverse: true);
    _pulseAnimation = Tween<double>(begin: 1.0, end: 1.3).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.visible) return const SizedBox.shrink();

    final s = S.of(context);

    return Consumer(
      builder: (context, ref, _) {
        final carsAsync = ref.watch(carsProvider);
        final exampleCommands = _buildExampleCommands(s, carsAsync);

        return Material(
          color: Colors.black.withOpacity(0.6),
          child: Stack(
            children: [
              // Cancel button top right
              Positioned(
                right: 16,
                top: 32,
                child: IconButton(
                  icon: const Icon(Icons.close, color: Colors.white, size: 28),
                  onPressed: widget.onCancel,
                ),
              ),
              // Centered content
              Center(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    ScaleTransition(
                      scale: _pulseAnimation,
                      child: const Icon(
                        Icons.mic,
                        color: Colors.white,
                        size: 72,
                      ),
                    ),
                    const SizedBox(height: 16),
                    const Text(
                      'Listening...',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 24),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 24),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            s.voiceCommandsHelpDescription,
                            style: const TextStyle(
                              color: Colors.white70,
                              fontSize: 14,
                            ),
                          ),
                          const SizedBox(height: 12),
                          for (final cmd in exampleCommands)
                            Padding(
                              padding: const EdgeInsets.only(bottom: 8.0),
                              child: Row(
                                children: [
                                  const Icon(Icons.chevron_right,
                                      color: Colors.white70, size: 18),
                                  const SizedBox(width: 4),
                                  Expanded(
                                    child: Text(
                                      cmd,
                                      style: const TextStyle(
                                        color: Colors.white,
                                        fontSize: 14,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  List<String> _buildExampleCommands(S s, AsyncValue<List<dynamic>> carsAsync) {
    // Basic fallbacks
    String make = 'Ford';
    String model = 'Mustang';
    int year = DateTime.now().year;
    int mileage = 50000;

    carsAsync.whenData((cars) {
      if (cars.isNotEmpty) {
        final car = cars.first;
        make = car.make;
        model = car.model;
        year = car.year;
        mileage = car.currentMileage + 5000;
      }
    });

    final locale = Localizations.localeOf(context).toString();
    final currencyExample =
        NumberFormat.simpleCurrency(locale: locale).format(100);

    return [
      s.exampleAddOilChange(make, model),
      s.exampleAddMaintenance(make, model),
      s.exampleAddCar(year, make, model),
      // Currency formatting example command
      'Convert 100 to $currencyExample',
      s.exampleUpdateMileage(mileage, make, model),
    ];
  }
}
