import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';

import '../../../../core/models/car_model.dart';
import '../../../../core/theme/theme_extensions.dart';
import '../../../../generated/app_localizations.dart';
import '../../models/voice_command_result.dart';
import '../../models/voice_form_type.dart';
import '../../processors/voice_command_processor.dart';
import '../../presentation/widgets/enterprise_voice_input_button.dart';

/// A dialog that shows the result of a voice command and allows the user to confirm or retry
class VoiceCommandConfirmationDialog extends ConsumerWidget {
  /// The voice command result to confirm
  final VoiceCommandResult result;

  /// Optional car model if available
  final CarModel? car;

  /// Creates a voice command confirmation dialog
  const VoiceCommandConfirmationDialog({
    super.key,
    required this.result,
    this.car,
  });

  /// Show the dialog
  static Future<bool?> show(
    BuildContext context,
    VoiceCommandResult result, {
    CarModel? car,
  }) {
    return showDialog<bool>(
      context: context,
      builder: (context) => VoiceCommandConfirmationDialog(
        result: result,
        car: car,
      ),
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final l10n = S.of(context);
    final theme = Theme.of(context);

    // Get dialog title based on command type
    String title;
    IconData icon;
    VoiceFormType formType;

    switch (result.commandType) {
      case VoiceCommandType.addOilChange:
        title = l10n.addOilChange;
        icon = Icons.oil_barrel;
        formType = VoiceFormType.oilChange;
        break;
      case VoiceCommandType.addMaintenance:
        title = l10n.addMaintenance;
        icon = Icons.build;
        formType = VoiceFormType.maintenance;
        break;
      case VoiceCommandType.viewCarDetails:
        title = l10n.viewCarDetails;
        icon = Icons.directions_car;
        formType = VoiceFormType.oilChange; // Default form type
        break;
      case VoiceCommandType.unknown:
      default:
        title = l10n.unknownCommand;
        icon = Icons.help_outline;
        formType = VoiceFormType.oilChange; // Default form type
        break;
    }

    return AlertDialog(
      backgroundColor: context.containerBackgroundColor,
      titlePadding: EdgeInsets.zero,
      title: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: theme.colorScheme.primary.withOpacity(0.1),
          borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
        ),
        child: Column(
          children: [
            CircleAvatar(
              radius: 28,
              backgroundColor: theme.colorScheme.primary.withOpacity(0.2),
              child: Icon(icon, color: theme.colorScheme.primary, size: 32),
            ),
            const SizedBox(height: 12),
            Text(
              title,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: context.primaryTextColor,
              ),
            ),
          ],
        ),
      ),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Original voice text
            Text(
              l10n.youSaid,
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: context.secondaryTextColor,
                fontSize: 14,
              ),
            ),
            const SizedBox(height: 4),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: theme.colorScheme.surface,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: theme.dividerColor),
              ),
              width: double.infinity,
              child: Text(
                '"${result.originalText}"',
                style: TextStyle(
                  fontStyle: FontStyle.italic,
                  color: context.primaryTextColor,
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Extracted parameters
            Text(
              l10n.extractedInformation,
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: context.secondaryTextColor,
                fontSize: 14,
              ),
            ),
            const SizedBox(height: 8),

            // Parameters list
            ...result.parameters.entries.map((entry) {
              // Format parameter name and value
              final name = _formatParameterName(entry.key);
              final value = _formatParameterValue(entry.value);

              return Padding(
                padding: const EdgeInsets.symmetric(vertical: 4),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(
                      width: 100,
                      child: Text(
                        '$name:',
                        style: TextStyle(
                          fontWeight: FontWeight.w500,
                          color: context.secondaryTextColor,
                        ),
                      ),
                    ),
                    Expanded(
                      child: Text(
                        value,
                        style: TextStyle(
                          color: context.primaryTextColor,
                        ),
                      ),
                    ),
                  ],
                ),
              );
            }).toList(),
          ],
        ),
      ),
      actions: [
        // Retry button
        TextButton.icon(
          onPressed: () {
            Navigator.pop(context, false);

            // Show voice input bottom sheet after a short delay
            Future.delayed(const Duration(milliseconds: 300), () {
              if (context.mounted) {
                showModalBottomSheet(
                  context: context,
                  isScrollControlled: true,
                  builder: (context) => Padding(
                    padding: EdgeInsets.only(
                      bottom: MediaQuery.of(context).viewInsets.bottom,
                    ),
                    child: EnterpriseVoiceInputButton(
                      formType: formType,
                      onTranscriptionComplete: (text) {
                        Navigator.pop(context);
                        _processVoiceCommand(context, ref, text);
                      },
                    ),
                  ),
                );
              }
            });
          },
          icon: const Icon(Icons.mic),
          label: Text(l10n.retry),
          style: TextButton.styleFrom(
            foregroundColor: theme.colorScheme.secondary,
          ),
        ),

        // Confirm button
        FilledButton.icon(
          onPressed: () {
            Navigator.pop(context, true);

            // Navigate to the appropriate route with parameters
            if (result.targetRoute != null) {
              context.push(result.targetRoute!, extra: result.parameters);
            }
          },
          icon: const Icon(Icons.check),
          label: Text(l10n.confirm),
          style: FilledButton.styleFrom(
            backgroundColor: theme.colorScheme.primary,
            foregroundColor: theme.colorScheme.onPrimary,
          ),
        ),
      ],
    );
  }

  // Process a new voice command
  Future<void> _processVoiceCommand(
      BuildContext context, WidgetRef ref, String text) async {
    final processor = ref.read(voiceCommandProcessorProvider);
    final result = await processor.processCommand(text,
        locale: Localizations.localeOf(context));

    if (result != null && context.mounted) {
      final confirmed =
          await VoiceCommandConfirmationDialog.show(context, result);

      if (confirmed == true && context.mounted && result.targetRoute != null) {
        context.push(result.targetRoute!, extra: result.parameters);
      }
    } else if (context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(S.of(context).couldNotUnderstandCommand),
          behavior: SnackBarBehavior.fixed,
        ),
      );
    }
  }

  // Format parameter name for display
  String _formatParameterName(String name) {
    return name
        .replaceAllMapped(RegExp(r'([A-Z])'), (match) => ' ${match.group(0)}')
        .trim()
        .split(' ')
        .map((word) =>
            word.isNotEmpty ? word[0].toUpperCase() + word.substring(1) : '')
        .join(' ');
  }

  // Format parameter value for display
  String _formatParameterValue(dynamic value) {
    if (value == null) return 'N/A';

    if (value is DateTime) {
      return DateFormat.yMMMd().format(value);
    }

    if (value is bool) {
      return value ? 'Yes' : 'No';
    }

    if (value is double) {
      return value.toStringAsFixed(2);
    }

    return value.toString();
  }
}
