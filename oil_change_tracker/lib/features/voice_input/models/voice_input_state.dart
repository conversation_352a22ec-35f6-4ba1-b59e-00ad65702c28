import 'package:freezed_annotation/freezed_annotation.dart';
import 'voice_command_result.dart';

part 'voice_input_state.freezed.dart';

/// State for voice input
@freezed
class VoiceInputState with _$VoiceInputState {
  /// Idle state - not listening
  const factory VoiceInputState.idle() = _Idle;

  /// Listening state - actively recording
  const factory VoiceInputState.listening() = _Listening;

  /// Recognizing state - processing partial results
  const factory VoiceInputState.recognizing({
    required String text,
  }) = _Recognizing;

  /// Recognized state - final recognition result
  const factory VoiceInputState.recognized({
    required String text,
    required double confidence,
  }) = _Recognized;

  /// Processing state - analyzing the recognized text
  const factory VoiceInputState.processing() = _Processing;

  /// Success state - command recognized and processed
  const factory VoiceInputState.success(VoiceCommandResult result) = _Success;

  /// No match state - couldn't match the recognized text to a command
  const factory VoiceInputState.noMatch() = _NoMatch;

  /// Error state - something went wrong
  const factory VoiceInputState.error(String message) = _Error;

  /// Permission denied state - microphone permission not granted
  const factory VoiceInputState.permissionDenied() = _PermissionDenied;

  /// Requires subscription state - feature requires premium subscription
  const factory VoiceInputState.requiresSubscription() = _RequiresSubscription;
}
