// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'voice_command_result.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$VoiceCommandResultImpl _$$VoiceCommandResultImplFromJson(
        Map<String, dynamic> json) =>
    _$VoiceCommandResultImpl(
      commandType: $enumDecode(_$VoiceCommandTypeEnumMap, json['commandType']),
      confidence: (json['confidence'] as num).toDouble(),
      originalText: json['originalText'] as String,
      targetRoute: json['targetRoute'] as String?,
      parameters: json['parameters'] as Map<String, dynamic>? ??
          const <String, dynamic>{},
    );

Map<String, dynamic> _$$VoiceCommandResultImplToJson(
        _$VoiceCommandResultImpl instance) =>
    <String, dynamic>{
      'commandType': _$VoiceCommandTypeEnumMap[instance.commandType]!,
      'confidence': instance.confidence,
      'originalText': instance.originalText,
      'targetRoute': instance.targetRoute,
      'parameters': instance.parameters,
    };

const _$VoiceCommandTypeEnumMap = {
  VoiceCommandType.addOilChange: 'addOilChange',
  VoiceCommandType.addMaintenance: 'addMaintenance',
  VoiceCommandType.viewCarDetails: 'viewCarDetails',
  VoiceCommandType.unknown: 'unknown',
};
