// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'voice_command_result.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

VoiceCommandResult _$VoiceCommandResultFromJson(Map<String, dynamic> json) {
  return _VoiceCommandResult.fromJson(json);
}

/// @nodoc
mixin _$VoiceCommandResult {
  /// Type of command recognized
  VoiceCommandType get commandType => throw _privateConstructorUsedError;

  /// Confidence level of recognition (0.0-1.0)
  double get confidence => throw _privateConstructorUsedError;

  /// Original text from speech recognition
  String get originalText => throw _privateConstructorUsedError;

  /// Route to navigate to (if applicable)
  String? get targetRoute => throw _privateConstructorUsedError;

  /// Parameters extracted from the command
  Map<String, dynamic> get parameters => throw _privateConstructorUsedError;

  /// Serializes this VoiceCommandResult to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of VoiceCommandResult
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $VoiceCommandResultCopyWith<VoiceCommandResult> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $VoiceCommandResultCopyWith<$Res> {
  factory $VoiceCommandResultCopyWith(
          VoiceCommandResult value, $Res Function(VoiceCommandResult) then) =
      _$VoiceCommandResultCopyWithImpl<$Res, VoiceCommandResult>;
  @useResult
  $Res call(
      {VoiceCommandType commandType,
      double confidence,
      String originalText,
      String? targetRoute,
      Map<String, dynamic> parameters});
}

/// @nodoc
class _$VoiceCommandResultCopyWithImpl<$Res, $Val extends VoiceCommandResult>
    implements $VoiceCommandResultCopyWith<$Res> {
  _$VoiceCommandResultCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of VoiceCommandResult
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? commandType = null,
    Object? confidence = null,
    Object? originalText = null,
    Object? targetRoute = freezed,
    Object? parameters = null,
  }) {
    return _then(_value.copyWith(
      commandType: null == commandType
          ? _value.commandType
          : commandType // ignore: cast_nullable_to_non_nullable
              as VoiceCommandType,
      confidence: null == confidence
          ? _value.confidence
          : confidence // ignore: cast_nullable_to_non_nullable
              as double,
      originalText: null == originalText
          ? _value.originalText
          : originalText // ignore: cast_nullable_to_non_nullable
              as String,
      targetRoute: freezed == targetRoute
          ? _value.targetRoute
          : targetRoute // ignore: cast_nullable_to_non_nullable
              as String?,
      parameters: null == parameters
          ? _value.parameters
          : parameters // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$VoiceCommandResultImplCopyWith<$Res>
    implements $VoiceCommandResultCopyWith<$Res> {
  factory _$$VoiceCommandResultImplCopyWith(_$VoiceCommandResultImpl value,
          $Res Function(_$VoiceCommandResultImpl) then) =
      __$$VoiceCommandResultImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {VoiceCommandType commandType,
      double confidence,
      String originalText,
      String? targetRoute,
      Map<String, dynamic> parameters});
}

/// @nodoc
class __$$VoiceCommandResultImplCopyWithImpl<$Res>
    extends _$VoiceCommandResultCopyWithImpl<$Res, _$VoiceCommandResultImpl>
    implements _$$VoiceCommandResultImplCopyWith<$Res> {
  __$$VoiceCommandResultImplCopyWithImpl(_$VoiceCommandResultImpl _value,
      $Res Function(_$VoiceCommandResultImpl) _then)
      : super(_value, _then);

  /// Create a copy of VoiceCommandResult
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? commandType = null,
    Object? confidence = null,
    Object? originalText = null,
    Object? targetRoute = freezed,
    Object? parameters = null,
  }) {
    return _then(_$VoiceCommandResultImpl(
      commandType: null == commandType
          ? _value.commandType
          : commandType // ignore: cast_nullable_to_non_nullable
              as VoiceCommandType,
      confidence: null == confidence
          ? _value.confidence
          : confidence // ignore: cast_nullable_to_non_nullable
              as double,
      originalText: null == originalText
          ? _value.originalText
          : originalText // ignore: cast_nullable_to_non_nullable
              as String,
      targetRoute: freezed == targetRoute
          ? _value.targetRoute
          : targetRoute // ignore: cast_nullable_to_non_nullable
              as String?,
      parameters: null == parameters
          ? _value._parameters
          : parameters // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$VoiceCommandResultImpl implements _VoiceCommandResult {
  const _$VoiceCommandResultImpl(
      {required this.commandType,
      required this.confidence,
      required this.originalText,
      this.targetRoute,
      final Map<String, dynamic> parameters = const <String, dynamic>{}})
      : _parameters = parameters;

  factory _$VoiceCommandResultImpl.fromJson(Map<String, dynamic> json) =>
      _$$VoiceCommandResultImplFromJson(json);

  /// Type of command recognized
  @override
  final VoiceCommandType commandType;

  /// Confidence level of recognition (0.0-1.0)
  @override
  final double confidence;

  /// Original text from speech recognition
  @override
  final String originalText;

  /// Route to navigate to (if applicable)
  @override
  final String? targetRoute;

  /// Parameters extracted from the command
  final Map<String, dynamic> _parameters;

  /// Parameters extracted from the command
  @override
  @JsonKey()
  Map<String, dynamic> get parameters {
    if (_parameters is EqualUnmodifiableMapView) return _parameters;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_parameters);
  }

  @override
  String toString() {
    return 'VoiceCommandResult(commandType: $commandType, confidence: $confidence, originalText: $originalText, targetRoute: $targetRoute, parameters: $parameters)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$VoiceCommandResultImpl &&
            (identical(other.commandType, commandType) ||
                other.commandType == commandType) &&
            (identical(other.confidence, confidence) ||
                other.confidence == confidence) &&
            (identical(other.originalText, originalText) ||
                other.originalText == originalText) &&
            (identical(other.targetRoute, targetRoute) ||
                other.targetRoute == targetRoute) &&
            const DeepCollectionEquality()
                .equals(other._parameters, _parameters));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      commandType,
      confidence,
      originalText,
      targetRoute,
      const DeepCollectionEquality().hash(_parameters));

  /// Create a copy of VoiceCommandResult
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$VoiceCommandResultImplCopyWith<_$VoiceCommandResultImpl> get copyWith =>
      __$$VoiceCommandResultImplCopyWithImpl<_$VoiceCommandResultImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$VoiceCommandResultImplToJson(
      this,
    );
  }
}

abstract class _VoiceCommandResult implements VoiceCommandResult {
  const factory _VoiceCommandResult(
      {required final VoiceCommandType commandType,
      required final double confidence,
      required final String originalText,
      final String? targetRoute,
      final Map<String, dynamic> parameters}) = _$VoiceCommandResultImpl;

  factory _VoiceCommandResult.fromJson(Map<String, dynamic> json) =
      _$VoiceCommandResultImpl.fromJson;

  /// Type of command recognized
  @override
  VoiceCommandType get commandType;

  /// Confidence level of recognition (0.0-1.0)
  @override
  double get confidence;

  /// Original text from speech recognition
  @override
  String get originalText;

  /// Route to navigate to (if applicable)
  @override
  String? get targetRoute;

  /// Parameters extracted from the command
  @override
  Map<String, dynamic> get parameters;

  /// Create a copy of VoiceCommandResult
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$VoiceCommandResultImplCopyWith<_$VoiceCommandResultImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
