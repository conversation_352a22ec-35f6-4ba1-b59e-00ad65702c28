import 'package:freezed_annotation/freezed_annotation.dart';

part 'voice_command_result.freezed.dart';
part 'voice_command_result.g.dart';

/// Types of voice commands
enum VoiceCommandType {
  /// Add a new oil change record
  addOilChange,

  /// Add a new maintenance record
  addMaintenance,

  /// View car details
  viewCarDetails,

  /// Unknown command
  unknown
}

/// Result of processing a voice command
@freezed
class VoiceCommandResult with _$VoiceCommandResult {
  /// Creates a voice command result
  const factory VoiceCommandResult({
    /// Type of command recognized
    required VoiceCommandType commandType,

    /// Confidence level of recognition (0.0-1.0)
    required double confidence,

    /// Original text from speech recognition
    required String originalText,

    /// Route to navigate to (if applicable)
    String? targetRoute,

    /// Parameters extracted from the command
    @Default(<String, dynamic>{}) Map<String, dynamic> parameters,
  }) = _VoiceCommandResult;

  /// Create from JSON
  factory VoiceCommandResult.fromJson(Map<String, dynamic> json) =>
      _$VoiceCommandResultFromJson(json);
}
