// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'voice_input_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$VoiceInputState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() idle,
    required TResult Function() listening,
    required TResult Function(String text) recognizing,
    required TResult Function(String text, double confidence) recognized,
    required TResult Function() processing,
    required TResult Function(VoiceCommandResult result) success,
    required TResult Function() noMatch,
    required TResult Function(String message) error,
    required TResult Function() permissionDenied,
    required TResult Function() requiresSubscription,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? idle,
    TResult? Function()? listening,
    TResult? Function(String text)? recognizing,
    TResult? Function(String text, double confidence)? recognized,
    TResult? Function()? processing,
    TResult? Function(VoiceCommandResult result)? success,
    TResult? Function()? noMatch,
    TResult? Function(String message)? error,
    TResult? Function()? permissionDenied,
    TResult? Function()? requiresSubscription,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? idle,
    TResult Function()? listening,
    TResult Function(String text)? recognizing,
    TResult Function(String text, double confidence)? recognized,
    TResult Function()? processing,
    TResult Function(VoiceCommandResult result)? success,
    TResult Function()? noMatch,
    TResult Function(String message)? error,
    TResult Function()? permissionDenied,
    TResult Function()? requiresSubscription,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Idle value) idle,
    required TResult Function(_Listening value) listening,
    required TResult Function(_Recognizing value) recognizing,
    required TResult Function(_Recognized value) recognized,
    required TResult Function(_Processing value) processing,
    required TResult Function(_Success value) success,
    required TResult Function(_NoMatch value) noMatch,
    required TResult Function(_Error value) error,
    required TResult Function(_PermissionDenied value) permissionDenied,
    required TResult Function(_RequiresSubscription value) requiresSubscription,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Idle value)? idle,
    TResult? Function(_Listening value)? listening,
    TResult? Function(_Recognizing value)? recognizing,
    TResult? Function(_Recognized value)? recognized,
    TResult? Function(_Processing value)? processing,
    TResult? Function(_Success value)? success,
    TResult? Function(_NoMatch value)? noMatch,
    TResult? Function(_Error value)? error,
    TResult? Function(_PermissionDenied value)? permissionDenied,
    TResult? Function(_RequiresSubscription value)? requiresSubscription,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Idle value)? idle,
    TResult Function(_Listening value)? listening,
    TResult Function(_Recognizing value)? recognizing,
    TResult Function(_Recognized value)? recognized,
    TResult Function(_Processing value)? processing,
    TResult Function(_Success value)? success,
    TResult Function(_NoMatch value)? noMatch,
    TResult Function(_Error value)? error,
    TResult Function(_PermissionDenied value)? permissionDenied,
    TResult Function(_RequiresSubscription value)? requiresSubscription,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $VoiceInputStateCopyWith<$Res> {
  factory $VoiceInputStateCopyWith(
          VoiceInputState value, $Res Function(VoiceInputState) then) =
      _$VoiceInputStateCopyWithImpl<$Res, VoiceInputState>;
}

/// @nodoc
class _$VoiceInputStateCopyWithImpl<$Res, $Val extends VoiceInputState>
    implements $VoiceInputStateCopyWith<$Res> {
  _$VoiceInputStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of VoiceInputState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$IdleImplCopyWith<$Res> {
  factory _$$IdleImplCopyWith(
          _$IdleImpl value, $Res Function(_$IdleImpl) then) =
      __$$IdleImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$IdleImplCopyWithImpl<$Res>
    extends _$VoiceInputStateCopyWithImpl<$Res, _$IdleImpl>
    implements _$$IdleImplCopyWith<$Res> {
  __$$IdleImplCopyWithImpl(_$IdleImpl _value, $Res Function(_$IdleImpl) _then)
      : super(_value, _then);

  /// Create a copy of VoiceInputState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$IdleImpl implements _Idle {
  const _$IdleImpl();

  @override
  String toString() {
    return 'VoiceInputState.idle()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$IdleImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() idle,
    required TResult Function() listening,
    required TResult Function(String text) recognizing,
    required TResult Function(String text, double confidence) recognized,
    required TResult Function() processing,
    required TResult Function(VoiceCommandResult result) success,
    required TResult Function() noMatch,
    required TResult Function(String message) error,
    required TResult Function() permissionDenied,
    required TResult Function() requiresSubscription,
  }) {
    return idle();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? idle,
    TResult? Function()? listening,
    TResult? Function(String text)? recognizing,
    TResult? Function(String text, double confidence)? recognized,
    TResult? Function()? processing,
    TResult? Function(VoiceCommandResult result)? success,
    TResult? Function()? noMatch,
    TResult? Function(String message)? error,
    TResult? Function()? permissionDenied,
    TResult? Function()? requiresSubscription,
  }) {
    return idle?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? idle,
    TResult Function()? listening,
    TResult Function(String text)? recognizing,
    TResult Function(String text, double confidence)? recognized,
    TResult Function()? processing,
    TResult Function(VoiceCommandResult result)? success,
    TResult Function()? noMatch,
    TResult Function(String message)? error,
    TResult Function()? permissionDenied,
    TResult Function()? requiresSubscription,
    required TResult orElse(),
  }) {
    if (idle != null) {
      return idle();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Idle value) idle,
    required TResult Function(_Listening value) listening,
    required TResult Function(_Recognizing value) recognizing,
    required TResult Function(_Recognized value) recognized,
    required TResult Function(_Processing value) processing,
    required TResult Function(_Success value) success,
    required TResult Function(_NoMatch value) noMatch,
    required TResult Function(_Error value) error,
    required TResult Function(_PermissionDenied value) permissionDenied,
    required TResult Function(_RequiresSubscription value) requiresSubscription,
  }) {
    return idle(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Idle value)? idle,
    TResult? Function(_Listening value)? listening,
    TResult? Function(_Recognizing value)? recognizing,
    TResult? Function(_Recognized value)? recognized,
    TResult? Function(_Processing value)? processing,
    TResult? Function(_Success value)? success,
    TResult? Function(_NoMatch value)? noMatch,
    TResult? Function(_Error value)? error,
    TResult? Function(_PermissionDenied value)? permissionDenied,
    TResult? Function(_RequiresSubscription value)? requiresSubscription,
  }) {
    return idle?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Idle value)? idle,
    TResult Function(_Listening value)? listening,
    TResult Function(_Recognizing value)? recognizing,
    TResult Function(_Recognized value)? recognized,
    TResult Function(_Processing value)? processing,
    TResult Function(_Success value)? success,
    TResult Function(_NoMatch value)? noMatch,
    TResult Function(_Error value)? error,
    TResult Function(_PermissionDenied value)? permissionDenied,
    TResult Function(_RequiresSubscription value)? requiresSubscription,
    required TResult orElse(),
  }) {
    if (idle != null) {
      return idle(this);
    }
    return orElse();
  }
}

abstract class _Idle implements VoiceInputState {
  const factory _Idle() = _$IdleImpl;
}

/// @nodoc
abstract class _$$ListeningImplCopyWith<$Res> {
  factory _$$ListeningImplCopyWith(
          _$ListeningImpl value, $Res Function(_$ListeningImpl) then) =
      __$$ListeningImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$ListeningImplCopyWithImpl<$Res>
    extends _$VoiceInputStateCopyWithImpl<$Res, _$ListeningImpl>
    implements _$$ListeningImplCopyWith<$Res> {
  __$$ListeningImplCopyWithImpl(
      _$ListeningImpl _value, $Res Function(_$ListeningImpl) _then)
      : super(_value, _then);

  /// Create a copy of VoiceInputState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$ListeningImpl implements _Listening {
  const _$ListeningImpl();

  @override
  String toString() {
    return 'VoiceInputState.listening()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$ListeningImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() idle,
    required TResult Function() listening,
    required TResult Function(String text) recognizing,
    required TResult Function(String text, double confidence) recognized,
    required TResult Function() processing,
    required TResult Function(VoiceCommandResult result) success,
    required TResult Function() noMatch,
    required TResult Function(String message) error,
    required TResult Function() permissionDenied,
    required TResult Function() requiresSubscription,
  }) {
    return listening();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? idle,
    TResult? Function()? listening,
    TResult? Function(String text)? recognizing,
    TResult? Function(String text, double confidence)? recognized,
    TResult? Function()? processing,
    TResult? Function(VoiceCommandResult result)? success,
    TResult? Function()? noMatch,
    TResult? Function(String message)? error,
    TResult? Function()? permissionDenied,
    TResult? Function()? requiresSubscription,
  }) {
    return listening?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? idle,
    TResult Function()? listening,
    TResult Function(String text)? recognizing,
    TResult Function(String text, double confidence)? recognized,
    TResult Function()? processing,
    TResult Function(VoiceCommandResult result)? success,
    TResult Function()? noMatch,
    TResult Function(String message)? error,
    TResult Function()? permissionDenied,
    TResult Function()? requiresSubscription,
    required TResult orElse(),
  }) {
    if (listening != null) {
      return listening();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Idle value) idle,
    required TResult Function(_Listening value) listening,
    required TResult Function(_Recognizing value) recognizing,
    required TResult Function(_Recognized value) recognized,
    required TResult Function(_Processing value) processing,
    required TResult Function(_Success value) success,
    required TResult Function(_NoMatch value) noMatch,
    required TResult Function(_Error value) error,
    required TResult Function(_PermissionDenied value) permissionDenied,
    required TResult Function(_RequiresSubscription value) requiresSubscription,
  }) {
    return listening(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Idle value)? idle,
    TResult? Function(_Listening value)? listening,
    TResult? Function(_Recognizing value)? recognizing,
    TResult? Function(_Recognized value)? recognized,
    TResult? Function(_Processing value)? processing,
    TResult? Function(_Success value)? success,
    TResult? Function(_NoMatch value)? noMatch,
    TResult? Function(_Error value)? error,
    TResult? Function(_PermissionDenied value)? permissionDenied,
    TResult? Function(_RequiresSubscription value)? requiresSubscription,
  }) {
    return listening?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Idle value)? idle,
    TResult Function(_Listening value)? listening,
    TResult Function(_Recognizing value)? recognizing,
    TResult Function(_Recognized value)? recognized,
    TResult Function(_Processing value)? processing,
    TResult Function(_Success value)? success,
    TResult Function(_NoMatch value)? noMatch,
    TResult Function(_Error value)? error,
    TResult Function(_PermissionDenied value)? permissionDenied,
    TResult Function(_RequiresSubscription value)? requiresSubscription,
    required TResult orElse(),
  }) {
    if (listening != null) {
      return listening(this);
    }
    return orElse();
  }
}

abstract class _Listening implements VoiceInputState {
  const factory _Listening() = _$ListeningImpl;
}

/// @nodoc
abstract class _$$RecognizingImplCopyWith<$Res> {
  factory _$$RecognizingImplCopyWith(
          _$RecognizingImpl value, $Res Function(_$RecognizingImpl) then) =
      __$$RecognizingImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String text});
}

/// @nodoc
class __$$RecognizingImplCopyWithImpl<$Res>
    extends _$VoiceInputStateCopyWithImpl<$Res, _$RecognizingImpl>
    implements _$$RecognizingImplCopyWith<$Res> {
  __$$RecognizingImplCopyWithImpl(
      _$RecognizingImpl _value, $Res Function(_$RecognizingImpl) _then)
      : super(_value, _then);

  /// Create a copy of VoiceInputState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? text = null,
  }) {
    return _then(_$RecognizingImpl(
      text: null == text
          ? _value.text
          : text // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$RecognizingImpl implements _Recognizing {
  const _$RecognizingImpl({required this.text});

  @override
  final String text;

  @override
  String toString() {
    return 'VoiceInputState.recognizing(text: $text)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RecognizingImpl &&
            (identical(other.text, text) || other.text == text));
  }

  @override
  int get hashCode => Object.hash(runtimeType, text);

  /// Create a copy of VoiceInputState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$RecognizingImplCopyWith<_$RecognizingImpl> get copyWith =>
      __$$RecognizingImplCopyWithImpl<_$RecognizingImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() idle,
    required TResult Function() listening,
    required TResult Function(String text) recognizing,
    required TResult Function(String text, double confidence) recognized,
    required TResult Function() processing,
    required TResult Function(VoiceCommandResult result) success,
    required TResult Function() noMatch,
    required TResult Function(String message) error,
    required TResult Function() permissionDenied,
    required TResult Function() requiresSubscription,
  }) {
    return recognizing(text);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? idle,
    TResult? Function()? listening,
    TResult? Function(String text)? recognizing,
    TResult? Function(String text, double confidence)? recognized,
    TResult? Function()? processing,
    TResult? Function(VoiceCommandResult result)? success,
    TResult? Function()? noMatch,
    TResult? Function(String message)? error,
    TResult? Function()? permissionDenied,
    TResult? Function()? requiresSubscription,
  }) {
    return recognizing?.call(text);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? idle,
    TResult Function()? listening,
    TResult Function(String text)? recognizing,
    TResult Function(String text, double confidence)? recognized,
    TResult Function()? processing,
    TResult Function(VoiceCommandResult result)? success,
    TResult Function()? noMatch,
    TResult Function(String message)? error,
    TResult Function()? permissionDenied,
    TResult Function()? requiresSubscription,
    required TResult orElse(),
  }) {
    if (recognizing != null) {
      return recognizing(text);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Idle value) idle,
    required TResult Function(_Listening value) listening,
    required TResult Function(_Recognizing value) recognizing,
    required TResult Function(_Recognized value) recognized,
    required TResult Function(_Processing value) processing,
    required TResult Function(_Success value) success,
    required TResult Function(_NoMatch value) noMatch,
    required TResult Function(_Error value) error,
    required TResult Function(_PermissionDenied value) permissionDenied,
    required TResult Function(_RequiresSubscription value) requiresSubscription,
  }) {
    return recognizing(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Idle value)? idle,
    TResult? Function(_Listening value)? listening,
    TResult? Function(_Recognizing value)? recognizing,
    TResult? Function(_Recognized value)? recognized,
    TResult? Function(_Processing value)? processing,
    TResult? Function(_Success value)? success,
    TResult? Function(_NoMatch value)? noMatch,
    TResult? Function(_Error value)? error,
    TResult? Function(_PermissionDenied value)? permissionDenied,
    TResult? Function(_RequiresSubscription value)? requiresSubscription,
  }) {
    return recognizing?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Idle value)? idle,
    TResult Function(_Listening value)? listening,
    TResult Function(_Recognizing value)? recognizing,
    TResult Function(_Recognized value)? recognized,
    TResult Function(_Processing value)? processing,
    TResult Function(_Success value)? success,
    TResult Function(_NoMatch value)? noMatch,
    TResult Function(_Error value)? error,
    TResult Function(_PermissionDenied value)? permissionDenied,
    TResult Function(_RequiresSubscription value)? requiresSubscription,
    required TResult orElse(),
  }) {
    if (recognizing != null) {
      return recognizing(this);
    }
    return orElse();
  }
}

abstract class _Recognizing implements VoiceInputState {
  const factory _Recognizing({required final String text}) = _$RecognizingImpl;

  String get text;

  /// Create a copy of VoiceInputState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$RecognizingImplCopyWith<_$RecognizingImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$RecognizedImplCopyWith<$Res> {
  factory _$$RecognizedImplCopyWith(
          _$RecognizedImpl value, $Res Function(_$RecognizedImpl) then) =
      __$$RecognizedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String text, double confidence});
}

/// @nodoc
class __$$RecognizedImplCopyWithImpl<$Res>
    extends _$VoiceInputStateCopyWithImpl<$Res, _$RecognizedImpl>
    implements _$$RecognizedImplCopyWith<$Res> {
  __$$RecognizedImplCopyWithImpl(
      _$RecognizedImpl _value, $Res Function(_$RecognizedImpl) _then)
      : super(_value, _then);

  /// Create a copy of VoiceInputState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? text = null,
    Object? confidence = null,
  }) {
    return _then(_$RecognizedImpl(
      text: null == text
          ? _value.text
          : text // ignore: cast_nullable_to_non_nullable
              as String,
      confidence: null == confidence
          ? _value.confidence
          : confidence // ignore: cast_nullable_to_non_nullable
              as double,
    ));
  }
}

/// @nodoc

class _$RecognizedImpl implements _Recognized {
  const _$RecognizedImpl({required this.text, required this.confidence});

  @override
  final String text;
  @override
  final double confidence;

  @override
  String toString() {
    return 'VoiceInputState.recognized(text: $text, confidence: $confidence)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RecognizedImpl &&
            (identical(other.text, text) || other.text == text) &&
            (identical(other.confidence, confidence) ||
                other.confidence == confidence));
  }

  @override
  int get hashCode => Object.hash(runtimeType, text, confidence);

  /// Create a copy of VoiceInputState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$RecognizedImplCopyWith<_$RecognizedImpl> get copyWith =>
      __$$RecognizedImplCopyWithImpl<_$RecognizedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() idle,
    required TResult Function() listening,
    required TResult Function(String text) recognizing,
    required TResult Function(String text, double confidence) recognized,
    required TResult Function() processing,
    required TResult Function(VoiceCommandResult result) success,
    required TResult Function() noMatch,
    required TResult Function(String message) error,
    required TResult Function() permissionDenied,
    required TResult Function() requiresSubscription,
  }) {
    return recognized(text, confidence);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? idle,
    TResult? Function()? listening,
    TResult? Function(String text)? recognizing,
    TResult? Function(String text, double confidence)? recognized,
    TResult? Function()? processing,
    TResult? Function(VoiceCommandResult result)? success,
    TResult? Function()? noMatch,
    TResult? Function(String message)? error,
    TResult? Function()? permissionDenied,
    TResult? Function()? requiresSubscription,
  }) {
    return recognized?.call(text, confidence);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? idle,
    TResult Function()? listening,
    TResult Function(String text)? recognizing,
    TResult Function(String text, double confidence)? recognized,
    TResult Function()? processing,
    TResult Function(VoiceCommandResult result)? success,
    TResult Function()? noMatch,
    TResult Function(String message)? error,
    TResult Function()? permissionDenied,
    TResult Function()? requiresSubscription,
    required TResult orElse(),
  }) {
    if (recognized != null) {
      return recognized(text, confidence);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Idle value) idle,
    required TResult Function(_Listening value) listening,
    required TResult Function(_Recognizing value) recognizing,
    required TResult Function(_Recognized value) recognized,
    required TResult Function(_Processing value) processing,
    required TResult Function(_Success value) success,
    required TResult Function(_NoMatch value) noMatch,
    required TResult Function(_Error value) error,
    required TResult Function(_PermissionDenied value) permissionDenied,
    required TResult Function(_RequiresSubscription value) requiresSubscription,
  }) {
    return recognized(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Idle value)? idle,
    TResult? Function(_Listening value)? listening,
    TResult? Function(_Recognizing value)? recognizing,
    TResult? Function(_Recognized value)? recognized,
    TResult? Function(_Processing value)? processing,
    TResult? Function(_Success value)? success,
    TResult? Function(_NoMatch value)? noMatch,
    TResult? Function(_Error value)? error,
    TResult? Function(_PermissionDenied value)? permissionDenied,
    TResult? Function(_RequiresSubscription value)? requiresSubscription,
  }) {
    return recognized?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Idle value)? idle,
    TResult Function(_Listening value)? listening,
    TResult Function(_Recognizing value)? recognizing,
    TResult Function(_Recognized value)? recognized,
    TResult Function(_Processing value)? processing,
    TResult Function(_Success value)? success,
    TResult Function(_NoMatch value)? noMatch,
    TResult Function(_Error value)? error,
    TResult Function(_PermissionDenied value)? permissionDenied,
    TResult Function(_RequiresSubscription value)? requiresSubscription,
    required TResult orElse(),
  }) {
    if (recognized != null) {
      return recognized(this);
    }
    return orElse();
  }
}

abstract class _Recognized implements VoiceInputState {
  const factory _Recognized(
      {required final String text,
      required final double confidence}) = _$RecognizedImpl;

  String get text;
  double get confidence;

  /// Create a copy of VoiceInputState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$RecognizedImplCopyWith<_$RecognizedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ProcessingImplCopyWith<$Res> {
  factory _$$ProcessingImplCopyWith(
          _$ProcessingImpl value, $Res Function(_$ProcessingImpl) then) =
      __$$ProcessingImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$ProcessingImplCopyWithImpl<$Res>
    extends _$VoiceInputStateCopyWithImpl<$Res, _$ProcessingImpl>
    implements _$$ProcessingImplCopyWith<$Res> {
  __$$ProcessingImplCopyWithImpl(
      _$ProcessingImpl _value, $Res Function(_$ProcessingImpl) _then)
      : super(_value, _then);

  /// Create a copy of VoiceInputState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$ProcessingImpl implements _Processing {
  const _$ProcessingImpl();

  @override
  String toString() {
    return 'VoiceInputState.processing()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$ProcessingImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() idle,
    required TResult Function() listening,
    required TResult Function(String text) recognizing,
    required TResult Function(String text, double confidence) recognized,
    required TResult Function() processing,
    required TResult Function(VoiceCommandResult result) success,
    required TResult Function() noMatch,
    required TResult Function(String message) error,
    required TResult Function() permissionDenied,
    required TResult Function() requiresSubscription,
  }) {
    return processing();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? idle,
    TResult? Function()? listening,
    TResult? Function(String text)? recognizing,
    TResult? Function(String text, double confidence)? recognized,
    TResult? Function()? processing,
    TResult? Function(VoiceCommandResult result)? success,
    TResult? Function()? noMatch,
    TResult? Function(String message)? error,
    TResult? Function()? permissionDenied,
    TResult? Function()? requiresSubscription,
  }) {
    return processing?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? idle,
    TResult Function()? listening,
    TResult Function(String text)? recognizing,
    TResult Function(String text, double confidence)? recognized,
    TResult Function()? processing,
    TResult Function(VoiceCommandResult result)? success,
    TResult Function()? noMatch,
    TResult Function(String message)? error,
    TResult Function()? permissionDenied,
    TResult Function()? requiresSubscription,
    required TResult orElse(),
  }) {
    if (processing != null) {
      return processing();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Idle value) idle,
    required TResult Function(_Listening value) listening,
    required TResult Function(_Recognizing value) recognizing,
    required TResult Function(_Recognized value) recognized,
    required TResult Function(_Processing value) processing,
    required TResult Function(_Success value) success,
    required TResult Function(_NoMatch value) noMatch,
    required TResult Function(_Error value) error,
    required TResult Function(_PermissionDenied value) permissionDenied,
    required TResult Function(_RequiresSubscription value) requiresSubscription,
  }) {
    return processing(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Idle value)? idle,
    TResult? Function(_Listening value)? listening,
    TResult? Function(_Recognizing value)? recognizing,
    TResult? Function(_Recognized value)? recognized,
    TResult? Function(_Processing value)? processing,
    TResult? Function(_Success value)? success,
    TResult? Function(_NoMatch value)? noMatch,
    TResult? Function(_Error value)? error,
    TResult? Function(_PermissionDenied value)? permissionDenied,
    TResult? Function(_RequiresSubscription value)? requiresSubscription,
  }) {
    return processing?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Idle value)? idle,
    TResult Function(_Listening value)? listening,
    TResult Function(_Recognizing value)? recognizing,
    TResult Function(_Recognized value)? recognized,
    TResult Function(_Processing value)? processing,
    TResult Function(_Success value)? success,
    TResult Function(_NoMatch value)? noMatch,
    TResult Function(_Error value)? error,
    TResult Function(_PermissionDenied value)? permissionDenied,
    TResult Function(_RequiresSubscription value)? requiresSubscription,
    required TResult orElse(),
  }) {
    if (processing != null) {
      return processing(this);
    }
    return orElse();
  }
}

abstract class _Processing implements VoiceInputState {
  const factory _Processing() = _$ProcessingImpl;
}

/// @nodoc
abstract class _$$SuccessImplCopyWith<$Res> {
  factory _$$SuccessImplCopyWith(
          _$SuccessImpl value, $Res Function(_$SuccessImpl) then) =
      __$$SuccessImplCopyWithImpl<$Res>;
  @useResult
  $Res call({VoiceCommandResult result});

  $VoiceCommandResultCopyWith<$Res> get result;
}

/// @nodoc
class __$$SuccessImplCopyWithImpl<$Res>
    extends _$VoiceInputStateCopyWithImpl<$Res, _$SuccessImpl>
    implements _$$SuccessImplCopyWith<$Res> {
  __$$SuccessImplCopyWithImpl(
      _$SuccessImpl _value, $Res Function(_$SuccessImpl) _then)
      : super(_value, _then);

  /// Create a copy of VoiceInputState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? result = null,
  }) {
    return _then(_$SuccessImpl(
      null == result
          ? _value.result
          : result // ignore: cast_nullable_to_non_nullable
              as VoiceCommandResult,
    ));
  }

  /// Create a copy of VoiceInputState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $VoiceCommandResultCopyWith<$Res> get result {
    return $VoiceCommandResultCopyWith<$Res>(_value.result, (value) {
      return _then(_value.copyWith(result: value));
    });
  }
}

/// @nodoc

class _$SuccessImpl implements _Success {
  const _$SuccessImpl(this.result);

  @override
  final VoiceCommandResult result;

  @override
  String toString() {
    return 'VoiceInputState.success(result: $result)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SuccessImpl &&
            (identical(other.result, result) || other.result == result));
  }

  @override
  int get hashCode => Object.hash(runtimeType, result);

  /// Create a copy of VoiceInputState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SuccessImplCopyWith<_$SuccessImpl> get copyWith =>
      __$$SuccessImplCopyWithImpl<_$SuccessImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() idle,
    required TResult Function() listening,
    required TResult Function(String text) recognizing,
    required TResult Function(String text, double confidence) recognized,
    required TResult Function() processing,
    required TResult Function(VoiceCommandResult result) success,
    required TResult Function() noMatch,
    required TResult Function(String message) error,
    required TResult Function() permissionDenied,
    required TResult Function() requiresSubscription,
  }) {
    return success(result);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? idle,
    TResult? Function()? listening,
    TResult? Function(String text)? recognizing,
    TResult? Function(String text, double confidence)? recognized,
    TResult? Function()? processing,
    TResult? Function(VoiceCommandResult result)? success,
    TResult? Function()? noMatch,
    TResult? Function(String message)? error,
    TResult? Function()? permissionDenied,
    TResult? Function()? requiresSubscription,
  }) {
    return success?.call(result);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? idle,
    TResult Function()? listening,
    TResult Function(String text)? recognizing,
    TResult Function(String text, double confidence)? recognized,
    TResult Function()? processing,
    TResult Function(VoiceCommandResult result)? success,
    TResult Function()? noMatch,
    TResult Function(String message)? error,
    TResult Function()? permissionDenied,
    TResult Function()? requiresSubscription,
    required TResult orElse(),
  }) {
    if (success != null) {
      return success(result);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Idle value) idle,
    required TResult Function(_Listening value) listening,
    required TResult Function(_Recognizing value) recognizing,
    required TResult Function(_Recognized value) recognized,
    required TResult Function(_Processing value) processing,
    required TResult Function(_Success value) success,
    required TResult Function(_NoMatch value) noMatch,
    required TResult Function(_Error value) error,
    required TResult Function(_PermissionDenied value) permissionDenied,
    required TResult Function(_RequiresSubscription value) requiresSubscription,
  }) {
    return success(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Idle value)? idle,
    TResult? Function(_Listening value)? listening,
    TResult? Function(_Recognizing value)? recognizing,
    TResult? Function(_Recognized value)? recognized,
    TResult? Function(_Processing value)? processing,
    TResult? Function(_Success value)? success,
    TResult? Function(_NoMatch value)? noMatch,
    TResult? Function(_Error value)? error,
    TResult? Function(_PermissionDenied value)? permissionDenied,
    TResult? Function(_RequiresSubscription value)? requiresSubscription,
  }) {
    return success?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Idle value)? idle,
    TResult Function(_Listening value)? listening,
    TResult Function(_Recognizing value)? recognizing,
    TResult Function(_Recognized value)? recognized,
    TResult Function(_Processing value)? processing,
    TResult Function(_Success value)? success,
    TResult Function(_NoMatch value)? noMatch,
    TResult Function(_Error value)? error,
    TResult Function(_PermissionDenied value)? permissionDenied,
    TResult Function(_RequiresSubscription value)? requiresSubscription,
    required TResult orElse(),
  }) {
    if (success != null) {
      return success(this);
    }
    return orElse();
  }
}

abstract class _Success implements VoiceInputState {
  const factory _Success(final VoiceCommandResult result) = _$SuccessImpl;

  VoiceCommandResult get result;

  /// Create a copy of VoiceInputState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SuccessImplCopyWith<_$SuccessImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$NoMatchImplCopyWith<$Res> {
  factory _$$NoMatchImplCopyWith(
          _$NoMatchImpl value, $Res Function(_$NoMatchImpl) then) =
      __$$NoMatchImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$NoMatchImplCopyWithImpl<$Res>
    extends _$VoiceInputStateCopyWithImpl<$Res, _$NoMatchImpl>
    implements _$$NoMatchImplCopyWith<$Res> {
  __$$NoMatchImplCopyWithImpl(
      _$NoMatchImpl _value, $Res Function(_$NoMatchImpl) _then)
      : super(_value, _then);

  /// Create a copy of VoiceInputState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$NoMatchImpl implements _NoMatch {
  const _$NoMatchImpl();

  @override
  String toString() {
    return 'VoiceInputState.noMatch()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$NoMatchImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() idle,
    required TResult Function() listening,
    required TResult Function(String text) recognizing,
    required TResult Function(String text, double confidence) recognized,
    required TResult Function() processing,
    required TResult Function(VoiceCommandResult result) success,
    required TResult Function() noMatch,
    required TResult Function(String message) error,
    required TResult Function() permissionDenied,
    required TResult Function() requiresSubscription,
  }) {
    return noMatch();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? idle,
    TResult? Function()? listening,
    TResult? Function(String text)? recognizing,
    TResult? Function(String text, double confidence)? recognized,
    TResult? Function()? processing,
    TResult? Function(VoiceCommandResult result)? success,
    TResult? Function()? noMatch,
    TResult? Function(String message)? error,
    TResult? Function()? permissionDenied,
    TResult? Function()? requiresSubscription,
  }) {
    return noMatch?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? idle,
    TResult Function()? listening,
    TResult Function(String text)? recognizing,
    TResult Function(String text, double confidence)? recognized,
    TResult Function()? processing,
    TResult Function(VoiceCommandResult result)? success,
    TResult Function()? noMatch,
    TResult Function(String message)? error,
    TResult Function()? permissionDenied,
    TResult Function()? requiresSubscription,
    required TResult orElse(),
  }) {
    if (noMatch != null) {
      return noMatch();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Idle value) idle,
    required TResult Function(_Listening value) listening,
    required TResult Function(_Recognizing value) recognizing,
    required TResult Function(_Recognized value) recognized,
    required TResult Function(_Processing value) processing,
    required TResult Function(_Success value) success,
    required TResult Function(_NoMatch value) noMatch,
    required TResult Function(_Error value) error,
    required TResult Function(_PermissionDenied value) permissionDenied,
    required TResult Function(_RequiresSubscription value) requiresSubscription,
  }) {
    return noMatch(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Idle value)? idle,
    TResult? Function(_Listening value)? listening,
    TResult? Function(_Recognizing value)? recognizing,
    TResult? Function(_Recognized value)? recognized,
    TResult? Function(_Processing value)? processing,
    TResult? Function(_Success value)? success,
    TResult? Function(_NoMatch value)? noMatch,
    TResult? Function(_Error value)? error,
    TResult? Function(_PermissionDenied value)? permissionDenied,
    TResult? Function(_RequiresSubscription value)? requiresSubscription,
  }) {
    return noMatch?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Idle value)? idle,
    TResult Function(_Listening value)? listening,
    TResult Function(_Recognizing value)? recognizing,
    TResult Function(_Recognized value)? recognized,
    TResult Function(_Processing value)? processing,
    TResult Function(_Success value)? success,
    TResult Function(_NoMatch value)? noMatch,
    TResult Function(_Error value)? error,
    TResult Function(_PermissionDenied value)? permissionDenied,
    TResult Function(_RequiresSubscription value)? requiresSubscription,
    required TResult orElse(),
  }) {
    if (noMatch != null) {
      return noMatch(this);
    }
    return orElse();
  }
}

abstract class _NoMatch implements VoiceInputState {
  const factory _NoMatch() = _$NoMatchImpl;
}

/// @nodoc
abstract class _$$ErrorImplCopyWith<$Res> {
  factory _$$ErrorImplCopyWith(
          _$ErrorImpl value, $Res Function(_$ErrorImpl) then) =
      __$$ErrorImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String message});
}

/// @nodoc
class __$$ErrorImplCopyWithImpl<$Res>
    extends _$VoiceInputStateCopyWithImpl<$Res, _$ErrorImpl>
    implements _$$ErrorImplCopyWith<$Res> {
  __$$ErrorImplCopyWithImpl(
      _$ErrorImpl _value, $Res Function(_$ErrorImpl) _then)
      : super(_value, _then);

  /// Create a copy of VoiceInputState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
  }) {
    return _then(_$ErrorImpl(
      null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$ErrorImpl implements _Error {
  const _$ErrorImpl(this.message);

  @override
  final String message;

  @override
  String toString() {
    return 'VoiceInputState.error(message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ErrorImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  /// Create a copy of VoiceInputState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ErrorImplCopyWith<_$ErrorImpl> get copyWith =>
      __$$ErrorImplCopyWithImpl<_$ErrorImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() idle,
    required TResult Function() listening,
    required TResult Function(String text) recognizing,
    required TResult Function(String text, double confidence) recognized,
    required TResult Function() processing,
    required TResult Function(VoiceCommandResult result) success,
    required TResult Function() noMatch,
    required TResult Function(String message) error,
    required TResult Function() permissionDenied,
    required TResult Function() requiresSubscription,
  }) {
    return error(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? idle,
    TResult? Function()? listening,
    TResult? Function(String text)? recognizing,
    TResult? Function(String text, double confidence)? recognized,
    TResult? Function()? processing,
    TResult? Function(VoiceCommandResult result)? success,
    TResult? Function()? noMatch,
    TResult? Function(String message)? error,
    TResult? Function()? permissionDenied,
    TResult? Function()? requiresSubscription,
  }) {
    return error?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? idle,
    TResult Function()? listening,
    TResult Function(String text)? recognizing,
    TResult Function(String text, double confidence)? recognized,
    TResult Function()? processing,
    TResult Function(VoiceCommandResult result)? success,
    TResult Function()? noMatch,
    TResult Function(String message)? error,
    TResult Function()? permissionDenied,
    TResult Function()? requiresSubscription,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Idle value) idle,
    required TResult Function(_Listening value) listening,
    required TResult Function(_Recognizing value) recognizing,
    required TResult Function(_Recognized value) recognized,
    required TResult Function(_Processing value) processing,
    required TResult Function(_Success value) success,
    required TResult Function(_NoMatch value) noMatch,
    required TResult Function(_Error value) error,
    required TResult Function(_PermissionDenied value) permissionDenied,
    required TResult Function(_RequiresSubscription value) requiresSubscription,
  }) {
    return error(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Idle value)? idle,
    TResult? Function(_Listening value)? listening,
    TResult? Function(_Recognizing value)? recognizing,
    TResult? Function(_Recognized value)? recognized,
    TResult? Function(_Processing value)? processing,
    TResult? Function(_Success value)? success,
    TResult? Function(_NoMatch value)? noMatch,
    TResult? Function(_Error value)? error,
    TResult? Function(_PermissionDenied value)? permissionDenied,
    TResult? Function(_RequiresSubscription value)? requiresSubscription,
  }) {
    return error?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Idle value)? idle,
    TResult Function(_Listening value)? listening,
    TResult Function(_Recognizing value)? recognizing,
    TResult Function(_Recognized value)? recognized,
    TResult Function(_Processing value)? processing,
    TResult Function(_Success value)? success,
    TResult Function(_NoMatch value)? noMatch,
    TResult Function(_Error value)? error,
    TResult Function(_PermissionDenied value)? permissionDenied,
    TResult Function(_RequiresSubscription value)? requiresSubscription,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(this);
    }
    return orElse();
  }
}

abstract class _Error implements VoiceInputState {
  const factory _Error(final String message) = _$ErrorImpl;

  String get message;

  /// Create a copy of VoiceInputState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ErrorImplCopyWith<_$ErrorImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$PermissionDeniedImplCopyWith<$Res> {
  factory _$$PermissionDeniedImplCopyWith(_$PermissionDeniedImpl value,
          $Res Function(_$PermissionDeniedImpl) then) =
      __$$PermissionDeniedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$PermissionDeniedImplCopyWithImpl<$Res>
    extends _$VoiceInputStateCopyWithImpl<$Res, _$PermissionDeniedImpl>
    implements _$$PermissionDeniedImplCopyWith<$Res> {
  __$$PermissionDeniedImplCopyWithImpl(_$PermissionDeniedImpl _value,
      $Res Function(_$PermissionDeniedImpl) _then)
      : super(_value, _then);

  /// Create a copy of VoiceInputState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$PermissionDeniedImpl implements _PermissionDenied {
  const _$PermissionDeniedImpl();

  @override
  String toString() {
    return 'VoiceInputState.permissionDenied()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$PermissionDeniedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() idle,
    required TResult Function() listening,
    required TResult Function(String text) recognizing,
    required TResult Function(String text, double confidence) recognized,
    required TResult Function() processing,
    required TResult Function(VoiceCommandResult result) success,
    required TResult Function() noMatch,
    required TResult Function(String message) error,
    required TResult Function() permissionDenied,
    required TResult Function() requiresSubscription,
  }) {
    return permissionDenied();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? idle,
    TResult? Function()? listening,
    TResult? Function(String text)? recognizing,
    TResult? Function(String text, double confidence)? recognized,
    TResult? Function()? processing,
    TResult? Function(VoiceCommandResult result)? success,
    TResult? Function()? noMatch,
    TResult? Function(String message)? error,
    TResult? Function()? permissionDenied,
    TResult? Function()? requiresSubscription,
  }) {
    return permissionDenied?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? idle,
    TResult Function()? listening,
    TResult Function(String text)? recognizing,
    TResult Function(String text, double confidence)? recognized,
    TResult Function()? processing,
    TResult Function(VoiceCommandResult result)? success,
    TResult Function()? noMatch,
    TResult Function(String message)? error,
    TResult Function()? permissionDenied,
    TResult Function()? requiresSubscription,
    required TResult orElse(),
  }) {
    if (permissionDenied != null) {
      return permissionDenied();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Idle value) idle,
    required TResult Function(_Listening value) listening,
    required TResult Function(_Recognizing value) recognizing,
    required TResult Function(_Recognized value) recognized,
    required TResult Function(_Processing value) processing,
    required TResult Function(_Success value) success,
    required TResult Function(_NoMatch value) noMatch,
    required TResult Function(_Error value) error,
    required TResult Function(_PermissionDenied value) permissionDenied,
    required TResult Function(_RequiresSubscription value) requiresSubscription,
  }) {
    return permissionDenied(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Idle value)? idle,
    TResult? Function(_Listening value)? listening,
    TResult? Function(_Recognizing value)? recognizing,
    TResult? Function(_Recognized value)? recognized,
    TResult? Function(_Processing value)? processing,
    TResult? Function(_Success value)? success,
    TResult? Function(_NoMatch value)? noMatch,
    TResult? Function(_Error value)? error,
    TResult? Function(_PermissionDenied value)? permissionDenied,
    TResult? Function(_RequiresSubscription value)? requiresSubscription,
  }) {
    return permissionDenied?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Idle value)? idle,
    TResult Function(_Listening value)? listening,
    TResult Function(_Recognizing value)? recognizing,
    TResult Function(_Recognized value)? recognized,
    TResult Function(_Processing value)? processing,
    TResult Function(_Success value)? success,
    TResult Function(_NoMatch value)? noMatch,
    TResult Function(_Error value)? error,
    TResult Function(_PermissionDenied value)? permissionDenied,
    TResult Function(_RequiresSubscription value)? requiresSubscription,
    required TResult orElse(),
  }) {
    if (permissionDenied != null) {
      return permissionDenied(this);
    }
    return orElse();
  }
}

abstract class _PermissionDenied implements VoiceInputState {
  const factory _PermissionDenied() = _$PermissionDeniedImpl;
}

/// @nodoc
abstract class _$$RequiresSubscriptionImplCopyWith<$Res> {
  factory _$$RequiresSubscriptionImplCopyWith(_$RequiresSubscriptionImpl value,
          $Res Function(_$RequiresSubscriptionImpl) then) =
      __$$RequiresSubscriptionImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$RequiresSubscriptionImplCopyWithImpl<$Res>
    extends _$VoiceInputStateCopyWithImpl<$Res, _$RequiresSubscriptionImpl>
    implements _$$RequiresSubscriptionImplCopyWith<$Res> {
  __$$RequiresSubscriptionImplCopyWithImpl(_$RequiresSubscriptionImpl _value,
      $Res Function(_$RequiresSubscriptionImpl) _then)
      : super(_value, _then);

  /// Create a copy of VoiceInputState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$RequiresSubscriptionImpl implements _RequiresSubscription {
  const _$RequiresSubscriptionImpl();

  @override
  String toString() {
    return 'VoiceInputState.requiresSubscription()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RequiresSubscriptionImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() idle,
    required TResult Function() listening,
    required TResult Function(String text) recognizing,
    required TResult Function(String text, double confidence) recognized,
    required TResult Function() processing,
    required TResult Function(VoiceCommandResult result) success,
    required TResult Function() noMatch,
    required TResult Function(String message) error,
    required TResult Function() permissionDenied,
    required TResult Function() requiresSubscription,
  }) {
    return requiresSubscription();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? idle,
    TResult? Function()? listening,
    TResult? Function(String text)? recognizing,
    TResult? Function(String text, double confidence)? recognized,
    TResult? Function()? processing,
    TResult? Function(VoiceCommandResult result)? success,
    TResult? Function()? noMatch,
    TResult? Function(String message)? error,
    TResult? Function()? permissionDenied,
    TResult? Function()? requiresSubscription,
  }) {
    return requiresSubscription?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? idle,
    TResult Function()? listening,
    TResult Function(String text)? recognizing,
    TResult Function(String text, double confidence)? recognized,
    TResult Function()? processing,
    TResult Function(VoiceCommandResult result)? success,
    TResult Function()? noMatch,
    TResult Function(String message)? error,
    TResult Function()? permissionDenied,
    TResult Function()? requiresSubscription,
    required TResult orElse(),
  }) {
    if (requiresSubscription != null) {
      return requiresSubscription();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Idle value) idle,
    required TResult Function(_Listening value) listening,
    required TResult Function(_Recognizing value) recognizing,
    required TResult Function(_Recognized value) recognized,
    required TResult Function(_Processing value) processing,
    required TResult Function(_Success value) success,
    required TResult Function(_NoMatch value) noMatch,
    required TResult Function(_Error value) error,
    required TResult Function(_PermissionDenied value) permissionDenied,
    required TResult Function(_RequiresSubscription value) requiresSubscription,
  }) {
    return requiresSubscription(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Idle value)? idle,
    TResult? Function(_Listening value)? listening,
    TResult? Function(_Recognizing value)? recognizing,
    TResult? Function(_Recognized value)? recognized,
    TResult? Function(_Processing value)? processing,
    TResult? Function(_Success value)? success,
    TResult? Function(_NoMatch value)? noMatch,
    TResult? Function(_Error value)? error,
    TResult? Function(_PermissionDenied value)? permissionDenied,
    TResult? Function(_RequiresSubscription value)? requiresSubscription,
  }) {
    return requiresSubscription?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Idle value)? idle,
    TResult Function(_Listening value)? listening,
    TResult Function(_Recognizing value)? recognizing,
    TResult Function(_Recognized value)? recognized,
    TResult Function(_Processing value)? processing,
    TResult Function(_Success value)? success,
    TResult Function(_NoMatch value)? noMatch,
    TResult Function(_Error value)? error,
    TResult Function(_PermissionDenied value)? permissionDenied,
    TResult Function(_RequiresSubscription value)? requiresSubscription,
    required TResult orElse(),
  }) {
    if (requiresSubscription != null) {
      return requiresSubscription(this);
    }
    return orElse();
  }
}

abstract class _RequiresSubscription implements VoiceInputState {
  const factory _RequiresSubscription() = _$RequiresSubscriptionImpl;
}
