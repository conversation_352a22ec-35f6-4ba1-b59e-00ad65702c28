import 'dart:async';
import 'dart:io';
import 'package:record/record.dart';
import '../entities/voice_recording.dart';

/// Repository interface for voice recording operations
abstract class VoiceRecordingRepository {
  /// Start a new voice recording session
  Future<VoiceRecording> startRecording({
    required String languageCode,
    Map<String, dynamic>? metadata,
  });

  /// Stop the current recording session
  Future<VoiceRecording> stopRecording(String recordingId);

  /// Cancel the current recording session
  Future<void> cancelRecording(String recordingId);

  /// Get recording by ID
  Future<VoiceRecording?> getRecording(String recordingId);

  /// Get the audio file for a recording
  Future<File?> getRecordingFile(String recordingId);

  /// Clean up temporary recording files
  Future<void> cleanupTempFiles();

  /// Check if microphone permission is granted
  Future<bool> hasPermission();

  /// Request microphone permission
  Future<bool> requestPermission();

  /// Test microphone functionality
  Future<bool> testMicrophone();

  /// Disposes of any resources used by the repository.
  Future<void> dispose();

  Stream<Amplitude> get amplitudeStream;
}
