// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'voice_recording.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

VoiceRecording _$VoiceRecordingFromJson(Map<String, dynamic> json) {
  return _VoiceRecording.fromJson(json);
}

/// @nodoc
mixin _$VoiceRecording {
  String get id => throw _privateConstructorUsedError;
  String get filePath => throw _privateConstructorUsedError;
  DateTime get startTime => throw _privateConstructorUsedError;
  DateTime get endTime => throw _privateConstructorUsedError;
  int get durationMs => throw _privateConstructorUsedError;
  int get fileSizeBytes => throw _privateConstructorUsedError;
  String get languageCode => throw _privateConstructorUsedError;
  VoiceRecordingStatus get status => throw _privateConstructorUsedError;
  String? get transcription => throw _privateConstructorUsedError;
  double? get confidence => throw _privateConstructorUsedError;
  String? get errorMessage => throw _privateConstructorUsedError;
  Map<String, dynamic>? get metadata => throw _privateConstructorUsedError;

  /// Serializes this VoiceRecording to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of VoiceRecording
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $VoiceRecordingCopyWith<VoiceRecording> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $VoiceRecordingCopyWith<$Res> {
  factory $VoiceRecordingCopyWith(
          VoiceRecording value, $Res Function(VoiceRecording) then) =
      _$VoiceRecordingCopyWithImpl<$Res, VoiceRecording>;
  @useResult
  $Res call(
      {String id,
      String filePath,
      DateTime startTime,
      DateTime endTime,
      int durationMs,
      int fileSizeBytes,
      String languageCode,
      VoiceRecordingStatus status,
      String? transcription,
      double? confidence,
      String? errorMessage,
      Map<String, dynamic>? metadata});
}

/// @nodoc
class _$VoiceRecordingCopyWithImpl<$Res, $Val extends VoiceRecording>
    implements $VoiceRecordingCopyWith<$Res> {
  _$VoiceRecordingCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of VoiceRecording
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? filePath = null,
    Object? startTime = null,
    Object? endTime = null,
    Object? durationMs = null,
    Object? fileSizeBytes = null,
    Object? languageCode = null,
    Object? status = null,
    Object? transcription = freezed,
    Object? confidence = freezed,
    Object? errorMessage = freezed,
    Object? metadata = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      filePath: null == filePath
          ? _value.filePath
          : filePath // ignore: cast_nullable_to_non_nullable
              as String,
      startTime: null == startTime
          ? _value.startTime
          : startTime // ignore: cast_nullable_to_non_nullable
              as DateTime,
      endTime: null == endTime
          ? _value.endTime
          : endTime // ignore: cast_nullable_to_non_nullable
              as DateTime,
      durationMs: null == durationMs
          ? _value.durationMs
          : durationMs // ignore: cast_nullable_to_non_nullable
              as int,
      fileSizeBytes: null == fileSizeBytes
          ? _value.fileSizeBytes
          : fileSizeBytes // ignore: cast_nullable_to_non_nullable
              as int,
      languageCode: null == languageCode
          ? _value.languageCode
          : languageCode // ignore: cast_nullable_to_non_nullable
              as String,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as VoiceRecordingStatus,
      transcription: freezed == transcription
          ? _value.transcription
          : transcription // ignore: cast_nullable_to_non_nullable
              as String?,
      confidence: freezed == confidence
          ? _value.confidence
          : confidence // ignore: cast_nullable_to_non_nullable
              as double?,
      errorMessage: freezed == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      metadata: freezed == metadata
          ? _value.metadata
          : metadata // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$VoiceRecordingImplCopyWith<$Res>
    implements $VoiceRecordingCopyWith<$Res> {
  factory _$$VoiceRecordingImplCopyWith(_$VoiceRecordingImpl value,
          $Res Function(_$VoiceRecordingImpl) then) =
      __$$VoiceRecordingImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String filePath,
      DateTime startTime,
      DateTime endTime,
      int durationMs,
      int fileSizeBytes,
      String languageCode,
      VoiceRecordingStatus status,
      String? transcription,
      double? confidence,
      String? errorMessage,
      Map<String, dynamic>? metadata});
}

/// @nodoc
class __$$VoiceRecordingImplCopyWithImpl<$Res>
    extends _$VoiceRecordingCopyWithImpl<$Res, _$VoiceRecordingImpl>
    implements _$$VoiceRecordingImplCopyWith<$Res> {
  __$$VoiceRecordingImplCopyWithImpl(
      _$VoiceRecordingImpl _value, $Res Function(_$VoiceRecordingImpl) _then)
      : super(_value, _then);

  /// Create a copy of VoiceRecording
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? filePath = null,
    Object? startTime = null,
    Object? endTime = null,
    Object? durationMs = null,
    Object? fileSizeBytes = null,
    Object? languageCode = null,
    Object? status = null,
    Object? transcription = freezed,
    Object? confidence = freezed,
    Object? errorMessage = freezed,
    Object? metadata = freezed,
  }) {
    return _then(_$VoiceRecordingImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      filePath: null == filePath
          ? _value.filePath
          : filePath // ignore: cast_nullable_to_non_nullable
              as String,
      startTime: null == startTime
          ? _value.startTime
          : startTime // ignore: cast_nullable_to_non_nullable
              as DateTime,
      endTime: null == endTime
          ? _value.endTime
          : endTime // ignore: cast_nullable_to_non_nullable
              as DateTime,
      durationMs: null == durationMs
          ? _value.durationMs
          : durationMs // ignore: cast_nullable_to_non_nullable
              as int,
      fileSizeBytes: null == fileSizeBytes
          ? _value.fileSizeBytes
          : fileSizeBytes // ignore: cast_nullable_to_non_nullable
              as int,
      languageCode: null == languageCode
          ? _value.languageCode
          : languageCode // ignore: cast_nullable_to_non_nullable
              as String,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as VoiceRecordingStatus,
      transcription: freezed == transcription
          ? _value.transcription
          : transcription // ignore: cast_nullable_to_non_nullable
              as String?,
      confidence: freezed == confidence
          ? _value.confidence
          : confidence // ignore: cast_nullable_to_non_nullable
              as double?,
      errorMessage: freezed == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      metadata: freezed == metadata
          ? _value._metadata
          : metadata // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$VoiceRecordingImpl extends _VoiceRecording {
  const _$VoiceRecordingImpl(
      {required this.id,
      required this.filePath,
      required this.startTime,
      required this.endTime,
      required this.durationMs,
      required this.fileSizeBytes,
      required this.languageCode,
      this.status = VoiceRecordingStatus.pending,
      this.transcription,
      this.confidence,
      this.errorMessage,
      final Map<String, dynamic>? metadata})
      : _metadata = metadata,
        super._();

  factory _$VoiceRecordingImpl.fromJson(Map<String, dynamic> json) =>
      _$$VoiceRecordingImplFromJson(json);

  @override
  final String id;
  @override
  final String filePath;
  @override
  final DateTime startTime;
  @override
  final DateTime endTime;
  @override
  final int durationMs;
  @override
  final int fileSizeBytes;
  @override
  final String languageCode;
  @override
  @JsonKey()
  final VoiceRecordingStatus status;
  @override
  final String? transcription;
  @override
  final double? confidence;
  @override
  final String? errorMessage;
  final Map<String, dynamic>? _metadata;
  @override
  Map<String, dynamic>? get metadata {
    final value = _metadata;
    if (value == null) return null;
    if (_metadata is EqualUnmodifiableMapView) return _metadata;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  String toString() {
    return 'VoiceRecording(id: $id, filePath: $filePath, startTime: $startTime, endTime: $endTime, durationMs: $durationMs, fileSizeBytes: $fileSizeBytes, languageCode: $languageCode, status: $status, transcription: $transcription, confidence: $confidence, errorMessage: $errorMessage, metadata: $metadata)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$VoiceRecordingImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.filePath, filePath) ||
                other.filePath == filePath) &&
            (identical(other.startTime, startTime) ||
                other.startTime == startTime) &&
            (identical(other.endTime, endTime) || other.endTime == endTime) &&
            (identical(other.durationMs, durationMs) ||
                other.durationMs == durationMs) &&
            (identical(other.fileSizeBytes, fileSizeBytes) ||
                other.fileSizeBytes == fileSizeBytes) &&
            (identical(other.languageCode, languageCode) ||
                other.languageCode == languageCode) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.transcription, transcription) ||
                other.transcription == transcription) &&
            (identical(other.confidence, confidence) ||
                other.confidence == confidence) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage) &&
            const DeepCollectionEquality().equals(other._metadata, _metadata));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      filePath,
      startTime,
      endTime,
      durationMs,
      fileSizeBytes,
      languageCode,
      status,
      transcription,
      confidence,
      errorMessage,
      const DeepCollectionEquality().hash(_metadata));

  /// Create a copy of VoiceRecording
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$VoiceRecordingImplCopyWith<_$VoiceRecordingImpl> get copyWith =>
      __$$VoiceRecordingImplCopyWithImpl<_$VoiceRecordingImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$VoiceRecordingImplToJson(
      this,
    );
  }
}

abstract class _VoiceRecording extends VoiceRecording {
  const factory _VoiceRecording(
      {required final String id,
      required final String filePath,
      required final DateTime startTime,
      required final DateTime endTime,
      required final int durationMs,
      required final int fileSizeBytes,
      required final String languageCode,
      final VoiceRecordingStatus status,
      final String? transcription,
      final double? confidence,
      final String? errorMessage,
      final Map<String, dynamic>? metadata}) = _$VoiceRecordingImpl;
  const _VoiceRecording._() : super._();

  factory _VoiceRecording.fromJson(Map<String, dynamic> json) =
      _$VoiceRecordingImpl.fromJson;

  @override
  String get id;
  @override
  String get filePath;
  @override
  DateTime get startTime;
  @override
  DateTime get endTime;
  @override
  int get durationMs;
  @override
  int get fileSizeBytes;
  @override
  String get languageCode;
  @override
  VoiceRecordingStatus get status;
  @override
  String? get transcription;
  @override
  double? get confidence;
  @override
  String? get errorMessage;
  @override
  Map<String, dynamic>? get metadata;

  /// Create a copy of VoiceRecording
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$VoiceRecordingImplCopyWith<_$VoiceRecordingImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
