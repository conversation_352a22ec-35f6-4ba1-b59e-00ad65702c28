import 'package:freezed_annotation/freezed_annotation.dart';

part 'voice_recording.freezed.dart';
part 'voice_recording.g.dart';

/// Domain entity representing a voice recording
@freezed
class VoiceRecording with _$VoiceRecording {
  const factory VoiceRecording({
    required String id,
    required String filePath,
    required DateTime startTime,
    required DateTime endTime,
    required int durationMs,
    required int fileSizeBytes,
    required String languageCode,
    @Default(VoiceRecordingStatus.pending) VoiceRecordingStatus status,
    String? transcription,
    double? confidence,
    String? errorMessage,
    Map<String, dynamic>? metadata,
  }) = _VoiceRecording;

  factory VoiceRecording.fromJson(Map<String, dynamic> json) =>
      _$VoiceRecordingFromJson(json);

  const VoiceRecording._();

  /// Check if recording is valid for processing
  bool get isValidForProcessing {
    // For speech-to-text, we're more flexible about file size
    // and focus on whether we have actual transcribed text
    final hasMinimumDuration = durationMs >= 300;
    final hasContent =
        fileSizeBytes > 10 || // Very small threshold for text files
            (transcription != null && transcription!.trim().isNotEmpty);
    final isCompleted = status == VoiceRecordingStatus.completed;

    return hasMinimumDuration && hasContent && isCompleted;
  }

  /// Get recording duration in seconds
  double get durationSeconds => durationMs / 1000.0;

  /// Check if recording is too short
  bool get isTooShort => durationMs < 300; // Reduced from 500ms

  /// Check if recording is too long
  bool get isTooLong => durationMs > 30000; // 30 seconds max

  /// Get detailed validation info for debugging
  String get validationInfo => 'Duration: ${durationMs}ms (min: 300ms), '
      'Size: ${fileSizeBytes}bytes (min: 10bytes), '
      'Status: $status, '
      'Transcription: ${transcription?.isNotEmpty == true ? '"${transcription!.substring(0, transcription!.length > 50 ? 50 : transcription!.length)}..."' : 'empty'}';
}

/// Status of a voice recording
enum VoiceRecordingStatus {
  @JsonValue('pending')
  pending,
  @JsonValue('recording')
  recording,
  @JsonValue('completed')
  completed,
  @JsonValue('processing')
  processing,
  @JsonValue('transcribed')
  transcribed,
  @JsonValue('failed')
  failed,
  @JsonValue('cancelled')
  cancelled,
}
