// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'voice_recording.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$VoiceRecordingImpl _$$VoiceRecordingImplFromJson(Map<String, dynamic> json) =>
    _$VoiceRecordingImpl(
      id: json['id'] as String,
      filePath: json['filePath'] as String,
      startTime: DateTime.parse(json['startTime'] as String),
      endTime: DateTime.parse(json['endTime'] as String),
      durationMs: (json['durationMs'] as num).toInt(),
      fileSizeBytes: (json['fileSizeBytes'] as num).toInt(),
      languageCode: json['languageCode'] as String,
      status:
          $enumDecodeNullable(_$VoiceRecordingStatusEnumMap, json['status']) ??
              VoiceRecordingStatus.pending,
      transcription: json['transcription'] as String?,
      confidence: (json['confidence'] as num?)?.toDouble(),
      errorMessage: json['errorMessage'] as String?,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$$VoiceRecordingImplToJson(
        _$VoiceRecordingImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'filePath': instance.filePath,
      'startTime': instance.startTime.toIso8601String(),
      'endTime': instance.endTime.toIso8601String(),
      'durationMs': instance.durationMs,
      'fileSizeBytes': instance.fileSizeBytes,
      'languageCode': instance.languageCode,
      'status': _$VoiceRecordingStatusEnumMap[instance.status]!,
      'transcription': instance.transcription,
      'confidence': instance.confidence,
      'errorMessage': instance.errorMessage,
      'metadata': instance.metadata,
    };

const _$VoiceRecordingStatusEnumMap = {
  VoiceRecordingStatus.pending: 'pending',
  VoiceRecordingStatus.recording: 'recording',
  VoiceRecordingStatus.completed: 'completed',
  VoiceRecordingStatus.processing: 'processing',
  VoiceRecordingStatus.transcribed: 'transcribed',
  VoiceRecordingStatus.failed: 'failed',
  VoiceRecordingStatus.cancelled: 'cancelled',
};
