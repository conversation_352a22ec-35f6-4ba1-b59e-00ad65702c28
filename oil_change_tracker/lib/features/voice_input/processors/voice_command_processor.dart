import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/voice_command_result.dart';
import 'dart:developer' as dev;
import 'package:flutter/material.dart';
import 'package:oil_change_tracker/features/car_management/providers/car_provider.dart';
import 'package:oil_change_tracker/core/models/car_model.dart';

/// Provider for the VoiceCommandProcessor
final voiceCommandProcessorProvider = Provider<VoiceCommandProcessor>((ref) {
  return VoiceCommandProcessor(ref);
});

/// Class to process voice commands and extract information
class VoiceCommandProcessor {
  final ProviderRef _ref;

  /// Creates a processor for voice commands
  VoiceCommandProcessor(this._ref);

  /// Process a voice command and extract information
  Future<VoiceCommandResult?> processCommand(String text,
      {Locale? locale}) async {
    // Determine the language
    final isArabic = locale?.languageCode == 'ar';

    // Normalize text for processing
    final normalizedText = text.toLowerCase().trim();

    // Try to match oil change command
    if (_matchesOilChange(normalizedText, isArabic)) {
      return await _processOilChangeCommand(normalizedText);
    }

    // Try to match maintenance command
    if (_matchesMaintenance(normalizedText, isArabic)) {
      return await _processMaintenanceCommand(normalizedText);
    }

    // Try to match view car command
    if (_matchesViewCar(normalizedText, isArabic)) {
      return await _processViewCarCommand(normalizedText);
    }

    // No match found
    dev.log('VoiceCommandProcessor: No command matched for "$text"');
    return null;
  }

  /// Check if text matches an oil change command
  bool _matchesOilChange(String text, bool isArabic) {
    if (isArabic) {
      return text.contains('تغيير الزيت') ||
          text.contains('زيت') && text.contains('تغيير');
    }

    return text.contains('oil change') ||
        text.contains('oil') && text.contains('change');
  }

  /// Check if text matches a maintenance command
  bool _matchesMaintenance(String text, bool isArabic) {
    if (isArabic) {
      return text.contains('صيانة') ||
          text.contains('إصلاح') ||
          text.contains('خدمة');
    }

    return text.contains('maintenance') ||
        text.contains('repair') ||
        text.contains('service');
  }

  /// Check if text matches a view car command
  bool _matchesViewCar(String text, bool isArabic) {
    if (isArabic) {
      return text.contains('عرض السيارة') ||
          text.contains('إظهار السيارة') ||
          text.contains('فتح السيارة');
    }

    return text.contains('view car') ||
        text.contains('show car') ||
        text.contains('open car');
  }

  /// Process an oil change command
  Future<VoiceCommandResult> _processOilChangeCommand(String text) async {
    // Extract car information
    final carInfo = await _extractCarInfo(text);

    // Extract mileage information
    final mileage = _extractMileage(text);

    // Extract oil type information
    final oilType = _extractOilType(text);

    // Extract oil quantity information
    final oilQuantity = _extractOilQuantity(text);

    // Extract filter information
    final filterInfo = _extractFilterInfo(text);

    // Extract cost information
    final costInfo = _extractCostInfo(text);

    // Extract notes
    final notes = _extractNotes(text);

    return VoiceCommandResult(
      commandType: VoiceCommandType.addOilChange,
      confidence: 0.85,
      originalText: text,
      targetRoute: '/oil-changes/add',
      parameters: {
        'carId': carInfo,
        if (mileage != null) 'mileage': mileage,
        if (oilType != null) 'oilType': oilType,
        if (oilQuantity != null) 'oilQuantity': oilQuantity,
        if (filterInfo['changed'] == true) 'filterChanged': true,
        if (filterInfo['type'] != null) 'filterType': filterInfo['type'],
        if (costInfo['oil'] != null) 'oilCost': costInfo['oil'],
        if (costInfo['filter'] != null) 'filterCost': costInfo['filter'],
        if (notes != null) 'notes': notes,
        'date': DateTime.now(),
      },
    );
  }

  /// Process a maintenance command
  Future<VoiceCommandResult> _processMaintenanceCommand(String text) async {
    // Extract car information
    final carInfo = await _extractCarInfo(text);

    // Extract maintenance type
    final maintenanceType = _extractMaintenanceType(text);

    // Extract mileage information
    final mileage = _extractMileage(text);

    // Extract cost information
    final costInfo = _extractCostInfo(text);

    // Extract service provider
    final serviceProvider = _extractServiceProvider(text);

    // Extract description
    final description = _extractMaintenanceDescription(text);

    // Extract notes
    final notes = _extractNotes(text);

    return VoiceCommandResult(
      commandType: VoiceCommandType.addMaintenance,
      confidence: 0.8,
      originalText: text,
      targetRoute: '/maintenance/add',
      parameters: {
        'carId': carInfo,
        'maintenanceType': maintenanceType,
        if (mileage != null) 'mileage': mileage,
        if (costInfo['total'] != null) 'cost': costInfo['total'],
        if (serviceProvider != null) 'serviceProvider': serviceProvider,
        if (description != null) 'description': description,
        if (notes != null) 'notes': notes,
        'date': DateTime.now(),
      },
    );
  }

  /// Process a view car command
  Future<VoiceCommandResult> _processViewCarCommand(String text) async {
    // Extract car information
    final carInfo = await _extractCarInfo(text);

    return VoiceCommandResult(
      commandType: VoiceCommandType.viewCarDetails,
      confidence: 0.75,
      originalText: text,
      targetRoute: '/car-details',
      parameters: {
        'carId': carInfo,
      },
    );
  }

  /// Extract car information from text
  Future<String> _extractCarInfo(String text) async {
    // Normalize text for better matching
    final normalizedText = text.toLowerCase().trim();
    dev.log('VoiceCommandProcessor: Extracting car info from: $normalizedText');

    // Try to match user's actual cars first
    final carsAsync = _ref.read(carsProvider);

    final carsData = carsAsync.maybeWhen(
            data: (c) => c, orElse: () => <CarModel>[]) ??
        await _ref.read(carsProvider.future).catchError((_) => <CarModel>[]);

    if (carsData.isNotEmpty) {
      // Log available cars for debugging
      dev.log(
          'VoiceCommandProcessor: User has ${carsData.length} cars available for matching');
      for (final car in carsData) {
        dev.log(
            'VoiceCommandProcessor: Available car: ${car.make} ${car.model} (${car.id})');
      }

      // First try to match by make and model together
      for (final car in carsData) {
        // Create variations of make+model in different formats
        final variations = [
          '${car.make} ${car.model}'.toLowerCase(),
          '${car.model} ${car.make}'.toLowerCase(),
          car.make.toLowerCase(),
          car.model.toLowerCase(),
          // Arabic specific patterns
          'سيارة ${car.make}'.toLowerCase(),
          'سيارة ${car.model}'.toLowerCase(),
          'عربية ${car.make}'.toLowerCase(),
          'عربية ${car.model}'.toLowerCase(),
          'سيارتي ${car.make}'.toLowerCase(),
          'سيارتي ${car.model}'.toLowerCase(),
          // Car with "the" prefix in Arabic
          'السيارة ${car.make}'.toLowerCase(),
          'السيارة ${car.model}'.toLowerCase(),
          'العربية ${car.make}'.toLowerCase(),
          'العربية ${car.model}'.toLowerCase(),
        ];

        // Try each variation
        for (final variation in variations) {
          if (normalizedText.contains(variation)) {
            dev.log(
                'VoiceCommandProcessor: Matched user car: ${car.make} ${car.model} with pattern "$variation"');
            return car.id ?? 'unknown';
          }
        }
      }

      // If we have cars but no match, return the first car's ID
      dev.log(
          'VoiceCommandProcessor: No specific car match found, defaulting to first car: ${carsData[0].make} ${carsData[0].model}');
      return carsData[0].id ?? 'unknown';
    }

    // Fallback to common car brands if we couldn't match user cars
    final carBrands = {
      // English car brands
      'toyota': 'toyota',
      'honda': 'honda',
      'ford': 'ford',
      'chevrolet': 'chevrolet',
      'chevy': 'chevrolet',
      'bmw': 'bmw',
      'audi': 'audi',
      'mercedes': 'mercedes',
      'hyundai': 'hyundai',
      'kia': 'kia',
      'nissan': 'nissan',
      'mazda': 'mazda',
      'volkswagen': 'volkswagen',
      'vw': 'volkswagen',
      'subaru': 'subaru',
      'lexus': 'lexus',
      'acura': 'acura',
      'infiniti': 'infiniti',
      'jeep': 'jeep',
      'dodge': 'dodge',
      'chrysler': 'chrysler',
      'tesla': 'tesla',
      'volvo': 'volvo',
      'land rover': 'land rover',
      'range rover': 'range rover',
      'jaguar': 'jaguar',
      'porsche': 'porsche',
      'ferrari': 'ferrari',
      'lamborghini': 'lamborghini',
      'maserati': 'maserati',
      'bentley': 'bentley',
      'rolls royce': 'rolls royce',

      // Arabic car brands
      'تويوتا': 'toyota',
      'هوندا': 'honda',
      'فورد': 'ford',
      'شيفروليه': 'chevrolet',
      'شيفروليت': 'chevrolet',
      'بي ام دبليو': 'bmw',
      'بي إم دبليو': 'bmw',
      'أودي': 'audi',
      'اودي': 'audi',
      'مرسيدس': 'mercedes',
      'هيونداي': 'hyundai',
      'هونداي': 'hyundai',
      'كيا': 'kia',
      'نيسان': 'nissan',
      'مازدا': 'mazda',
      'فولكس فاجن': 'volkswagen',
      'فولكسفاجن': 'volkswagen',
      'سوبارو': 'subaru',
      'لكزس': 'lexus',
      'اكيورا': 'acura',
      'انفينيتي': 'infiniti',
      'جيب': 'jeep',
      'دودج': 'dodge',
      'كرايسلر': 'chrysler',
      'تسلا': 'tesla',
      'فولفو': 'volvo',
      'لاند روفر': 'land rover',
      'رينج روفر': 'range rover',
      'جاكوار': 'jaguar',
      'بورش': 'porsche',
      'فيراري': 'ferrari',
      'لامبورجيني': 'lamborghini',
      'مازيراتي': 'maserati',
      'بنتلي': 'bentley',
      'رولز رويس': 'rolls royce',

      // Common car models in Arabic
      'كورولا': 'toyota',
      'كامري': 'toyota',
      'اكورد': 'honda',
      'سيفيك': 'honda',
      'فيرنا': 'hyundai',
      'النترا': 'hyundai',
      'إلنترا': 'hyundai',
      'سوناتا': 'hyundai',
      'سبورتاج': 'kia',
      'سيراتو': 'kia',
      'صني': 'nissan',
      'تيدا': 'nissan',
      'اوبترا': 'chevrolet',
      'أوبترا': 'chevrolet',
      'لانوس': 'chevrolet',
      'افيو': 'chevrolet',
      'أفيو': 'chevrolet',
      'باسات': 'volkswagen',
      'جولف': 'volkswagen',
      'جيتا': 'volkswagen',
    };

    for (final entry in carBrands.entries) {
      if (normalizedText.contains(entry.key)) {
        dev.log(
            'VoiceCommandProcessor: Matched car brand: ${entry.key} -> ${entry.value}');
        return entry.value;
      }
    }

    dev.log('VoiceCommandProcessor: No car match found, returning unknown');
    return 'unknown';
  }

  /// Extract mileage information from text
  int? _extractMileage(String text) {
    // Look for numbers followed by "miles" or "km"
    final RegExp mileageRegex =
        RegExp(r'(\d+)\s*(miles|mile|km|kilometers|كم|كيلومتر|كيلومترات)');
    final match = mileageRegex.firstMatch(text);

    if (match != null) {
      return int.tryParse(match.group(1) ?? '');
    }

    return null;
  }

  /// Extract oil type information from text
  String? _extractOilType(String text) {
    final oilTypes = {
      'synthetic': ['synthetic', 'سينتيك'],
      'semi-synthetic': ['semi-synthetic', 'نصف سينتيك'],
      'conventional': ['conventional', 'تقليدي'],
    };

    for (final entry in oilTypes.entries) {
      for (final keyword in entry.value) {
        if (text.contains(keyword)) {
          return entry.key;
        }
      }
    }

    return null;
  }

  /// Extract oil quantity information from text
  int? _extractOilQuantity(String text) {
    // Look for numbers followed by "liters" or "gallons"
    final RegExp oilQuantityRegex = RegExp(
        r'(\d+)\s*(liters|litre|gallons|gallon|لتر|لترات|غالون|غالونات)');
    final match = oilQuantityRegex.firstMatch(text);

    if (match != null) {
      return int.tryParse(match.group(1) ?? '');
    }

    return null;
  }

  /// Extract filter information from text
  Map<String, dynamic> _extractFilterInfo(String text) {
    final filterInfo = <String, dynamic>{
      'changed': false,
      'type': null,
    };

    final filterKeywords = [
      'filter',
      'filters',
      'air filter',
      'فلتر',
      'فلاتر',
      'فلتر هواء',
    ];

    for (final keyword in filterKeywords) {
      if (text.contains(keyword)) {
        filterInfo['changed'] = true;
        filterInfo['type'] = keyword;
        break;
      }
    }

    return filterInfo;
  }

  /// Extract cost information from text
  Map<String, dynamic> _extractCostInfo(String text) {
    final costInfo = <String, dynamic>{
      'oil': null,
      'filter': null,
      'total': null,
    };

    // Look for numbers followed by currency indicators
    final RegExp costRegex =
        RegExp(r'(\d+(?:\.\d+)?)\s*(dollars?|euros?|دولار|يورو|\$|€)');
    final matches = costRegex.allMatches(text);

    if (matches.isNotEmpty) {
      final firstMatch = matches.first;
      final cost = double.tryParse(firstMatch.group(1) ?? '');

      // If we find oil-related keywords, assign to oil cost
      if (text.contains('oil') || text.contains('زيت')) {
        costInfo['oil'] = cost;
      }
      // If we find filter-related keywords, assign to filter cost
      else if (text.contains('filter') || text.contains('فلتر')) {
        costInfo['filter'] = cost;
      }
      // Otherwise, assign to total cost
      else {
        costInfo['total'] = cost;
      }
    }

    return costInfo;
  }

  /// Extract notes from text
  String? _extractNotes(String text) {
    // Look for text after the word "notes"
    final RegExp notesRegex = RegExp(r'notes\s*:\s*(.*)');
    final match = notesRegex.firstMatch(text);

    if (match != null) {
      return match.group(1)?.trim();
    }

    return null;
  }

  /// Extract maintenance type from text
  String _extractMaintenanceType(String text) {
    final maintenanceTypes = {
      'brake': ['brake', 'brakes', 'فرامل', 'فرملة'],
      'tire': [
        'tire',
        'tires',
        'wheel',
        'wheels',
        'إطار',
        'إطارات',
        'عجلة',
        'عجلات'
      ],
      'filter': [
        'filter',
        'filters',
        'air filter',
        'فلتر',
        'فلاتر',
        'فلتر هواء'
      ],
      'battery': ['battery', 'بطارية'],
      'transmission': ['transmission', 'ناقل الحركة', 'جير'],
      'coolant': ['coolant', 'cooling', 'تبريد', 'مبرد'],
      'spark plug': ['spark plug', 'spark plugs', 'شمعة', 'شمعات', 'بوجيهات'],
    };

    for (final entry in maintenanceTypes.entries) {
      for (final keyword in entry.value) {
        if (text.contains(keyword)) {
          return entry.key;
        }
      }
    }

    return 'general';
  }

  /// Extract service provider from text
  String? _extractServiceProvider(String text) {
    // Look for text after the word "service" or "provider"
    final RegExp serviceProviderRegex = RegExp(r'service\s*:\s*(.*)');
    final match = serviceProviderRegex.firstMatch(text);

    if (match != null) {
      return match.group(1)?.trim();
    }

    return null;
  }

  /// Extract maintenance description from text
  String? _extractMaintenanceDescription(String text) {
    // Look for text after the word "description"
    final RegExp descriptionRegex = RegExp(r'description\s*:\s*(.*)');
    final match = descriptionRegex.firstMatch(text);

    if (match != null) {
      return match.group(1)?.trim();
    }

    return null;
  }
}
