import 'dart:async';
import 'dart:io';

import 'package:path_provider/path_provider.dart';
import 'package:record/record.dart';

import '../../../../core/utils/logger.dart';
import '../../domain/entities/voice_recording.dart';
import '../../domain/repositories/voice_recording_repository.dart';

/// A repository for recording voice using the `record` package.
class RecordPackageRecordingRepository implements VoiceRecordingRepository {
  final AudioRecorder _audioRecorder = AudioRecorder();
  final Map<String, VoiceRecording> _activeRecordings = {};
  String? _currentRecordingId;
  Timer? _amplitudeTimer;
  final _amplitudeStreamController = StreamController<Amplitude>.broadcast();

  @override
  Stream<Amplitude> get amplitudeStream => _amplitudeStreamController.stream;

  @override
  Future<bool> hasPermission() {
    return _audioRecorder.hasPermission();
  }

  @override
  Future<bool> requestPermission() async {
    // The `record` package handles this internally, but we can log it.
    final hasPerm = await _audioRecorder.hasPermission();
    AppLogger.info(
        'RecordPackageRecordingRepository: Microphone permission status: $hasPerm');
    return hasPerm;
  }

  @override
  Future<VoiceRecording> startRecording({
    required String languageCode,
    Map<String, dynamic>? metadata,
  }) async {
    if (!await hasPermission()) {
      throw Exception('Microphone permission not granted');
    }

    final recordingId =
        '${DateTime.now().millisecondsSinceEpoch}_${_activeRecordings.length}';
    _currentRecordingId = recordingId;
    final directory = await getApplicationDocumentsDirectory();
    final filePath = '${directory.path}/$recordingId.wav';

    final recording = VoiceRecording(
      id: recordingId,
      filePath: filePath,
      startTime: DateTime.now(),
      endTime: DateTime.now(), // Placeholder
      durationMs: 0,
      fileSizeBytes: 0,
      languageCode: languageCode,
      status: VoiceRecordingStatus.recording,
      metadata: metadata,
    );

    _activeRecordings[recordingId] = recording;

    const recordConfig = RecordConfig(
      encoder: AudioEncoder.wav,
      numChannels: 1,
      sampleRate: 16000,
    );
    await _audioRecorder.start(recordConfig, path: filePath);

    _amplitudeTimer =
        Timer.periodic(const Duration(milliseconds: 200), (timer) async {
      if (await _audioRecorder.isRecording()) {
        final amplitude = await _audioRecorder.getAmplitude();
        _amplitudeStreamController.add(amplitude);
      }
    });

    AppLogger.info(
        'RecordPackageRecordingRepository: Started recording $recordingId to $filePath');
    return recording;
  }

  @override
  Future<VoiceRecording> stopRecording(String recordingId) async {
    // The recorder might have been disposed if the user backed out.
    if (_currentRecordingId != recordingId) {
      final oldRecording = _activeRecordings[recordingId];
      if (oldRecording != null) return oldRecording;
      throw Exception('Recording session mismatch or already stopped.');
    }

    final path = await _audioRecorder.stop();
    final recording = _activeRecordings[recordingId];
    _currentRecordingId = null;
    _amplitudeTimer?.cancel();

    if (path == null || recording == null) {
      throw Exception('Recording session not found or failed to stop.');
    }

    final file = File(path);
    final fileSizeBytes = await file.length();
    final endTime = DateTime.now();
    final durationMs = endTime.difference(recording.startTime).inMilliseconds;

    final completedRecording = recording.copyWith(
      endTime: endTime,
      durationMs: durationMs,
      fileSizeBytes: fileSizeBytes,
      status: VoiceRecordingStatus.completed,
    );

    _activeRecordings[recordingId] = completedRecording;
    AppLogger.info(
        'RecordPackageRecordingRepository: Stopped recording $recordingId. File at $path, Size: $fileSizeBytes bytes.');

    return completedRecording;
  }

  @override
  Future<File?> getRecordingFile(String recordingId) async {
    final recording = _activeRecordings[recordingId];
    if (recording == null) return null;
    final file = File(recording.filePath);
    return await file.exists() ? file : null;
  }

  @override
  Future<void> dispose() async {
    if (await _audioRecorder.isRecording()) {
      await _audioRecorder.stop();
    }
    _amplitudeTimer?.cancel();
    await _audioRecorder.dispose();
    await _amplitudeStreamController.close();
    _activeRecordings.clear();
    _currentRecordingId = null;
    AppLogger.info('RecordPackageRecordingRepository: Disposed');
  }

  @override
  Future<void> cancelRecording(String recordingId) async {
    if (_currentRecordingId == recordingId &&
        (await _audioRecorder.isRecording())) {
      await _audioRecorder.stop();
    }
    _amplitudeTimer?.cancel();
    final recording = _activeRecordings.remove(recordingId);
    _currentRecordingId = null;
    if (recording != null) {
      final file = File(recording.filePath);
      if (await file.exists()) {
        await file.delete();
      }
    }
    AppLogger.info(
        'RecordPackageRecordingRepository: Canceled and deleted recording $recordingId');
  }

  @override
  Future<VoiceRecording?> getRecording(String recordingId) async {
    return _activeRecordings[recordingId];
  }

  @override
  Future<void> cleanupTempFiles() async {
    // This implementation uses permanent document storage, so no-op.
  }

  @override
  Future<bool> testMicrophone() async {
    // This is a simple implementation, we assume if permission is granted, it works.
    return hasPermission();
  }
}
