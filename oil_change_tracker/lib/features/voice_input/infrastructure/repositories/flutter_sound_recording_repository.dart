import 'dart:async';
import 'dart:io';
import 'package:flutter_sound/flutter_sound.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:uuid/uuid.dart';
import 'package:record/record.dart';
import '../../domain/entities/voice_recording.dart';
import '../../domain/repositories/voice_recording_repository.dart';
import '../../../../core/utils/logger.dart';

/// Flutter Sound implementation of voice recording repository
class FlutterSoundRecordingRepository implements VoiceRecordingRepository {
  static const _uuid = Uuid();

  FlutterSoundRecorder? _recorder;
  final Map<String, VoiceRecording> _activeRecordings = {};
  bool _isInitialized = false;
  StreamSubscription? _progressSubscription;
  final _amplitudeStreamController = StreamController<Amplitude>.broadcast();

  @override
  Stream<Amplitude> get amplitudeStream => _amplitudeStreamController.stream;

  /// Initialize FlutterSound recorder
  Future<void> _ensureInitialized() async {
    if (_isInitialized) return;

    try {
      _recorder = FlutterSoundRecorder();

      // Open the audio session with specific Android configuration
      await _recorder!.openRecorder();

      // Set subscription duration for better audio capture
      await _recorder!
          .setSubscriptionDuration(const Duration(milliseconds: 100));

      _isInitialized = true;
      AppLogger.info(
          'FlutterSoundRecordingRepository: Initialized successfully');
    } catch (e) {
      _isInitialized = false;
      AppLogger.error(
          'FlutterSoundRecordingRepository: Failed to initialize', e);
      rethrow;
    }
  }

  @override
  Future<bool> hasPermission() async {
    try {
      final status = await Permission.microphone.status;
      AppLogger.info(
          'FlutterSoundRecordingRepository: Microphone permission status: $status');
      return status.isGranted;
    } catch (e) {
      AppLogger.error(
          'FlutterSoundRecordingRepository: Error checking permission', e);
      return false;
    }
  }

  @override
  Future<bool> requestPermission() async {
    try {
      final currentStatus = await Permission.microphone.status;
      AppLogger.info(
          'FlutterSoundRecordingRepository: Current permission status: $currentStatus');

      if (currentStatus.isGranted) {
        return true;
      }

      if (currentStatus.isPermanentlyDenied) {
        AppLogger.warning(
            'FlutterSoundRecordingRepository: Permission permanently denied');
        return false;
      }

      final status = await Permission.microphone.request();
      AppLogger.info(
          'FlutterSoundRecordingRepository: Permission request result: $status');
      return status.isGranted;
    } catch (e) {
      AppLogger.error(
          'FlutterSoundRecordingRepository: Error requesting permission', e);
      return false;
    }
  }

  @override
  Future<VoiceRecording> startRecording({
    required String languageCode,
    Map<String, dynamic>? metadata,
  }) async {
    await _ensureInitialized();

    if (!await hasPermission()) {
      throw Exception('Microphone permission not granted');
    }

    if (_recorder!.isRecording) {
      throw Exception('Already recording');
    }

    try {
      // Generate unique recording ID and file path
      final recordingId = _uuid.v4();
      final directory = await getTemporaryDirectory();
      final filePath = '${directory.path}/voice_recording_$recordingId.wav';

      // Create initial recording entity
      final recording = VoiceRecording(
        id: recordingId,
        filePath: filePath,
        startTime: DateTime.now(),
        endTime: DateTime.now(), // Will be updated on stop
        durationMs: 0, // Will be calculated on stop
        fileSizeBytes: 0, // Will be calculated on stop
        languageCode: languageCode,
        status: VoiceRecordingStatus.recording,
        metadata: metadata,
      );

      // Store active recording
      _activeRecordings[recordingId] = recording;

      // Start recording with optimal settings
      AppLogger.info(
          'FlutterSoundRecordingRepository: Starting recording with path: $filePath');

      // Try different codecs for better compatibility based on device
      Codec codecToUse = Codec.pcm16WAV; // Start with most compatible
      String fileExtension = '.wav';

      // Update file path with correct extension
      final correctedFilePath = filePath.replaceAll('.aac', fileExtension);
      final correctedRecording =
          recording.copyWith(filePath: correctedFilePath);
      _activeRecordings[recordingId] = correctedRecording;

      try {
        AppLogger.info(
            'FlutterSoundRecordingRepository: Attempting recording with codec: $codecToUse');

        await _recorder!.startRecorder(
          toFile: correctedFilePath,
          codec: codecToUse,
          sampleRate: 44100, // Higher sample rate for better quality
          numChannels: 1,
          bitRate: 128000,
        );

        _progressSubscription = _recorder!.onProgress!.listen((e) {
          if (e.decibels != null) {
            final amplitude = Amplitude(current: e.decibels!, max: 0.0);
            _amplitudeStreamController.add(amplitude);
          }
        });

        // Verify recording actually started
        if (!_recorder!.isRecording) {
          throw Exception(
              'Failed to start recording - recorder not in recording state');
        }

        AppLogger.info(
            'FlutterSoundRecordingRepository: Started recording successfully');
        return correctedRecording;
      } catch (e) {
        AppLogger.error(
            'FlutterSoundRecordingRepository: Failed to start recording with $codecToUse, trying fallback',
            e);

        // Try fallback with different settings
        try {
          final fallbackPath =
              correctedFilePath.replaceAll('.wav', '_fallback.wav');
          final fallbackRecording =
              correctedRecording.copyWith(filePath: fallbackPath);
          _activeRecordings[recordingId] = fallbackRecording;

          await _recorder!.startRecorder(
            toFile: fallbackPath,
            codec: Codec.pcm16WAV,
            sampleRate: 16000, // Lower sample rate for compatibility
            numChannels: 1,
          );

          if (!_recorder!.isRecording) {
            throw Exception('Fallback recording also failed to start');
          }

          AppLogger.info(
              'FlutterSoundRecordingRepository: Started fallback recording successfully');
          return fallbackRecording;
        } catch (e2) {
          _activeRecordings.remove(recordingId);
          AppLogger.error(
              'FlutterSoundRecordingRepository: Both recording attempts failed',
              e2);
          rethrow;
        }
      }
    } catch (e) {
      AppLogger.error(
          'FlutterSoundRecordingRepository: Failed to start recording', e);
      rethrow;
    }
  }

  @override
  Future<VoiceRecording> stopRecording(String recordingId) async {
    final recording = _activeRecordings[recordingId];
    if (recording == null) {
      throw Exception('Recording not found: $recordingId');
    }

    if (!_recorder!.isRecording) {
      throw Exception('Not currently recording');
    }

    try {
      await _recorder!.stopRecorder();
      await _progressSubscription?.cancel();
      _progressSubscription = null;

      // Wait for file system to flush
      await Future.delayed(
          const Duration(milliseconds: 500)); // Increased delay

      final endTime = DateTime.now();
      final durationMs = endTime.difference(recording.startTime).inMilliseconds;

      // Get file size with better error handling
      final file = File(recording.filePath);
      int fileSizeBytes = 0;
      bool fileExists = false;

      if (await file.exists()) {
        fileExists = true;
        fileSizeBytes = await file.length();
        AppLogger.info(
            'FlutterSoundRecordingRepository: Recording file exists: $fileExists, size: $fileSizeBytes bytes');
      } else {
        AppLogger.warning(
            'FlutterSoundRecordingRepository: Recording file does not exist at path: ${recording.filePath}');
      }

      // Update recording with final data
      final completedRecording = recording.copyWith(
        endTime: endTime,
        durationMs: durationMs,
        fileSizeBytes: fileSizeBytes,
        status: VoiceRecordingStatus.completed,
      );

      // Remove from active recordings
      _activeRecordings.remove(recordingId);

      AppLogger.info(
        'FlutterSoundRecordingRepository: Completed recording $recordingId '
        '(${durationMs}ms, ${fileSizeBytes}bytes, fileExists: $fileExists)',
      );

      return completedRecording;
    } catch (e) {
      // Update recording with error status
      final errorRecording = recording.copyWith(
        status: VoiceRecordingStatus.failed,
        errorMessage: e.toString(),
      );
      _activeRecordings.remove(recordingId);

      AppLogger.error(
          'FlutterSoundRecordingRepository: Failed to stop recording', e);
      throw Exception('Failed to stop recording: $e');
    }
  }

  @override
  Future<void> cancelRecording(String recordingId) async {
    final recording = _activeRecordings[recordingId];
    if (recording == null) {
      return; // Already cancelled or doesn't exist
    }

    try {
      if (_recorder!.isRecording) {
        await _recorder!.stopRecorder();
      }
      await _progressSubscription?.cancel();
      _progressSubscription = null;

      // Delete the file if it exists
      final file = File(recording.filePath);
      if (await file.exists()) {
        await file.delete();
      }

      _activeRecordings.remove(recordingId);
      AppLogger.info(
          'FlutterSoundRecordingRepository: Cancelled recording $recordingId');
    } catch (e) {
      AppLogger.error(
          'FlutterSoundRecordingRepository: Error cancelling recording', e);
    }
  }

  @override
  Future<VoiceRecording?> getRecording(String recordingId) async {
    return _activeRecordings[recordingId];
  }

  @override
  Future<File?> getRecordingFile(String recordingId) async {
    final recording = _activeRecordings[recordingId];
    if (recording == null) return null;

    final file = File(recording.filePath);
    if (await file.exists()) {
      return file;
    }
    return null;
  }

  @override
  Future<void> cleanupTempFiles() async {
    try {
      final directory = await getTemporaryDirectory();
      final files = directory
          .listSync()
          .whereType<File>()
          .where((file) => file.path.contains('voice_recording_'));

      for (final file in files) {
        try {
          await file.delete();
        } catch (e) {
          AppLogger.warning('Failed to delete temp file: ${file.path}');
        }
      }

      AppLogger.info('FlutterSoundRecordingRepository: Cleaned up temp files');
    } catch (e) {
      AppLogger.error(
          'FlutterSoundRecordingRepository: Error cleaning temp files', e);
    }
  }

  /// Dispose resources
  Future<void> dispose() async {
    try {
      // Cancel any active recordings
      for (final recordingId in _activeRecordings.keys.toList()) {
        await cancelRecording(recordingId);
      }

      if (_isInitialized && _recorder != null) {
        if (_recorder!.isRecording) {
          await _recorder!.stopRecorder();
        }
        await _recorder!.closeRecorder();
      }
      await _progressSubscription?.cancel();
      _progressSubscription = null;
      await _amplitudeStreamController.close();

      _isInitialized = false;
      AppLogger.info('FlutterSoundRecordingRepository: Disposed successfully');
    } catch (e) {
      AppLogger.error(
          'FlutterSoundRecordingRepository: Error during dispose', e);
    }
  }

  /// Test microphone functionality
  Future<bool> testMicrophone() async {
    await _ensureInitialized();

    if (!await hasPermission()) {
      AppLogger.warning(
          'FlutterSoundRecordingRepository: No microphone permission for test');
      return false;
    }

    try {
      AppLogger.info(
          'FlutterSoundRecordingRepository: Starting microphone test (simple recording)');

      // Try a simple recording approach
      final directory = await getTemporaryDirectory();
      final testFilePath = '${directory.path}/mic_test_simple.wav';

      await _recorder!.startRecorder(
        toFile: testFilePath,
        codec: Codec.pcm16WAV,
        sampleRate: 44100, // Try higher sample rate
        numChannels: 1,
      );

      // Record for 3 seconds (longer test)
      await Future.delayed(const Duration(seconds: 3));

      await _recorder!.stopRecorder();

      // Check if file was created and has content
      final testFile = File(testFilePath);
      if (await testFile.exists()) {
        final fileSize = await testFile.length();
        AppLogger.info(
            'FlutterSoundRecordingRepository: Simple test recording created ${fileSize} bytes');

        // Clean up test file
        await testFile.delete();

        if (fileSize > 1000) {
          AppLogger.info(
              'FlutterSoundRecordingRepository: Microphone test PASSED');
          return true;
        } else {
          AppLogger.warning(
              'FlutterSoundRecordingRepository: Microphone test FAILED - insufficient audio data');
          return false;
        }
      } else {
        AppLogger.warning(
            'FlutterSoundRecordingRepository: Simple test recording file not created');
        return false;
      }
    } catch (e) {
      AppLogger.error(
          'FlutterSoundRecordingRepository: Simple test failed, trying fallback method',
          e);

      // Fallback to original file-based test
      try {
        final directory = await getTemporaryDirectory();
        final testFilePath = '${directory.path}/mic_test.wav';

        AppLogger.info(
            'FlutterSoundRecordingRepository: Starting file-based microphone test');

        await _recorder!.startRecorder(
          toFile: testFilePath,
          codec: Codec.pcm16WAV,
          sampleRate: 16000,
          numChannels: 1,
        );

        // Record for 2 seconds
        await Future.delayed(const Duration(seconds: 2));

        await _recorder!.stopRecorder();

        // Check if file was created and has content
        final testFile = File(testFilePath);
        if (await testFile.exists()) {
          final fileSize = await testFile.length();
          AppLogger.info(
              'FlutterSoundRecordingRepository: Test recording created ${fileSize} bytes');

          // Clean up test file
          await testFile.delete();

          return fileSize >
              1000; // Should be more than 1KB for 2 seconds of audio
        } else {
          AppLogger.warning(
              'FlutterSoundRecordingRepository: Test recording file not created');
          return false;
        }
      } catch (e2) {
        AppLogger.error(
            'FlutterSoundRecordingRepository: Both microphone tests failed',
            e2);
        return false;
      }
    }
  }
}
