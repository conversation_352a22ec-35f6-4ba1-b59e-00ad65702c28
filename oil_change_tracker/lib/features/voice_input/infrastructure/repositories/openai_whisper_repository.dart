import 'dart:io';
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:http_parser/http_parser.dart';

import '../../domain/entities/voice_recording.dart';
import '../../domain/repositories/speech_recognition_repository.dart';
import '../../../../core/services/api_key_service.dart';
import '../../../../core/utils/logger.dart';

/// OpenAI Whisper API implementation of speech recognition repository
class OpenAIWhisperRepository implements SpeechRecognitionRepository {
  final ApiKeyService _apiKeyService;
  String? _apiKey;
  bool _isInitialized = false;
  static const String _apiEndpoint =
      'https://api.openai.com/v1/audio/transcriptions';

  OpenAIWhisperRepository(this._apiKeyService);

  @override
  Future<bool> initialize() async {
    if (_isInitialized) return true;

    try {
      _apiKey = await _apiKeyService.getOpenAIApiKey();
      if (_apiKey == null || _apiKey!.isEmpty) {
        AppLogger.error(
            'OpenAIWhisperRepository: Failed to get OpenAI API key');
        return false;
      }

      _isInitialized = true;
      AppLogger.info('OpenAIWhisperRepository: Initialized successfully');
      return true;
    } catch (e) {
      AppLogger.error('OpenAIWhisperRepository: Initialization failed', e);
      return false;
    }
  }

  @override
  Future<bool> isAvailable() async {
    return _isInitialized || await initialize();
  }

  @override
  Future<VoiceRecording> transcribe(VoiceRecording recording) async {
    if (!_isInitialized && !await initialize()) {
      throw Exception('OpenAI Whisper API not initialized');
    }

    try {
      final file = File(recording.filePath);
      if (!await file.exists()) {
        throw Exception('Recording file not found: ${recording.filePath}');
      }

      // Verify the file has content
      final fileSize = await file.length();
      if (fileSize <= 0) {
        throw Exception('Recording file is empty: ${recording.filePath}');
      }

      AppLogger.info(
          'OpenAIWhisperRepository: Transcribing file ${recording.filePath} (${fileSize} bytes)');

      // Debug the file format
      try {
        // Read the first few bytes to check WAV header
        final bytes = await file.openRead(0, 44).toList();
        final buffer = bytes.expand((x) => x).toList();

        // Check WAV header
        final isRiffHeader =
            String.fromCharCodes(buffer.sublist(0, 4)) == 'RIFF';
        final isWavFormat =
            String.fromCharCodes(buffer.sublist(8, 12)) == 'WAVE';
        final formatChunk =
            String.fromCharCodes(buffer.sublist(12, 16)) == 'fmt ';

        // Extract format information if possible
        int channels = 0;
        int sampleRate = 0;
        int bitsPerSample = 0;

        if (buffer.length >= 34) {
          channels = buffer[22] + (buffer[23] << 8);
          sampleRate = buffer[24] +
              (buffer[25] << 8) +
              (buffer[26] << 16) +
              (buffer[27] << 24);
          bitsPerSample = buffer[34] + (buffer[35] << 8);
        }

        AppLogger.info(
            'OpenAIWhisperRepository: File format check - RIFF: $isRiffHeader, WAVE: $isWavFormat, fmt: $formatChunk');
        AppLogger.info(
            'OpenAIWhisperRepository: Audio format - Channels: $channels, Sample Rate: $sampleRate Hz, Bits: $bitsPerSample');

        if (!isRiffHeader || !isWavFormat) {
          AppLogger.warning(
              'OpenAIWhisperRepository: File does not appear to be a valid WAV file');
        }

        if (channels != 1 || sampleRate != 16000 || bitsPerSample != 16) {
          AppLogger.warning(
              'OpenAIWhisperRepository: Audio format may not be optimal for Whisper API (should be mono 16kHz PCM-16)');
        }
      } catch (e) {
        AppLogger.warning(
            'OpenAIWhisperRepository: Could not analyze file format: $e');
      }

      // Upload WAV file directly (no MP3 conversion)
      final String filePathToUpload = recording.filePath;
      const String mimeSubtype = 'wav';

      // Check the chosen file
      final uploadFileSize = await File(filePathToUpload).length();
      AppLogger.info(
          'OpenAIWhisperRepository: Upload file size: $uploadFileSize bytes');

      // Create multipart request
      final uri = Uri.parse(_apiEndpoint);
      final request = http.MultipartRequest('POST', uri);

      // Set headers and fields
      request.headers['Authorization'] = 'Bearer $_apiKey';
      request.fields['model'] = 'whisper-1';

      // Convert language code to ISO-639-1 format (e.g., 'en-US' -> 'en', 'ar-EG' -> 'ar')
      final isoLanguageCode = _convertToISO6391(recording.languageCode);
      request.fields['language'] = isoLanguageCode;

      AppLogger.info(
          'OpenAIWhisperRepository: Using language code: $isoLanguageCode (from ${recording.languageCode})');

      // Add the audio file with proper content type
      request.files.add(await http.MultipartFile.fromPath(
        'file',
        filePathToUpload,
        contentType: MediaType('audio', mimeSubtype),
      ));

      // Log the request details
      AppLogger.info(
          'OpenAIWhisperRepository: Sending request to ${uri.toString()} with ${request.files.length} files');
      AppLogger.info(
          'OpenAIWhisperRepository: Request fields: ${request.fields}');

      // Send the request
      final streamedResponse = await request.send();
      final response = await http.Response.fromStream(streamedResponse);

      // Log response status
      AppLogger.info(
          'OpenAIWhisperRepository: Received response with status code ${response.statusCode}');

      if (response.statusCode == 200) {
        final Map<String, dynamic> data = json.decode(response.body);
        final String transcription = data['text'] ?? '';

        AppLogger.info(
            'OpenAIWhisperRepository: Transcription successful: "$transcription"');

        return recording.copyWith(
          transcription: transcription,
          status: VoiceRecordingStatus.transcribed,
        );
      } else {
        final errorMsg =
            'OpenAI API error: ${response.statusCode} - ${response.body}';
        AppLogger.error(errorMsg);

        // Try to parse error message for better debugging
        try {
          final errorData = json.decode(response.body);
          final errorMessage =
              errorData['error']?['message'] ?? 'Unknown error';
          final errorType = errorData['error']?['type'] ?? 'unknown';
          AppLogger.error(
              'OpenAIWhisperRepository: Error type: $errorType, message: $errorMessage');
        } catch (e) {
          AppLogger.error(
              'OpenAIWhisperRepository: Could not parse error response');
        }

        throw Exception(errorMsg);
      }
    } catch (e) {
      AppLogger.error('OpenAIWhisperRepository: Transcription failed', e);
      return recording.copyWith(
        status: VoiceRecordingStatus.failed,
        errorMessage: e.toString(),
      );
    }
  }

  // Convert language code to ISO-639-1 format
  String _convertToISO6391(String languageCode) {
    // If the language code contains a hyphen (e.g., 'en-US', 'ar-EG'),
    // take only the first part which is the ISO-639-1 code
    if (languageCode.contains('-') || languageCode.contains('_')) {
      return languageCode.split(RegExp(r'[-_]'))[0].toLowerCase();
    }

    // If it's already in ISO-639-1 format, return as is
    return languageCode.toLowerCase();
  }

  @override
  List<String> getSupportedLanguages() {
    // Return a list of supported languages for Whisper API
    return [
      'en', // English
      'ar', // Arabic
      'zh', // Chinese
      'fr', // French
      'de', // German
      'hi', // Hindi
      'it', // Italian
      'ja', // Japanese
      'ko', // Korean
      'pt', // Portuguese
      'ru', // Russian
      'es', // Spanish
    ];
  }

  @override
  Future<void> dispose() async {
    // No resources to dispose
  }

  @override
  Future<bool> testConnection() async {
    return _isInitialized || await initialize();
  }
}
