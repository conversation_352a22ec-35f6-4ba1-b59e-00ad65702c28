import '../../domain/entities/voice_recording.dart';
import '../../domain/repositories/voice_recording_repository.dart';
import '../../domain/repositories/speech_recognition_repository.dart';
import '../../../../core/utils/logger.dart';

/// Use case for recording and transcribing voice input
class RecordAndTranscribeUseCase {
  final VoiceRecordingRepository _recordingRepository;
  final SpeechRecognitionRepository _speechRepository;

  // Public getters for repository access
  VoiceRecordingRepository get recordingRepository => _recordingRepository;
  SpeechRecognitionRepository get speechRepository => _speechRepository;

  RecordAndTranscribeUseCase(
    this._recordingRepository,
    this._speechRepository,
  );

  /// Start a new recording session
  Future<VoiceRecording> startRecording({
    required String languageCode,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      // Check permissions
      if (!await _recordingRepository.hasPermission()) {
        final granted = await _recordingRepository.requestPermission();
        if (!granted) {
          throw Exception('Microphone permission required');
        }
      }

      // Ensure speech recognition is available
      if (!await _speechRepository.isAvailable()) {
        final initialized = await _speechRepository.initialize();
        if (!initialized) {
          throw Exception('Speech recognition service unavailable');
        }
      }

      // Start recording
      final recording = await _recordingRepository.startRecording(
        languageCode: languageCode,
        metadata: metadata,
      );

      AppLogger.info(
        'RecordAndTranscribeUseCase: Started recording ${recording.id}',
      );

      return recording;
    } catch (e) {
      AppLogger.error(
          'RecordAndTranscribeUseCase: Failed to start recording', e);
      rethrow;
    }
  }

  /// Stop recording and transcribe the audio
  Future<VoiceRecording> stopAndTranscribe(String recordingId) async {
    try {
      // Stop recording
      var recording = await _recordingRepository.stopRecording(recordingId);

      AppLogger.info(
        'RecordAndTranscribeUseCase: Stopped recording ${recording.id} '
        '(${recording.durationMs}ms)',
      );

      // Validate recording
      if (!recording.isValidForProcessing) {
        AppLogger.warning(
          'RecordAndTranscribeUseCase: Recording $recordingId is not valid for processing. ${recording.validationInfo}',
        );
        throw Exception(
          recording.isTooShort
              ? 'Recording too short - please speak for at least 1 second'
              : recording.isTooLong
                  ? 'Recording too long (maximum 30s allowed)'
                  : 'No speech detected - please speak clearly and try again',
        );
      }

      // If speech_to_text already provided a transcription, use it directly.
      if (recording.transcription != null &&
          recording.transcription!.isNotEmpty) {
        AppLogger.info(
            'RecordAndTranscribeUseCase: Using transcription from speech_to_text for recording $recordingId');
        return recording;
      }

      // If local recognition failed, we fall back to Google's API
      AppLogger.info(
          'RecordAndTranscribeUseCase: Local recognition failed, attempting transcription with Google Speech API for recording $recordingId');
      return _speechRepository.transcribe(recording);
    } catch (e) {
      AppLogger.error(
        'RecordAndTranscribeUseCase: Failed to stop and transcribe recording $recordingId',
        e,
      );
      rethrow;
    }
  }

  /// Cancel an active recording
  Future<void> cancelRecording(String recordingId) async {
    try {
      await _recordingRepository.cancelRecording(recordingId);
      AppLogger.info(
          'RecordAndTranscribeUseCase: Cancelled recording $recordingId');
    } catch (e) {
      AppLogger.error(
        'RecordAndTranscribeUseCase: Failed to cancel recording $recordingId',
        e,
      );
      rethrow;
    }
  }

  /// Get supported languages for speech recognition
  List<String> getSupportedLanguages() {
    return _speechRepository.getSupportedLanguages();
  }

  /// Clean up resources
  Future<void> dispose() async {
    try {
      await _recordingRepository.cleanupTempFiles();
      await _speechRepository.dispose();
      AppLogger.info('RecordAndTranscribeUseCase: Disposed successfully');
    } catch (e) {
      AppLogger.error('RecordAndTranscribeUseCase: Error during dispose', e);
    }
  }
}
