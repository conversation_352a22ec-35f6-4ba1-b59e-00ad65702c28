import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../data/repositories/car_repository.dart';
import '../../../../core/models/car_model.dart';
import '../../../../core/providers/auth_provider.dart';

final carsProvider = StreamProvider<List<CarModel>>((ref) {
  final repository = ref.watch(carRepositoryProvider);
  final authState = ref.watch(authStateChangesProvider);
  final user = authState.value;
  
  if (user == null) {
    return Stream.value([]);
  }
  
  return repository.watchUserCars();
});

final selectedCarProvider = StateProvider<CarModel?>((ref) => null);

class CarNotifier extends StateNotifier<AsyncValue<void>> {
  final CarRepository _repository;
  final Ref _ref;

  CarNotifier(this._repository, this._ref) : super(const AsyncValue.data(null));

  Future<void> addCar(CarModel car) async {
    try {
      state = const AsyncValue.loading();
      await _repository.addCar(car);
      state = const AsyncValue.data(null);
    } catch (e, st) {
      state = AsyncValue.error(e, st);
      rethrow;
    }
  }

  Future<void> updateCar(CarModel car) async {
    try {
      state = const AsyncValue.loading();
      await _repository.updateCar(car);
      state = const AsyncValue.data(null);
    } catch (e, st) {
      state = AsyncValue.error(e, st);
      rethrow;
    }
  }

  Future<void> deleteCar(String carId) async {
    try {
      state = const AsyncValue.loading();
      await _repository.deleteCar(carId);
      state = const AsyncValue.data(null);
    } catch (e, st) {
      state = AsyncValue.error(e, st);
      rethrow;
    }
  }

  Future<void> updateMileage(String carId, int newMileage) async {
    try {
      state = const AsyncValue.loading();
      await _repository.updateCarMileage(carId, newMileage);
      state = const AsyncValue.data(null);
    } catch (e, st) {
      state = AsyncValue.error(e, st);
      rethrow;
    }
  }

  Future<void> recordOilChange(String carId, Map<String, dynamic> oilChangeData) async {
    try {
      state = const AsyncValue.loading();
      await _repository.recordOilChange(
        carId,
        oilChangeData['date'] as DateTime,
        oilChangeData['mileage'] as int,
      );
      state = const AsyncValue.data(null);
    } catch (e, st) {
      state = AsyncValue.error(e, st);
      rethrow;
    }
  }
}

final carNotifierProvider = StateNotifierProvider<CarNotifier, AsyncValue<void>>((ref) {
  final repository = ref.watch(carRepositoryProvider);
  return CarNotifier(repository, ref);
}); 