import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:percent_indicator/circular_percent_indicator.dart';
import '../../../../core/models/car_model.dart';
import '../../../../generated/app_localizations.dart';
import 'dart:developer' as dev;
import '../../../dashboard/presentation/screens/dashboard_screen.dart';
import 'package:go_router/go_router.dart';

class MileageUpdateCard extends ConsumerWidget {
  final CarModel car;

  const MileageUpdateCard({
    super.key,
    required this.car,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final l10n = S.of(context);
    
    final car = this.car;
    final oilChangeProgress = car.oilChangeProgress;
    final kmUntilChange = car.kilometersUntilNextChange;
    final kmSinceChange = car.kilometersSinceLastChange;
    
    return Card(
      elevation: theme.cardTheme.elevation ?? 2.0, // Use theme elevation
      shape: theme.cardTheme.shape ?? RoundedRectangleBorder( // Use theme shape or default
        borderRadius: BorderRadius.circular(12.0),
      ),
      margin: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 8.0), // Consistent margin
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  l10n.currentMileage,
                  style: theme.textTheme.titleMedium,
                ),
                _buildMileageDisplay(theme, car),
              ],
            ),
              
            const SizedBox(height: 20),
            CircularPercentIndicator(
              radius: 80.0,
              lineWidth: 14.0,
              percent: oilChangeProgress > 1.0 ? 1.0 : oilChangeProgress,
              center: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    kmUntilChange <= 0
                        ? l10n.oilChangeOverdue
                        : kmUntilChange.toString(),
                    style: theme.textTheme.titleLarge,
                  ),
                  if (kmUntilChange > 0)
                    Text(
                      'km',
                      style: theme.textTheme.bodySmall,
                    ),
                ],
              ),
              progressColor: _getProgressColor(theme, oilChangeProgress),
              backgroundColor: theme.colorScheme.primary.withOpacity(0.1),
              animation: true,
              animationDuration: 800,
            ),
            const SizedBox(height: 10),
            Text(
              kmUntilChange <= 0
                  ? l10n.oilChangeOverdue
                  : '$kmUntilChange km ${l10n.kmUntilNextOilChange}'
                      .replaceAll('{km}', ''),
              style: theme.textTheme.bodyLarge?.copyWith(
                color: _getTextColor(theme, oilChangeProgress),
                fontWeight: kmUntilChange <= 0 ? FontWeight.bold : null,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              '${l10n.lastOilChange}: ${car.lastOilChangeMileage} km',
              style: theme.textTheme.bodyMedium,
            ),
            Text(
              '${l10n.kilometersElapsed}: $kmSinceChange km',
              style: theme.textTheme.bodyMedium,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: theme.colorScheme.primary,
                foregroundColor: theme.colorScheme.onPrimary,
                padding: const EdgeInsets.symmetric(horizontal: 30, vertical: 12),
                textStyle: theme.textTheme.labelLarge?.copyWith(fontWeight: FontWeight.bold),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8.0),
                ),
              ),
              onPressed: () => _showUpdateMileageDialog(context, ref, car),
              child: Text(l10n.updateMileage),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMileageDisplay(ThemeData theme, CarModel car) {
    return Row(
      children: [
        Text(
          car.currentMileage.toString(),
          style: theme.textTheme.headlineSmall,
        ),
        const SizedBox(width: 4),
        Text(
          'km',
          style: theme.textTheme.bodyMedium,
        ),
      ],
    );
  }

  void _showUpdateMileageDialog(BuildContext context, WidgetRef ref, CarModel car) {
    try {
      // Use the dashboard's update mileage dialog
      final dashboard = DashboardScreen.of(context);
      if (dashboard != null) {
        dashboard.showMileageUpdateDialog(car);
      } else {
        // Fallback - navigate to the dashboard
        context.push('/');
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(S.of(context).updateMileage),
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    } catch (e) {
      dev.log('Error showing mileage update dialog: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(S.of(context).errorOccurred),
          behavior: SnackBarBehavior.floating,
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Color _getProgressColor(ThemeData theme, double progress) {
    if (progress >= 1.0) {
      return theme.colorScheme.error;
    } else if (progress >= 0.8) {
      return Colors.orange;
    } else {
      return theme.colorScheme.primary;
    }
  }

  Color _getTextColor(ThemeData theme, double progress) {
    if (progress >= 1.0) {
      return theme.colorScheme.error;
    } else if (progress >= 0.8) {
      return Colors.orange;
    } else {
      return theme.colorScheme.primary;
    }
  }
} 