import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'dart:developer' as dev;

import '../../../../generated/app_localizations.dart';
import '../../../../core/models/car_model.dart';
import '../../../../core/theme/theme_extensions.dart';
import '../../../../shared/widgets/image_carousel.dart';
import '../../../../shared/widgets/primary_button.dart';
import '../../../../shared/services/image_cache_service.dart';

import '../../providers/car_provider.dart';
import '../../data/repositories/car_repository.dart' as car_repo;
import 'edit_car_screen.dart';

import '../../../oil_change/presentation/screens/oil_change_history_screen.dart';
import '../../../maintenance/presentation/screens/maintenance_history_list.dart';
import '../../../maintenance/providers/maintenance_provider.dart';

class CarDetailsScreen extends ConsumerStatefulWidget {
  final String carId;

  const CarDetailsScreen({
    super.key,
    required this.carId,
  });

  @override
  ConsumerState<CarDetailsScreen> createState() => _CarDetailsScreenState();
}

class _CarDetailsScreenState extends ConsumerState<CarDetailsScreen>
    with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => false; // Don't cache the state to ensure fresh data

  // Track if we came from maintenance screen
  bool _fromMaintenance = false;

  @override
  void initState() {
    super.initState();

    // Check if we came from a maintenance screen based on route history
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // Invalidate the car provider to ensure fresh data
      ref.invalidate(carProvider(widget.carId));

      // Check navigation history to determine initial tab
      if (mounted) {
        final router = GoRouter.of(context);
        final location = router.routeInformationProvider.value.uri.toString();

        // Check if the source query parameter exists and is set to maintenance
        if (location.contains('source=maintenance')) {
          setState(() {
            _fromMaintenance = true;
          });
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // Required by AutomaticKeepAliveClientMixin
    final l10n = S.of(context);
    final carAsync = ref.watch(carProvider(widget.carId));

    return WillPopScope(
      // If we came from a maintenance screen, navigate to dashboard on back press
      onWillPop: () async {
        if (_fromMaintenance) {
          context.go('/dashboard');
          return false; // Return false to prevent default back behavior
        }
        return true; // Allow default back behavior
      },
      child: carAsync.when(
        data: (car) {
          if (car == null) {
            return Scaffold(
              backgroundColor: context.containerBackgroundColor,
              appBar: AppBar(
                backgroundColor: context.containerBackgroundColor,
                title: Text(
                  l10n.error,
                  style: TextStyle(color: context.accentColor),
                ),
              ),
              body: Center(
                child: Text(
                  l10n.errorLoadingCarDetails,
                  style: TextStyle(color: context.secondaryAccentColor),
                ),
              ),
            );
          }

          return Scaffold(
            backgroundColor: context.containerBackgroundColor,
            appBar: AppBar(
              backgroundColor: context.containerBackgroundColor,
              title: Text(
                '${car.year} ${car.make} ${car.model}',
                style: TextStyle(
                  color: context.accentColor,
                  fontWeight: FontWeight.bold,
                ),
              ),
              // Override back button to handle navigation properly
              leading: IconButton(
                icon: Icon(Icons.arrow_back),
                onPressed: () {
                  if (_fromMaintenance) {
                    // Navigate to dashboard if we came from maintenance deletion
                    context.go('/dashboard');
                  } else {
                    // Use GoRouter's methods which are safer than Navigator.pop()
                    // context.canPop() checks if there's a screen to go back to
                    if (context.canPop()) {
                      context.pop();
                    } else {
                      // If we can't pop, navigate to the dashboard as a fallback
                      context.go('/dashboard');
                    }
                  }
                },
              ),
              actions: [
                IconButton(
                  icon: Icon(Icons.edit, color: context.secondaryAccentColor),
                  onPressed: () async {
                    // Use a smoother transition to edit screen
                    final navigator = Navigator.of(context);
                    await navigator.push(
                      MaterialPageRoute(
                        builder: (context) =>
                            EditCarScreen(carId: widget.carId),
                      ),
                    );
                    // Invalidate car provider to force refresh
                    ref.invalidate(carProvider(widget.carId));
                  },
                  tooltip: l10n.editCar,
                ),
                IconButton(
                  icon: Icon(Icons.delete, color: context.secondaryAccentColor),
                  onPressed: () => _showDeleteDialog(context, ref),
                  tooltip: l10n.deleteCar,
                ),
              ],
            ),
            body: DefaultTabController(
              length: 3,
              initialIndex: _fromMaintenance
                  ? 2
                  : 0, // Select maintenance tab if coming from maintenance screen
              child: Column(
                children: [
                  TabBar(
                    labelColor: context.accentColor,
                    unselectedLabelColor: context.secondaryTextColor,
                    indicatorColor: context.accentColor,
                    tabs: [
                      Tab(text: l10n.overview),
                      Tab(text: l10n.oilChanges),
                      Tab(text: l10n.maintenance),
                    ],
                  ),
                  Expanded(
                    child: Container(
                      color: context.containerBackgroundColor,
                      child: TabBarView(
                        children: [
                          _buildOverviewTab(context, car),
                          Container(
                            color: context.containerBackgroundColor,
                            child: _buildOilChangeTab(context, car),
                          ),
                          _buildMaintenanceTab(context, car, ref),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        },
        loading: () => Scaffold(
          backgroundColor: context.containerBackgroundColor,
          body: Center(
            child: CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(context.accentColor),
            ),
          ),
        ),
        error: (error, _) => Scaffold(
          backgroundColor: context.containerBackgroundColor,
          appBar: AppBar(
            backgroundColor: context.containerBackgroundColor,
            title: Text(
              l10n.error,
              style: TextStyle(color: context.accentColor),
            ),
          ),
          body: Center(
            child: Text(
              error.toString(),
              style: TextStyle(color: context.secondaryAccentColor),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildInfoSection(
      BuildContext context, String title, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: TextStyle(
            color: context.accentColor,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Card(
          color: context.containerBackgroundColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
            side: BorderSide(color: context.accentColor),
          ),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: children,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(color: context.secondaryTextColor),
          ),
          Text(
            value,
            style: TextStyle(
              color: context.secondaryAccentColor,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOilChangeSection(BuildContext context, CarModel car) {
    final l10n = S.of(context);
    final currentMileage = car.currentMileage;
    final lastOilChangeMileage = car.lastOilChangeMileage;
    final oilChangeInterval = car.oilChangeMileageInterval;

    final mileageUntilDue =
        (lastOilChangeMileage + oilChangeInterval) - currentMileage;
    final isOverdue = mileageUntilDue <= 0;

    // Calculate days until next oil change based on time interval
    final lastOilChangeDate = car.lastOilChangeDate;
    final interval = Duration(days: car.oilChangeMonthInterval * 30);
    final nextOilChangeDate = lastOilChangeDate.add(interval);
    final daysUntilDue = nextOilChangeDate.difference(DateTime.now()).inDays;

    return _buildInfoSection(
      context,
      l10n.oilChangeStatus,
      [
        _buildInfoRow(
          l10n.lastOilChangeMileage,
          '${car.lastOilChangeMileage} km',
        ),
        _buildInfoRow(
          l10n.nextOilChange('$mileageUntilDue km'),
          isOverdue ? l10n.overdue : '$mileageUntilDue km',
        ),
        if (!isOverdue)
          _buildInfoRow(
            l10n.daysUntilNextChange(daysUntilDue),
            daysUntilDue.toString(),
          ),
        Divider(color: context.accentColor, height: 24, thickness: 0.5),
        _buildInfoRow(
          l10n.timeBasedOilChangeInterval,
          '${car.oilChangeMonthInterval} ${l10n.months}',
        ),
        _buildInfoRow(
          l10n.oilFilter,
          car.filterChanged ? l10n.yes : l10n.no,
        ),
        if (car.oilCost > 0)
          _buildInfoRow(
            l10n.oilCost,
            car.oilCost.toStringAsFixed(2),
          ),
        if (car.filterCost > 0)
          _buildInfoRow(
            l10n.filterCost,
            car.filterCost.toStringAsFixed(2),
          ),
      ],
    );
  }

  void _showDeleteDialog(BuildContext context, WidgetRef ref) {
    final l10n = S.of(context);
    showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: context.containerBackgroundColor,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
          side: BorderSide(color: context.accentColor, width: 1),
        ),
        title: Text(
          l10n.deleteCar,
          style: TextStyle(color: context.accentColor),
        ),
        content: Text(
          l10n.deleteCarConfirmation,
          style: TextStyle(color: context.primaryTextColor),
        ),
        actions: [
          TextButton(
            onPressed: () => context.pop(false),
            child: Text(
              l10n.cancel,
              style: TextStyle(color: context.accentColor),
            ),
          ),
          TextButton(
            onPressed: () => context.pop(true),
            child: Text(
              l10n.delete,
              style: TextStyle(color: context.secondaryAccentColor),
            ),
          ),
        ],
      ),
    ).then((confirmed) async {
      if (confirmed == true) {
        await ref.read(car_repo.carRepositoryProvider).deleteCar(widget.carId);
        if (context.mounted) {
          context.pop();
        }
      }
    });
  }

  Widget _buildOverviewTab(BuildContext context, CarModel car) {
    final l10n = S.of(context);
    final imageCacheService = ImageCacheService();

    // Validate and filter image URLs
    List<String> validImages = [];

    // First check if car has multiple images
    if (car.imageUrls != null && car.imageUrls!.isNotEmpty) {
      for (String url in car.imageUrls!) {
        String? validUrl = imageCacheService.validateAndFixUrl(url);
        if (validUrl != null) {
          validImages.add(validUrl);
        } else {
          dev.log('Skipping invalid image URL: $url');
        }
      }
    }
    // If no valid URLs in imageUrls array, try the single imageUrl
    else if (validImages.isEmpty &&
        car.imageUrl != null &&
        car.imageUrl!.isNotEmpty) {
      String? validUrl = imageCacheService.validateAndFixUrl(car.imageUrl);
      if (validUrl != null) {
        validImages.add(validUrl);
      } else {
        dev.log('Main image URL is invalid: ${car.imageUrl}');
      }
    }

    if (validImages.isEmpty) {
      dev.log('No valid images found for car: ${car.id}');
    } else {
      dev.log('Found ${validImages.length} valid images for car: ${car.id}');
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (validImages.isNotEmpty)
            Container(
              width: double.infinity,
              height: 250,
              margin: const EdgeInsets.only(bottom: 16),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: context.accentColor.withOpacity(0.5),
                  width: 1,
                ),
                boxShadow: [
                  BoxShadow(
                    color: context.accentColor.withOpacity(0.2),
                    blurRadius: 10,
                    offset: const Offset(0, 5),
                  ),
                ],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(15),
                child: ImageCarousel(
                  networkImages: validImages,
                  enableAdd: false,
                  enableDelete: false,
                  showIndicator: validImages.length > 1,
                  autoPlay: validImages.length > 1,
                  height: 250,
                  imageFit: BoxFit.cover,
                ),
              ),
            ),
          _buildInfoSection(
            context,
            l10n.carDetails,
            [
              _buildInfoRow(l10n.make, car.make),
              _buildInfoRow(l10n.model, car.model),
              _buildInfoRow(l10n.year, car.year.toString()),
              _buildInfoRow(l10n.currentMileage, '${car.currentMileage} km'),
              if (car.licenseExpiryDate != null)
                _buildInfoRow(
                  l10n.licenseExpiryDate,
                  DateFormat.yMMMd(l10n.localeName)
                      .format(car.licenseExpiryDate!),
                ),
            ],
          ),
          const SizedBox(height: 16),
          _buildInfoSection(
            context,
            l10n.maintenanceIntervals,
            [
              _buildInfoRow(
                l10n.oilChangeInterval,
                '${car.oilChangeMileageInterval} km',
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildOilChangeSection(context, car),
        ],
      ),
    );
  }

  Widget _buildOilChangeTab(BuildContext context, CarModel car) {
    final l10n = S.of(context);
    final isRTL = Directionality.of(context) == TextDirection.RTL;

    return Column(
      children: [
        Expanded(
          child: OilChangeHistoryScreen(carId: widget.carId),
        ),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: context.containerBackgroundColor,
            border: Border(
              top: BorderSide(color: context.accentColor.withOpacity(0.3)),
            ),
          ),
          child: PrimaryButton(
            text: l10n.recordOilChange,
            icon: Icons.add,
            onPressed: () =>
                context.push('/cars/${widget.carId}/oil-changes/add'),
          ),
        ),
      ],
    );
  }

  Widget _buildMaintenanceTab(
      BuildContext context, CarModel car, WidgetRef ref) {
    final l10n = S.of(context);
    return Column(
      children: [
        Expanded(
          child: MaintenanceHistoryList(
            carId: widget.carId,
            useNotifierProvider: true,
          ),
        ),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: context.containerBackgroundColor,
            border: Border(
              top: BorderSide(color: context.accentColor.withOpacity(0.3)),
            ),
          ),
          child: PrimaryButton(
            text: l10n.addMaintenance,
            icon: Icons.add,
            onPressed: () async {
              // Navigate to add maintenance screen
              final result =
                  await context.push('/cars/${widget.carId}/maintenance/add');

              // Force refresh maintenance data when returning
              ref.invalidate(maintenanceNotifierProvider(widget.carId));
            },
          ),
        ),
      ],
    );
  }
}
