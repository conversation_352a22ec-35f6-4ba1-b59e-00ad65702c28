import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:oil_change_tracker/core/models/car_model.dart';
import 'package:oil_change_tracker/core/utils/firebase_collections.dart';
import 'package:oil_change_tracker/core/services/connectivity_service.dart';
import 'dart:developer' as dev;
import '../../../../core/utils/logger.dart';

final carRepositoryProvider = Provider<CarRepository>((ref) {
  final connectivityService = ref.watch(connectivityServiceProvider);
  return CarRepository(
    firestore: FirebaseFirestore.instance,
    auth: FirebaseAuth.instance,
    connectivityService: connectivityService,
  );
});

class CarRepository {
  final FirebaseFirestore _firestore;
  final FirebaseAuth _auth;
  final ConnectivityService _connectivityService;
  
  // Cache for user cars when offline
  List<CarModel>? _cachedCars;
  DateTime? _lastCacheTime;
  static const cacheValidityDuration = Duration(minutes: 30);

  CarRepository({
    required FirebaseFirestore firestore,
    required FirebaseAuth auth,
    required ConnectivityService connectivityService,
  })  : _firestore = firestore,
        _auth = auth,
        _connectivityService = connectivityService;

  CollectionReference<Map<String, dynamic>> get _carsCollection =>
      _firestore.collection(FirebaseCollections.cars);

  // Check if cache is valid
  bool get _isCacheValid => 
      _cachedCars != null && 
      _lastCacheTime != null && 
      DateTime.now().difference(_lastCacheTime!) < cacheValidityDuration;

  // Watch user's cars with offline support
  Stream<List<CarModel>> watchUserCars() {
    final user = _auth.currentUser;
    if (user == null) throw Exception('User not authenticated');

    // Set up the Firestore query with offline persistence
    final query = _carsCollection
        .where('userId', isEqualTo: user.uid);
    
    // Add cache metadata to the stream
    return query.snapshots().map((snapshot) {
      final cars = snapshot.docs
          .map((doc) => CarModel.fromFirestore(doc))
          .toList()
        ..sort((a, b) {
          int makeComp = a.make.compareTo(b.make);
          if (makeComp != 0) return makeComp;
          return a.model.compareTo(b.model);
        });
      
      // Update cache when online
      if (_connectivityService.isConnected) {
        dev.log('CarRepository: Updating cars cache from Firestore');
        _cachedCars = cars;
        _lastCacheTime = DateTime.now();
      }
      
      // Add metadata about source
      dev.log('CarRepository: Returning ${cars.length} cars from ${snapshot.metadata.isFromCache ? "cache" : "server"}');
      
      return cars;
    });
  }

  Future<List<CarModel>> getUserCars() async {
    final user = _auth.currentUser;
    if (user == null) throw Exception('User not authenticated');

    try {
      // If offline and we have a valid cache, use it
      if (!_connectivityService.isConnected && _isCacheValid) {
        dev.log('CarRepository: Offline - returning cached cars');
        return _cachedCars!;
      }

      // Otherwise, attempt to get from Firestore (which will use local cache if offline)
      final snapshot = await _carsCollection
          .where('userId', isEqualTo: user.uid)
          .get();

      final cars = snapshot.docs
          .map((doc) => CarModel.fromFirestore(doc))
          .toList();
      
      cars.sort((a, b) {
        int makeComp = a.make.compareTo(b.make);
        if (makeComp != 0) return makeComp;
        return a.model.compareTo(b.model);
      });
      
      // Update cache
      _cachedCars = cars;
      _lastCacheTime = DateTime.now();
      
      dev.log('CarRepository: Returning ${cars.length} cars from ${snapshot.metadata.isFromCache ? "cache" : "server"}');
      
      return cars;
    } catch (e) {
      dev.log('CarRepository: Error getting cars: $e');
      
      // Fall back to cache if available, even if outdated
      if (_cachedCars != null) {
        dev.log('CarRepository: Error - falling back to cached cars');
        return _cachedCars!;
      }
      
      rethrow;
    }
  }

  // Alias for getUserCars to maintain compatibility with existing code
  Future<List<CarModel>?> getCars() async {
    try {
      final cars = await getUserCars();
      return cars;
    } catch (e) {
      dev.log('CarRepository: Error in getCars(): $e');
      return null;
    }
  }

  // Get a single car with offline support
  Future<CarModel> getCar(String id) async {
    try {
      // Check cache first if we're offline
      if (!_connectivityService.isConnected && _cachedCars != null) {
        final cachedCar = _cachedCars!.where((car) => car.id == id).firstOrNull;
        if (cachedCar != null) {
          dev.log('CarRepository: Returning cached car $id');
          return cachedCar;
        }
      }
      
      final doc = await _carsCollection.doc(id).get();
      if (!doc.exists) throw Exception('Car not found');
      return CarModel.fromFirestore(doc);
    } catch (e) {
      dev.log('CarRepository: Error getting car $id: $e');
      
      // Last attempt to find in cache
      if (_cachedCars != null) {
        final cachedCar = _cachedCars!.where((car) => car.id == id).firstOrNull;
        if (cachedCar != null) {
          dev.log('CarRepository: Error - falling back to cached car $id');
          return cachedCar;
        }
      }
      
      rethrow;
    }
  }

  // Add a new car
  Future<String> addCar(CarModel car) async {
    try {
      // Ensure user is authenticated
      final user = _auth.currentUser;
      if (user == null) {
        throw Exception('User not authenticated');
      }
      
      // Verify car belongs to current user
      if (car.userId != user.uid) {
        throw Exception('Car does not belong to authenticated user');
      }
      
      AppLogger.info('[CarRepository] Adding car - Auth check passed. User ID: ${user.uid}');
      AppLogger.info('[CarRepository] Car data before timestamps: ${car.toJson()}');
      
      // Add timestamps for created/updated
      final now = DateTime.now();
      final carWithTimestamps = car.copyWith(
        createdAt: now,
        updatedAt: now,
      );
      
      AppLogger.info('[CarRepository] Car data after timestamps: ${carWithTimestamps.toJson()}');
      
      // Convert to Firestore data
      final firestoreData = carWithTimestamps.toFirestore();
      AppLogger.info('[CarRepository] Data being sent to Firestore: $firestoreData');
      
      // Add to Firestore
      final docRef = await _carsCollection.add(firestoreData);
      AppLogger.info('[CarRepository] Successfully added car with ID: ${docRef.id}');
      
      // Update the car with the Firestore ID
      final carWithId = carWithTimestamps.copyWith(id: docRef.id);
      
      // Update cache
      _updateLocalCache(carWithId);
      AppLogger.info('[CarRepository] Updated local cache with new car');
      
      return docRef.id;
    } catch (e) {
      AppLogger.error('[CarRepository] Error adding car: $e');
      rethrow;
    }
  }

  // Update car details
  Future<void> updateCar(CarModel car) async {
    if (car.id == null) throw Exception('Car ID cannot be null');
    
    final user = _auth.currentUser;
    if (user == null) throw Exception('User not authenticated');

    if (!_connectivityService.isConnected) {
      throw Exception('Cannot update a car while offline. Please connect to the internet and try again.');
    }

    // Update timestamp
    final carWithTimestamp = car.copyWith(
      updatedAt: DateTime.now(),
    );

    await _carsCollection.doc(car.id).update(carWithTimestamp.toFirestore());
    
    // Update local cache
    if (_cachedCars != null) {
      final index = _cachedCars!.indexWhere((c) => c.id == car.id);
      if (index >= 0) {
        _cachedCars![index] = carWithTimestamp;
        _lastCacheTime = DateTime.now();
      }
    }
  }

  // Delete car
  Future<void> deleteCar(String id) async {
    final user = _auth.currentUser;
    if (user == null) throw Exception('User not authenticated');

    if (!_connectivityService.isConnected) {
      throw Exception('Cannot delete a car while offline. Please connect to the internet and try again.');
    }

    await _carsCollection.doc(id).delete();
    
    // Update local cache
    if (_cachedCars != null) {
      _cachedCars!.removeWhere((c) => c.id == id);
      _lastCacheTime = DateTime.now();
    }
  }

  // Update car mileage
  Future<void> updateCarMileage(String carId, int newMileage) async {
    final user = _auth.currentUser;
    if (user == null) throw Exception('User not authenticated');

    if (!_connectivityService.isConnected) {
      throw Exception('Cannot update mileage while offline. Please connect to the internet and try again.');
    }

    await _carsCollection.doc(carId).update({
      'currentMileage': newMileage,
    });
    
    // Update local cache
    if (_cachedCars != null) {
      final index = _cachedCars!.indexWhere((c) => c.id == carId);
      if (index >= 0) {
        _cachedCars![index] = _cachedCars![index].copyWith(currentMileage: newMileage);
        _lastCacheTime = DateTime.now();
      }
    }
  }

  // Record oil change
  Future<void> recordOilChange(String carId, DateTime date, int mileage) async {
    final user = _auth.currentUser;
    if (user == null) throw Exception('User not authenticated');

    final batch = _firestore.batch();
    final carRef = _carsCollection.doc(carId);

    batch.update(carRef, {
      'lastOilChangeDate': Timestamp.fromDate(date),
      'lastOilChangeMileage': mileage,
    });

    await batch.commit();
  }

  // Update car's oil change information
  Future<void> updateCarOilChangeInfo(String carId, DateTime date, int mileage) async {
    final user = _auth.currentUser;
    if (user == null) throw Exception('User not authenticated');

    if (!_connectivityService.isConnected) {
      throw Exception('Cannot update oil change info while offline. Please connect to the internet and try again.');
    }

    // Get current car data
    final car = await getCar(carId);

    // Update with new oil change info
    final updatedCar = car.copyWith(
      lastOilChangeDate: date,
      lastOilChangeMileage: mileage,
      updatedAt: DateTime.now(),
    );

    // Update in Firestore
    await _carsCollection.doc(carId).update(updatedCar.toFirestore());
    
    // Update local cache
    if (_cachedCars != null) {
      final index = _cachedCars!.indexWhere((c) => c.id == carId);
      if (index >= 0) {
        _cachedCars![index] = updatedCar;
        _lastCacheTime = DateTime.now();
      }
    }
  }

  // Helper method to update local cache
  void _updateLocalCache(CarModel car) {
    if (_cachedCars != null) {
      final index = _cachedCars!.indexWhere((c) => c.id == car.id);
      if (index >= 0) {
        _cachedCars![index] = car;
      } else {
        _cachedCars!.add(car);
        _cachedCars!.sort((a, b) {
          int makeComp = a.make.compareTo(b.make);
          if (makeComp != 0) return makeComp;
          return a.model.compareTo(b.model);
        });
      }
      _lastCacheTime = DateTime.now();
    }
  }
} 