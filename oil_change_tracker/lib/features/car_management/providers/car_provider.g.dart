// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'car_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$carHash() => r'3941de2f1f9f283e8ed12c741e4f51c32940b4a9';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// See also [car].
@ProviderFor(car)
const carProvider = CarFamily();

/// See also [car].
class CarFamily extends Family<AsyncValue<CarModel?>> {
  /// See also [car].
  const CarFamily();

  /// See also [car].
  CarProvider call(
    String carId,
  ) {
    return CarProvider(
      carId,
    );
  }

  @override
  CarProvider getProviderOverride(
    covariant CarProvider provider,
  ) {
    return call(
      provider.carId,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'carProvider';
}

/// See also [car].
class CarProvider extends AutoDisposeFutureProvider<CarModel?> {
  /// See also [car].
  CarProvider(
    String carId,
  ) : this._internal(
          (ref) => car(
            ref as CarRef,
            carId,
          ),
          from: carProvider,
          name: r'carProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product') ? null : _$carHash,
          dependencies: CarFamily._dependencies,
          allTransitiveDependencies: CarFamily._allTransitiveDependencies,
          carId: carId,
        );

  CarProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.carId,
  }) : super.internal();

  final String carId;

  @override
  Override overrideWith(
    FutureOr<CarModel?> Function(CarRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: CarProvider._internal(
        (ref) => create(ref as CarRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        carId: carId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<CarModel?> createElement() {
    return _CarProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is CarProvider && other.carId == carId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, carId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin CarRef on AutoDisposeFutureProviderRef<CarModel?> {
  /// The parameter `carId` of this provider.
  String get carId;
}

class _CarProviderElement extends AutoDisposeFutureProviderElement<CarModel?>
    with CarRef {
  _CarProviderElement(super.provider);

  @override
  String get carId => (origin as CarProvider).carId;
}

String _$carsHash() => r'7521c2527a88dfde7e6eb47dbfec97b8a0281560';

/// See also [Cars].
@ProviderFor(Cars)
final carsProvider =
    AutoDisposeStreamNotifierProvider<Cars, List<CarModel>>.internal(
  Cars.new,
  name: r'carsProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$carsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$Cars = AutoDisposeStreamNotifier<List<CarModel>>;
String _$carNotifierHash() => r'93bbffe4b076f9e7ecb5fc5f2c2e822d1924e218';

abstract class _$CarNotifier
    extends BuildlessAutoDisposeAsyncNotifier<CarModel?> {
  late final String? carId;

  FutureOr<CarModel?> build(
    String? carId,
  );
}

/// See also [CarNotifier].
@ProviderFor(CarNotifier)
const carNotifierProvider = CarNotifierFamily();

/// See also [CarNotifier].
class CarNotifierFamily extends Family<AsyncValue<CarModel?>> {
  /// See also [CarNotifier].
  const CarNotifierFamily();

  /// See also [CarNotifier].
  CarNotifierProvider call(
    String? carId,
  ) {
    return CarNotifierProvider(
      carId,
    );
  }

  @override
  CarNotifierProvider getProviderOverride(
    covariant CarNotifierProvider provider,
  ) {
    return call(
      provider.carId,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'carNotifierProvider';
}

/// See also [CarNotifier].
class CarNotifierProvider
    extends AutoDisposeAsyncNotifierProviderImpl<CarNotifier, CarModel?> {
  /// See also [CarNotifier].
  CarNotifierProvider(
    String? carId,
  ) : this._internal(
          () => CarNotifier()..carId = carId,
          from: carNotifierProvider,
          name: r'carNotifierProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$carNotifierHash,
          dependencies: CarNotifierFamily._dependencies,
          allTransitiveDependencies:
              CarNotifierFamily._allTransitiveDependencies,
          carId: carId,
        );

  CarNotifierProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.carId,
  }) : super.internal();

  final String? carId;

  @override
  FutureOr<CarModel?> runNotifierBuild(
    covariant CarNotifier notifier,
  ) {
    return notifier.build(
      carId,
    );
  }

  @override
  Override overrideWith(CarNotifier Function() create) {
    return ProviderOverride(
      origin: this,
      override: CarNotifierProvider._internal(
        () => create()..carId = carId,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        carId: carId,
      ),
    );
  }

  @override
  AutoDisposeAsyncNotifierProviderElement<CarNotifier, CarModel?>
      createElement() {
    return _CarNotifierProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is CarNotifierProvider && other.carId == carId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, carId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin CarNotifierRef on AutoDisposeAsyncNotifierProviderRef<CarModel?> {
  /// The parameter `carId` of this provider.
  String? get carId;
}

class _CarNotifierProviderElement
    extends AutoDisposeAsyncNotifierProviderElement<CarNotifier, CarModel?>
    with CarNotifierRef {
  _CarNotifierProviderElement(super.provider);

  @override
  String? get carId => (origin as CarNotifierProvider).carId;
}

String _$carsNotifierHash() => r'9d1e7a068b1174987a2ccede1fb8164c8c0b0692';

/// See also [CarsNotifier].
@ProviderFor(CarsNotifier)
final carsNotifierProvider =
    AutoDisposeAsyncNotifierProvider<CarsNotifier, List<CarModel>>.internal(
  CarsNotifier.new,
  name: r'carsNotifierProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$carsNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$CarsNotifier = AutoDisposeAsyncNotifier<List<CarModel>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
