import '../../../core/models/car_model.dart';
import '../data/repositories/car_repository.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:flutter/material.dart'; // Import Material for BuildContext
import 'package:riverpod/riverpod.dart'; // Import for Ref class
import '../../../core/services/connectivity_service.dart';
import '../../../core/utils/logger.dart';
import '../../../core/services/notification_service.dart';
import '../../../features/ads/presentation/managers/interstitial_ad_manager.dart';
import '../../../features/subscription/providers/feature_gate_provider.dart';

part 'car_provider.g.dart';

final carRepositoryProvider = Provider<CarRepository>((ref) {
  final connectivityService = ref.watch(connectivityServiceProvider);
  return CarRepository(
    firestore: FirebaseFirestore.instance,
    auth: FirebaseAuth.instance,
    connectivityService: connectivityService,
  );
});

@riverpod
class Cars extends _$Cars {
  @override
  Stream<List<CarModel>> build() {
    final user = FirebaseAuth.instance.currentUser;
    if (user == null) return Stream.value([]);

    return ref.watch(carRepositoryProvider).watchUserCars();
  }

  Future<String> addCar(CarModel car) async {
    try {
      state = const AsyncLoading();
      AppLogger.info('[CarsProvider] Adding car with data: ${car.toJson()}');

      // Check if the user has reached the vehicle limit
      final currentCars = await ref.read(carRepositoryProvider).getCars();
      final hasUnlimitedVehicles =
          ref.read(featureGateProvider(PremiumFeature.unlimitedVehicles));

      // Free users are limited to 3 vehicles
      if (!hasUnlimitedVehicles &&
          currentCars != null &&
          currentCars.length >= 3) {
        throw Exception(
            'Free users are limited to 3 vehicles. Please upgrade to Premium for unlimited vehicles.');
      }

      final carId = await ref.read(carRepositoryProvider).addCar(car);

      AppLogger.info('[CarsProvider] Successfully added car');

      // Refresh the cars list instead of modifying state directly
      ref.invalidateSelf();

      return carId;
    } catch (e) {
      AppLogger.error('[CarsProvider] Error adding car: $e');
      state = AsyncError(e, StackTrace.current);
      rethrow;
    }
  }

  Future<void> updateCar(CarModel car) async {
    try {
      await ref.read(carRepositoryProvider).updateCar(car);
      // Refresh the cars list
      ref.invalidateSelf();
    } catch (e) {
      throw Exception('Failed to update car: ${e.toString()}');
    }
  }

  Future<void> deleteCar(String carId) async {
    try {
      await ref.read(carRepositoryProvider).deleteCar(carId);
    } catch (e) {
      throw Exception('Failed to delete car: ${e.toString()}');
    }
  }
}

@riverpod
Future<CarModel?> car(Ref ref, String carId) {
  return ref.read(carRepositoryProvider).getCar(carId);
}

@riverpod
class CarNotifier extends _$CarNotifier {
  @override
  FutureOr<CarModel?> build(String? carId) {
    if (carId == null) return null;
    return ref.read(carRepositoryProvider).getCar(carId);
  }

  Future<void> updateCarMileage(
      String carId, int newMileage, BuildContext context) async {
    try {
      state = const AsyncLoading();

      // Get the current car
      final car = await ref.read(carRepositoryProvider).getCar(carId);

      // Validate new mileage
      if (newMileage <= car.currentMileage) {
        throw Exception('New mileage must be greater than current mileage');
      }

      // Update the car with new mileage
      final updatedCar = car.updateMileage(newMileage);
      await ref.read(carRepositoryProvider).updateCar(updatedCar);

      // Refresh the cars list
      ref.invalidate(carsProvider);

      // Schedule notifications for updated car
      await _scheduleNotifications(updatedCar, context);

      // Show interstitial ad after successful mileage update
      ref.read(interstitialAdManagerProvider.notifier).showAdIfAvailable();

      state = AsyncData(updatedCar);
    } catch (e, st) {
      state = AsyncError(e, st);
    }
  }

  Future<void> _scheduleNotifications(
      CarModel car, BuildContext context) async {
    try {
      if (car.id == null) {
        AppLogger.warning(
            '[CarNotifier] Cannot schedule notifications for car with null ID');
        return;
      }

      final notificationService = ref.read(notificationServiceProvider);
      await notificationService.scheduleOilChangeNotification(car, context);
      if (car.licenseExpiryDate != null) {
        await notificationService.scheduleLicenseExpiryNotification(
            car, context);
      }
    } catch (e) {
      AppLogger.error('[CarNotifier] Error scheduling notifications: $e');
      // Don't rethrow, just log - notification failure shouldn't break app flow
    }
  }
}

@riverpod
class CarsNotifier extends _$CarsNotifier {
  @override
  Future<List<CarModel>> build() async {
    final cars = await ref.read(carRepositoryProvider).getCars();
    return cars ?? [];
  }

  Future<void> addCar(CarModel car, BuildContext context) async {
    state = const AsyncLoading();
    try {
      // Check if the user has reached the vehicle limit
      final currentCars = await ref.read(carRepositoryProvider).getCars();
      final hasUnlimitedVehicles =
          ref.read(featureGateProvider(PremiumFeature.unlimitedVehicles));

      // Free users are limited to 3 vehicles
      if (!hasUnlimitedVehicles &&
          currentCars != null &&
          currentCars.length >= 3) {
        throw Exception(
            'Free users are limited to 3 vehicles. Please upgrade to Premium for unlimited vehicles.');
      }

      final newCarId = await ref.read(carRepositoryProvider).addCar(car);

      if (newCarId.isNotEmpty) {
        // First update the cars list to ensure we have the latest data
        final updatedCars = await ref.read(carRepositoryProvider).getCars();
        state = AsyncData(updatedCars ?? []);

        // Now try to schedule notifications after the state is updated
        try {
          // Find the car in the updated list to ensure it has an ID
          final newCar = updatedCars?.firstWhere(
            (c) => c.id == newCarId,
            orElse: () => car.copyWith(id: newCarId),
          );

          // Double check that we have a valid car with ID
          if (newCar != null && newCar.id != null && newCar.id!.isNotEmpty) {
            // Schedule notification for the new car
            await _scheduleNotifications(newCar, context);
          } else {
            // Try to get the car directly as a fallback
            try {
              final fetchedCar =
                  await ref.read(carRepositoryProvider).getCar(newCarId);
              if (fetchedCar.id != null && fetchedCar.id!.isNotEmpty) {
                await _scheduleNotifications(fetchedCar, context);
              } else {
                AppLogger.warning(
                    '[CarsNotifier] Could not schedule notifications - car has no ID');
              }
            } catch (fetchError) {
              AppLogger.error(
                  '[CarsNotifier] Error fetching car for notifications: $fetchError');
            }
          }
        } catch (notificationError) {
          // Log the error but don't fail the whole operation
          AppLogger.error(
              '[CarsNotifier] Error scheduling notifications: $notificationError');
        }
      }
    } catch (e, st) {
      AppLogger.error('[CarsNotifier] Error adding car: $e');
      state = AsyncError(e, st);
    }
  }

  Future<void> updateCar(CarModel car, BuildContext context) async {
    state = const AsyncLoading();
    try {
      AppLogger.info('[CarsNotifier] Updating car with data: ${car.toJson()}');

      // Validate car has an ID
      if (car.id == null || car.id!.isEmpty) {
        throw Exception('Cannot update car without an ID');
      }

      // Update the car in the repository
      await ref.read(carRepositoryProvider).updateCar(car);

      // Refresh the cars list first to ensure state is updated
      final updatedCars = await ref.read(carRepositoryProvider).getCars();
      state = AsyncData(updatedCars ?? []);

      // Now try to schedule notifications after the state is updated
      try {
        // Find the car in the updated list to ensure it has the latest data
        final carId = car.id!;
        final updatedCar = updatedCars?.firstWhere(
          (c) => c.id == carId,
          orElse: () => car,
        );

        // Double check that we have a valid car with ID
        if (updatedCar != null &&
            updatedCar.id != null &&
            updatedCar.id!.isNotEmpty) {
          // Update notifications for the changed car
          await _scheduleNotifications(updatedCar, context);
        } else {
          // Try to get the car directly as a fallback
          try {
            final fetchedCar =
                await ref.read(carRepositoryProvider).getCar(carId);
            if (fetchedCar.id != null && fetchedCar.id!.isNotEmpty) {
              await _scheduleNotifications(fetchedCar, context);
            } else {
              AppLogger.warning(
                  '[CarsNotifier] Could not schedule notifications - car has no ID');
            }
          } catch (fetchError) {
            AppLogger.error(
                '[CarsNotifier] Error fetching car for notifications: $fetchError');
          }
        }
      } catch (notificationError) {
        // Log the error but don't fail the whole operation
        AppLogger.error(
            '[CarsNotifier] Error scheduling notifications after update: $notificationError');
      }

      AppLogger.info('[CarsNotifier] Car updated successfully');
    } catch (e, st) {
      AppLogger.error('[CarsNotifier] Error updating car: $e');
      state = AsyncError(e, st);
    }
  }

  Future<void> deleteCar(String carId) async {
    state = const AsyncLoading();
    try {
      await ref.read(carRepositoryProvider).deleteCar(carId);

      // Cancel notifications for deleted car
      final notificationService = ref.read(notificationServiceProvider);
      await notificationService.cancelNotificationsForCar(carId);

      final updatedCars = await ref.read(carRepositoryProvider).getCars();
      state = AsyncData(updatedCars ?? []);
    } catch (e, st) {
      state = AsyncError(e, st);
    }
  }

  // This private method is duplicated in CarNotifier, consider refactoring to a common place or service extension
  Future<void> _scheduleNotifications(
      CarModel car, BuildContext context) async {
    try {
      if (car.id == null) {
        AppLogger.warning(
            '[CarsNotifier] Cannot schedule notifications for car with null ID');
        return;
      }

      final notificationService = ref.read(notificationServiceProvider);
      await notificationService.scheduleOilChangeNotification(car, context);
      if (car.licenseExpiryDate != null) {
        await notificationService.scheduleLicenseExpiryNotification(
            car, context);
      }
    } catch (e) {
      AppLogger.error('[CarsNotifier] Error scheduling notifications: $e');
      // Don't rethrow - notification failure shouldn't break app flow
    }
  }

  Future<void> rescheduleAllNotifications(BuildContext context) async {
    try {
      final carsList = await ref.read(carRepositoryProvider).getCars();
      if (carsList != null && carsList.isNotEmpty) {
        // Filter out cars with null IDs to prevent errors
        final validCars = carsList
            .where((car) => car.id != null && car.id!.isNotEmpty)
            .toList();

        if (validCars.isNotEmpty) {
          final notificationService = ref.read(notificationServiceProvider);
          await notificationService.scheduleNotificationsForAllCars(
              validCars, context);
          AppLogger.info(
              '[CarsNotifier] Successfully rescheduled notifications for ${validCars.length} cars');
        } else {
          AppLogger.warning(
              '[CarsNotifier] No valid cars with IDs found for rescheduling notifications');
        }
      } else {
        AppLogger.info(
            '[CarsNotifier] No cars found for rescheduling notifications');
      }
    } catch (e) {
      AppLogger.error(
          '[CarsNotifier] Error rescheduling all notifications: $e');
    }
  }
}
