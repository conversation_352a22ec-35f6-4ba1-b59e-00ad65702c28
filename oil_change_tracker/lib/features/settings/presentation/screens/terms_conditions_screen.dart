import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/theme/theme_extensions.dart';
import '../../../../generated/app_localizations.dart';

class TermsConditionsScreen extends ConsumerWidget {
  const TermsConditionsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final l10n = S.of(context);
    
    // Using localized strings instead of hardcoded strings
    final sections = {
      l10n.termsAcceptance: l10n.termsAcceptanceText,
      l10n.appUsage: l10n.appUsageText,
      l10n.userAccounts: l10n.userAccountsText,
      l10n.userContent: l10n.userContentText,
      l10n.intellectualProperty: l10n.intellectualPropertyText,
      l10n.disclaimerWarranties: l10n.disclaimerWarrantiesText,
      l10n.limitationLiability: l10n.limitationLiabilityText,
      l10n.indemnification: l10n.indemnificationText,
      l10n.disputeResolution: l10n.disputeResolutionText,
      l10n.severability: l10n.severabilityText,
      l10n.termsModifications: l10n.termsModificationsText,
      l10n.notifyChanges: l10n.notifyChangesText,
      l10n.governingLaw: l10n.governingLawText,
      l10n.contactUs: l10n.contactUsText
    };
    
    return Scaffold(
      backgroundColor: context.containerBackgroundColor,
      appBar: AppBar(
        title: Text(
          l10n.termsAndConditions,
          style: TextStyle(
            color: context.accentColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: context.containerBackgroundColor,
        elevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: context.accentColor),
          onPressed: () => context.pop(),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Build sections dynamically from the map
            ...sections.entries.expand((entry) => [
              _buildSectionTitle(context, entry.key),
              const SizedBox(height: 12),
              _buildParagraph(context, entry.value),
              const SizedBox(height: 24),
            ]),
            
            // Last updated text 
            const SizedBox(height: 16),
            Text(
              "${l10n.lastUpdated}: ${DateTime.now().toString().substring(0, 10)}",
              style: TextStyle(
                color: context.secondaryTextColor.withOpacity(0.7),
                fontSize: 12,
                fontStyle: FontStyle.italic,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionTitle(BuildContext context, String title) {
    return Text(
      title,
      style: TextStyle(
        color: context.accentColor,
        fontSize: 20,
        fontWeight: FontWeight.bold,
      ),
    );
  }

  Widget _buildParagraph(BuildContext context, String text) {
    return Text(
      text,
      style: TextStyle(
        color: context.primaryTextColor,
        fontSize: 16,
        height: 1.5,
      ),
    );
  }
} 