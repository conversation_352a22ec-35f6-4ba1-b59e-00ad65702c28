import 'package:flutter/material.dart';
import 'package:gpt_markdown/gpt_markdown.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/theme/theme_extensions.dart';
import '../../../../generated/app_localizations.dart';

class RefundPolicyScreen extends ConsumerWidget {
  const RefundPolicyScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final s = S.of(context);

    const _md = '''
# Refund Policy

We follow Google Play's standard refund guidelines and strive for transparency.

---

## 1️⃣ 48-Hour Refunds via Google Play
* Within the first **48 hours** after purchase, you may request a full refund directly from Google Play:
  1. Open **payments.google.com** or the **Play Store ➜ Order history**.
  2. Select the Oil Change Tracker+ transaction.
  3. Tap **Refund / Report a problem** and choose the appropriate reason.

## 2️⃣ After 48 Hours
* Outside the 48-hour window, refunds are generally **not guaranteed** by Google.
* We will consider exceptions for proven accidental purchases or technical issues preventing usage.
* Email **<EMAIL>** with your ** GPA-order number** and a brief explanation.

## 3️⃣ Subscriptions
* Cancelling a subscription stops future billing but **does not automatically refund** the current period.
* You retain premium access until the end of the paid cycle.

## 4️⃣ Duplicate Charges
If you believe you were double-charged, contact us (include screenshots) and we'll work with Google to resolve it.

_Last updated: July 2025_
''';

    return Scaffold(
      backgroundColor: context.containerBackgroundColor,
      appBar: AppBar(
        title: Text(
          s.refundPolicy,
          style: TextStyle(
            color: context.accentColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: context.containerBackgroundColor,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: context.accentColor),
          onPressed: () => context.pop(),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: SingleChildScrollView(
          child: GPTMarkdown(
            _md,
            config: MarkdownConfig(
              configs: [
                PConfig(
                  textStyle: TextStyle(
                      color: context.primaryTextColor, fontSize: 16, height: 1.4),
                ),
                H1Config(
                  style: H1Style(
                    textStyle: TextStyle(
                        color: context.accentColor,
                        fontSize: 24,
                        fontWeight: FontWeight.bold),
                  ),
                ),
                H2Config(
                  style: H2Style(
                    textStyle: TextStyle(
                        color: context.accentColor,
                        fontSize: 20,
                        fontWeight: FontWeight.bold),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
