import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/providers/notification_settings_provider.dart';
import '../../../../generated/app_localizations.dart';

class NotificationSettingsScreen extends ConsumerWidget {
  const NotificationSettingsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final l10n = S.of(context);
    final settings = ref.watch(notificationSettingsNotifierProvider);

    return Scaffold(
      appBar: AppBar(
        title: Text(l10n.notificationSettings),
      ),
      body: ListView(
        children: [
          _buildNotificationOption(
            context,
            title: l10n.notifications,
            subtitle: l10n.notificationSettings,
            value: settings.promotionalNotifications,
            onChanged: (value) => ref.read(notificationSettingsNotifierProvider.notifier).togglePromotionalNotifications(),
          ),
          _buildNotificationOption(
            context,
            title: l10n.oilChangeReminders,
            subtitle: l10n.oilChangeRemindersDesc,
            value: settings.oilChangeReminders,
            onChanged: (value) => ref.read(notificationSettingsNotifierProvider.notifier).toggleOilChangeReminders(),
          ),
          _buildNotificationOption(
            context,
            title: l10n.maintenanceReminders,
            subtitle: l10n.maintenanceRemindersDesc,
            value: settings.maintenanceReminders,
            onChanged: (value) => ref.read(notificationSettingsNotifierProvider.notifier).toggleMaintenanceReminders(),
          ),
          _buildNotificationOption(
            context,
            title: l10n.mileageReminders,
            subtitle: l10n.mileageRemindersDesc,
            value: settings.mileageReminders,
            onChanged: (value) => ref.read(notificationSettingsNotifierProvider.notifier).toggleMileageReminders(),
          ),
          // FCM Token refresh section
          if (settings.error != null)
            Card(
              margin: const EdgeInsets.all(16),
              color: Colors.red.shade100,
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      l10n.error,
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    Text(l10n.failedToRefreshToken ?? "Failed to refresh FCM token"),
                  ],
                ),
              ),
            ),
            
          Padding(
            padding: const EdgeInsets.all(16),
            child: ElevatedButton(
              onPressed: () => ref.read(notificationSettingsNotifierProvider.notifier).refreshFcmToken(),
              child: Text(l10n.refreshToken ?? "Refresh FCM Token"),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNotificationOption(
    BuildContext context, {
    required String title,
    required String subtitle,
    required bool value,
    required Function(bool) onChanged,
  }) {
    return SwitchListTile(
      title: Text(title),
      subtitle: Text(subtitle),
      value: value,
      onChanged: onChanged,
    );
  }
} 