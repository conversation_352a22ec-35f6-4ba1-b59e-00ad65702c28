import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/theme/theme_extensions.dart';
import '../../../../generated/app_localizations.dart';

class PrivacyPolicyScreen extends ConsumerWidget {
  const PrivacyPolicyScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final l10n = S.of(context);
    
    // Using localized strings with the comprehensive privacy policy
    final sections = [
      {
        'title': l10n.privacyPolicyIntro,
        'content': l10n.privacyPolicyIntroText,
      },
      {
        'title': l10n.privacyPolicyInfo,
        'content': l10n.privacyPolicyInfoText,
      },
      {
        'title': l10n.privacyPolicyUse,
        'content': l10n.privacyPolicyUseText,
      },
      {
        'title': l10n.privacyPolicyStorage,
        'content': l10n.privacyPolicyStorageText,
      },
      {
        'title': l10n.privacyPolicyRights,
        'content': l10n.privacyPolicyRightsText,
      },
      {
        'title': l10n.privacyPolicyChildren,
        'content': l10n.privacyPolicyChildrenText,
      },
      {
        'title': l10n.privacyPolicyThirdParty,
        'content': l10n.privacyPolicyThirdPartyText,
      },
      {
        'title': l10n.privacyPolicyChanges,
        'content': l10n.privacyPolicyChangesText,
      },
      {
        'title': l10n.contactUs,
        'content': l10n.contactUsText,
      }
    ];
    
    return Scaffold(
      backgroundColor: context.containerBackgroundColor,
      appBar: AppBar(
        title: Text(
          l10n.privacyPolicy,
          style: TextStyle(
            color: context.accentColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: context.containerBackgroundColor,
        elevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: context.accentColor),
          onPressed: () => context.pop(),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Build sections dynamically
            ...sections.expand((section) => [
              _buildSectionTitle(context, section['title'] as String),
              const SizedBox(height: 12),
              _buildParagraph(context, section['content'] as String),
              const SizedBox(height: 24),
            ]),
            
            // Last updated text 
            const SizedBox(height: 16),
            Text(
              "${l10n.lastUpdated}: ${DateTime.now().toString().substring(0, 10)}",
              style: TextStyle(
                color: context.secondaryTextColor.withOpacity(0.7),
                fontSize: 12,
                fontStyle: FontStyle.italic,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionTitle(BuildContext context, String title) {
    return Text(
      title,
      style: TextStyle(
        color: context.accentColor,
        fontSize: 20,
        fontWeight: FontWeight.bold,
      ),
    );
  }

  Widget _buildParagraph(BuildContext context, String text) {
    return Text(
      text,
      style: TextStyle(
        color: context.primaryTextColor,
        fontSize: 16,
        height: 1.5,
      ),
    );
  }
} 