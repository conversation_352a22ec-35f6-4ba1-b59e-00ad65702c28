import 'dart:async';
import 'dart:convert';
import 'dart:developer' as dev;
import 'dart:math' as math;
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Provider for the Integrity Service
final integrityServiceProvider = Provider<IntegrityService>((ref) {
  return IntegrityService();
});

/// A service for handling Play Integrity API (replacement for SafetyNet)
class IntegrityService {
  /// The method channel for communication with the native platform
  static const MethodChannel _channel = MethodChannel('com.maximummdeia.oil_change_tracker/integrity');
  
  /// Request a Play Integrity token
  /// 
  /// This is used for app attestation and verification with Firebase or other backends
  /// It replaces the deprecated SafetyNet attestation
  Future<String?> requestIntegrityToken() async {
    try {
      // Generate a random nonce for the request
      final nonce = _generateNonce();
      
      dev.log('Requesting Play Integrity token with nonce: $nonce');
      
      // Request the token through the method channel
      final String token = await _channel.invokeMethod('requestIntegrityToken', {
        'nonce': nonce,
      });
      
      dev.log('Successfully received Play Integrity token');
      return token;
    } on PlatformException catch (e) {
      dev.log('Failed to get Play Integrity token: ${e.message}');
      return null;
    } catch (e) {
      dev.log('Error in requestIntegrityToken: $e');
      return null;
    }
  }
  
  /// Generate a random nonce for the integrity request
  String _generateNonce() {
    final random = math.Random.secure();
    final values = List<int>.generate(32, (i) => random.nextInt(256));
    return base64Url.encode(values);
  }
} 