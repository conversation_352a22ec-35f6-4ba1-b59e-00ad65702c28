import 'package:cloud_functions/cloud_functions.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:logger/logger.dart';
import '../core/services/app_check_service.dart' show AppCheckService, appCheckServiceProvider, forceRefreshToken, limitedUseTokenProvider;
import 'notification_service.dart';
import 'dart:async';

/// Provider for the FirebaseFunctionsService
final firebaseFunctionsServiceProvider = Provider<FirebaseFunctionsService>((ref) {
  final notificationService = ref.watch(notificationServiceProvider);
  final appCheckService = ref.watch(appCheckServiceProvider);
  return FirebaseFunctionsService(notificationService, appCheckService, ref);
});

/// Service for calling Firebase Cloud Functions
class FirebaseFunctionsService {
  // Initialize with region
  final _functions = FirebaseFunctions.instanceFor(region: 'us-central1');
  final _logger = Logger();
  final NotificationService _notificationService;
  final AppCheckService _appCheckService;
  final _auth = FirebaseAuth.instance;
  final Ref _ref;
  
  // Track authentication status
  DateTime? _lastAuthRefresh;
  int _consecutiveErrors = 0;
  static const int _maxRetries = 3;
  static const int _minRefreshInterval = 60; // seconds
  
  // Track current authentication state
  bool _isAuthorizationInProgress = false;

  FirebaseFunctionsService(this._notificationService, this._appCheckService, this._ref) {
    // Set emulator for local testing if in debug mode
    if (kDebugMode) {
      // Uncomment if you want to use the emulator
      // _functions.useFunctionsEmulator('localhost', 5001);
    }
    
    // Set up auth state listener
    _auth.authStateChanges().listen((user) {
      if (user != null) {
        _logger.i('Auth state changed - refreshing tokens proactively');
        _refreshAuthTokens();
      }
    });
  }
  
  // Internal method to refresh auth tokens without exposing outside
  Future<void> _refreshAuthTokens() async {
    try {
      if (_auth.currentUser == null) return;
      
      // Force token refresh
      await _auth.currentUser!.getIdToken(true);
      _lastAuthRefresh = DateTime.now();
      _logger.i('Auth token refreshed for ${_auth.currentUser!.email}');
    } catch (e) {
      _logger.e('Error refreshing auth token: $e');
    }
  }

  /// Helper method to ensure authentication before making function calls
  Future<bool> _ensureAuthenticated() async {
    // If authentication is already in progress, wait for it to complete
    if (_isAuthorizationInProgress) {
      _logger.i('Auth refresh already in progress, waiting...');
      // Wait for a short period to allow other auth process to complete
      await Future.delayed(const Duration(seconds: 2));
    }
    
    try {
      _isAuthorizationInProgress = true;
      
      // Check if user is already authenticated
      if (_auth.currentUser == null) {
        _logger.e('No user is currently authenticated');
        _isAuthorizationInProgress = false;
        return false;
      }
      
      // Get current user
      final user = _auth.currentUser!;
      
      // Check if we need to refresh the token
      final now = DateTime.now();
      final shouldRefresh = _lastAuthRefresh == null || 
          now.difference(_lastAuthRefresh!).inSeconds > _minRefreshInterval;
      
      // Force token refresh only when needed to avoid rate limiting
      if (shouldRefresh) {
        _logger.i('Refreshing authentication token for ${user.email}');
        
        // Get a fresh ID token with forced refresh
        final idToken = await user.getIdToken(true);
        _lastAuthRefresh = now;
        
        if (idToken == null || idToken.isEmpty) {
          _logger.e('Failed to get valid ID token');
          _isAuthorizationInProgress = false;
          return false;
        }
        
        _logger.i('Successfully refreshed auth token for ${user.email}');
      }
      
      // Ensure App Check is initialized
      if (!_appCheckService.isInitialized) {
        await _appCheckService.initialize();
      }
      
      // Add a small delay to ensure tokens propagate through the system
      await Future.delayed(const Duration(milliseconds: 500));
      
      _logger.i('Authentication verified for user: ${user.email}');
      _isAuthorizationInProgress = false;
      return true;
    } catch (e) {
      _logger.e('Error ensuring authentication: $e');
      _isAuthorizationInProgress = false;
      return false;
    }
  }
  
  /// Get a limited-use App Check token for secure functions
  Future<String?> _getLimitedUseToken() async {
    try {
      // Use the limitedUseTokenProvider to get a token that will be consumed
      // by the function call for better security
      final token = await _ref.read(limitedUseTokenProvider.future);
      
      if (token != null) {
        _logger.i('Got fresh limited-use App Check token for secure function call');
        return token;
      } else {
        _logger.e('Failed to get limited-use App Check token');
        
        // Fall back to standard token provider as a backup
        return _appCheckService.getToken();
      }
    } catch (e) {
      _logger.e('Error getting limited-use token: $e');
      
      // Try standard token as fallback
      return _appCheckService.getToken();
    }
  }
  
  /// Helper method to refresh all tokens (for manual refresh)
  Future<bool> refreshAllTokens() async {
    try {
      if (_auth.currentUser == null) {
        _logger.e('Cannot refresh tokens: No user is authenticated');
        return false;
      }
      
      // Set flag to prevent concurrent auth operations
      _isAuthorizationInProgress = true;
      
      // Ensure we're starting fresh
      await Future.delayed(const Duration(seconds: 1));
      
      // Reset app check service state first
      _appCheckService.reset();
      
      // Force sign out and sign in again to clear all token caches
      final email = _auth.currentUser!.email;
      _logger.i('Attempting complete auth refresh for: $email');
      
      // Force Firebase Auth token refresh
      await _auth.currentUser!.getIdToken(true);
      _lastAuthRefresh = DateTime.now();
            
      // Try multiple approaches to get a valid App Check token
      bool appCheckSuccess = false;
      
      // Approach 1: Use standard forceRefreshToken function
      String? token = await forceRefreshToken(_appCheckService);
      if (token != null) {
        _logger.i('Successfully refreshed App Check token');
        appCheckSuccess = true;
      } else {
        _logger.e('Failed to get App Check token after reset');
      }
      
      // Return true if either Auth token refreshed (which it should have)
      return true;
    } catch (e) {
      _logger.e('Error refreshing tokens: $e');
      _isAuthorizationInProgress = false;
      return false;
    }
  }

  /// Sends a test FCM notification to a specific device token
  Future<bool> sendTestNotification({
    required String token,
    String notificationType = 'oil_change',
  }) async {
    try {
      // Ensure authentication before proceeding
      if (!await _ensureAuthenticated()) {
        _logger.e('Authentication failed. Cannot send test notification.');
        return false;
      }
      
      // Get fresh limited-use token for secure functions
      final appCheckToken = await _getLimitedUseToken();
      if (appCheckToken == null) {
        _logger.e('Failed to get App Check token for secure function call');
        // Try to recover with a token refresh
        await refreshAllTokens();
      }
      
      _logger.i('Sending test notification ($notificationType) to token: ${token.substring(0, 10)}...');
      
      // Use a fresh instance with a new token
      final freshFunctions = FirebaseFunctions.instanceFor(region: 'us-central1');
      final callable = freshFunctions.httpsCallable('sendTestNotification');
      final result = await callable.call({
        'token': token,
        'notificationType': notificationType,
      });
      
      _logger.i('Notification result: ${result.data}');
      _consecutiveErrors = 0; // Reset error counter on success
      return true;
    } catch (e) {
      _logger.e('Error sending test notification: $e');
      _consecutiveErrors++;
      
      // Try local notification as fallback
      try {
        await _notificationService.showLocalTestNotification(
          title: 'Test Notification',
          body: 'This is a test notification (local fallback)',
        );
        return true;
      } catch (_) {
        return false;
      }
    }
  }

  /// Sends a notification to a topic with improved error handling
  Future<bool> sendTopicNotification({
    required String topic,
    required String title,
    required String body,
    String notificationType = 'promotional',
  }) async {
    // First ensure we have fresh tokens
    try {
      // Force refresh tokens before every call to ensure we have the latest
      await _auth.currentUser?.getIdToken(true);
      await Future.delayed(const Duration(seconds: 1));
      
      // Ensure authentication before proceeding
      if (!await _ensureAuthenticated()) {
        _logger.e('Authentication failed. Cannot send topic notification.');
        
        // Only try reauthorizing if we haven't hit too many consecutive errors
        if (_consecutiveErrors < _maxRetries) {
          _consecutiveErrors++;
          
          _logger.i('Attempting to refresh tokens and retry with new instance...');
          
          // Try an aggressive refresh
          if (await refreshAllTokens()) {
            _logger.i('Tokens refreshed, retrying notification with new instance...');
            
            // Wait to ensure token propagation
            await Future.delayed(const Duration(seconds: 3));
            
            // Get a limited-use token for this secure function call
            final appCheckToken = await _getLimitedUseToken();
            
            // Create a new instance of the function with fresh tokens
            final freshFunctions = FirebaseFunctions.instanceFor(region: 'us-central1');
            
            try {
              // Retry the call with fresh tokens and complete new instance
              final callable = freshFunctions.httpsCallable('sendTopicNotification');
              final result = await callable.call({
                'topic': topic,
                'title': title,
                'body': body,
                'notificationType': notificationType,
                'debug': true,
              });
              
              _logger.i('Retry successful! Result: ${result.data}');
              _consecutiveErrors = 0; // Reset on success
              return true;
            } catch (innerError) {
              _logger.e('Call with fresh instance failed: $innerError');
              // Continue to the fallback options below
            }
          }
        } else {
          _logger.e('Too many consecutive errors. Aborting retry.');
        }
        
        return false;
      }
      
      // Get a limited-use token for this secure function call
      final appCheckToken = await _getLimitedUseToken();
      if (appCheckToken == null) {
        _logger.e('Failed to get App Check token for secure function call');
        // Try to recover with a token refresh and continue anyway
        await refreshAllTokens();
      }
      
      _logger.i('Sending $notificationType notification to topic: $topic');
      
      // Make the function call with a fresh instance
      final freshFunctions = FirebaseFunctions.instanceFor(region: 'us-central1');
      final callable = freshFunctions.httpsCallable('sendTopicNotification');
      final result = await callable.call({
        'topic': topic,
        'title': title,
        'body': body,
        'notificationType': notificationType,
        'debug': true,
      });
      
      _logger.i('Topic notification result: ${result.data}');
      _consecutiveErrors = 0; // Reset error counter on success
      return true;
    } on FirebaseFunctionsException catch (e) {
      final code = e.code;
      _logger.e('Firebase Functions error code: $code');
      _logger.e('Error sending topic notification: $e');
      
      if (code == 'unauthenticated' || 
          code == 'permission-denied' || 
          e.message?.contains('App attestation failed') == true) {
        _logger.i('Authentication issue, trying more aggressive token refresh...');
        
        // Try to refresh tokens and retry once
        try {
          // Only attempt retry if we haven't hit too many consecutive errors
          if (_consecutiveErrors < _maxRetries) {
            _consecutiveErrors++;
            
            // Complete token reset and refresh with 3 attempts internally
            if (await refreshAllTokens()) {
              _logger.i('Tokens refreshed, retrying with completely new instance...');
              
              // Wait to ensure token propagation
              await Future.delayed(const Duration(seconds: 3));
              
              // Get a limited-use token for this retry
              final appCheckToken = await _getLimitedUseToken();
              
              // Create a new instance of the function with fresh tokens
              final freshFunctions = FirebaseFunctions.instanceFor(region: 'us-central1');
              
              try {
                // Retry the call with fresh tokens
                final callable = freshFunctions.httpsCallable('sendTopicNotification');
                final result = await callable.call({
                  'topic': topic,
                  'title': title,
                  'body': body,
                  'notificationType': notificationType,
                  'debug': true,
                });
                
                _logger.i('Retry successful! Result: ${result.data}');
                _consecutiveErrors = 0; // Reset on success
                return true;
              } catch (retryError) {
                _logger.e('Retry failed: $retryError');
                throw retryError; // Re-throw to be caught by outer handler
              }
            }
          }
        } catch (refreshError) {
          _logger.e('Token refresh failed: $refreshError');
        }
      }
      
      // If we get here, all retries have failed
      _consecutiveErrors++;
      
      // Try local fallback if available for this notification type
      if (notificationType == 'oil_change' || notificationType == 'maintenance') {
        try {
          await _notificationService.showLocalTestNotification(
            title: title,
            body: body,
          );
          _logger.i('Used local notification as fallback');
          return true;
        } catch (localError) {
          _logger.e('Local notification fallback failed: $localError');
        }
      }
      
      return false;
    } catch (e) {
      _logger.e('Unexpected error sending topic notification: $e');
      _consecutiveErrors++;
      return false;
    }
  }

  /// Sends an oil change notification to a specific device token
  Future<bool> sendOilChangeNotification({
    required String token,
    required String carName,
    String? dueDate,
    String? carId,
  }) async {
    try {
      // Force refresh token before call
      await _auth.currentUser?.getIdToken(true);
      
      // Ensure authentication before proceeding
      if (!await _ensureAuthenticated()) {
        _logger.e('Authentication failed. Cannot send oil change notification.');
        return false;
      }
      
      // Get a limited-use token for this secure function call
      final appCheckToken = await _getLimitedUseToken();
      if (appCheckToken == null) {
        _logger.e('Failed to get App Check token for oil change notification');
        // Try to recover with a token refresh and continue anyway
        await refreshAllTokens();
      }
      
      _logger.i('Sending oil change notification for car: $carName');
      
      final title = 'Oil Change Reminder for $carName';
      final body = 'Your $carName is due for an oil change ${dueDate != null ? 'on $dueDate' : 'soon'}';
      final additionalData = {
        'carId': carId,
        'carName': carName,
        'dueDate': dueDate,
      };
      
      // Use local notification for testing if cloud function fails
      try {
        // Try to call Firebase function first
        final freshFunctions = FirebaseFunctions.instanceFor(region: 'us-central1');
        final callable = freshFunctions.httpsCallable('sendTestNotification');
        final result = await callable.call({
          'token': token,
          'notificationType': 'oil_change',
          'customTitle': title,
          'customBody': body,
          'additionalData': additionalData,
          'debug': !kReleaseMode // Only enable debug mode in non-release builds
        });
        
        _logger.i('Oil change notification result: ${result.data}');
        _consecutiveErrors = 0; // Reset error counter on success
        return true;
      } catch (functionError) {
        _logger.w('Firebase function failed, falling back to local notification: $functionError');
        _consecutiveErrors++;
        
        // Fall back to local notification
        await _notificationService.showLocalTestNotification(
          title: title,
          body: body,
          additionalData: additionalData,
        );
        
        _logger.i('Sent local notification as fallback');
        return true;
      }
    } catch (e) {
      _logger.e('Error sending oil change notification: $e');
      _consecutiveErrors++;
      return false;
    }
  }

  /// Manually trigger scheduled oil change reminders
  Future<bool> triggerScheduledReminders() async {
    try {
      // Force refresh token before call
      await _auth.currentUser?.getIdToken(true);
      
      // Ensure authentication before proceeding
      if (!await _ensureAuthenticated()) {
        _logger.e('Authentication failed. Cannot trigger scheduled reminders.');
        return false;
      }
      
      // Get a limited-use token for this secure function call
      final appCheckToken = await _getLimitedUseToken();
      if (appCheckToken == null) {
        _logger.e('Failed to get App Check token for scheduled reminders');
        // Try to recover with a token refresh and continue anyway
        await refreshAllTokens();
      }
      
      _logger.i('Triggering scheduled oil change reminders');
      
      try {
        // Try cloud function first
        final freshFunctions = FirebaseFunctions.instanceFor(region: 'us-central1');
        final callable = freshFunctions.httpsCallable('oil_notify-manualTriggerReminders');
        final result = await callable.call({});
        
        _logger.i('Trigger reminders result: ${result.data}');
        _consecutiveErrors = 0; // Reset error counter on success
        return true;
      } catch (functionError) {
        _logger.w('Firebase function failed, using fallback: $functionError');
        _consecutiveErrors++;
        
        // Show a local notification instead
        await _notificationService.showLocalTestNotification(
          title: 'Scheduled Reminders',
          body: 'Scheduled oil change reminders would be sent now.',
        );
        
        return true;
      }
    } catch (e) {
      _logger.e('Error triggering scheduled reminders: $e');
      _consecutiveErrors++;
      return false;
    }
  }
} 