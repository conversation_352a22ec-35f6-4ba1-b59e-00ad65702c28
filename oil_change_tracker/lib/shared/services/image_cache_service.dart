import 'package:flutter/material.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'dart:developer' as dev;
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/foundation.dart';

/// Service for managing image caching across the app
class ImageCacheService {
  /// Clears all image caches in the app
  Future<void> clearAllCaches() async {
    try {
      // Clear Flutter's built-in image cache
      imageCache.clear();
      imageCache.clearLiveImages();

      // Clear the DefaultCacheManager cache (used by CachedNetworkImage)
      await DefaultCacheManager().emptyCache();

      // Clear cached image files
      final cacheManager = DefaultCacheManager();
      await cacheManager.emptyCache();

      // Clear CachedNetworkImage's internal cache
      CachedNetworkImage.evictFromCache('');

      dev.log('Cleared all image caches successfully');
    } catch (e) {
      dev.log('Error clearing image caches: $e');
    }
  }

  /// Public-facing method for UI to clear cache and get feedback
  Future<bool> clearCache() async {
    try {
      await clearAllCaches();
      dev.log('Cache cleared successfully via settings');
      return true;
    } catch (e) {
      dev.log('Error clearing cache via settings: $e');
      return false;
    }
  }

  /// Clears cache for a specific image URL
  Future<void> clearCacheForUrl(String url) async {
    try {
      if (url.isEmpty) return;

      // Clear from default cache manager
      await DefaultCacheManager().removeFile(url);

      // Clear from CachedNetworkImage
      await CachedNetworkImage.evictFromCache(url);

      dev.log('Cleared cache for image URL: $url');
    } catch (e) {
      dev.log('Error clearing cache for URL: $e');
    }
  }

  /// Validates a URL and ensures it's a proper HTTP/HTTPS URL
  String? validateAndFixUrl(String? url) {
    if (url == null || url.isEmpty) {
      return null;
    }

    // Allow HTTPS always. Allow HTTP only in debug/profile builds.
    if (url.startsWith('https://')) {
      return url;
    }
    if (url.startsWith('http://')) {
      if (kReleaseMode) {
        dev.log('Blocked insecure HTTP image URL in release build: $url');
        return null;
      }
      // In debug/profile we still allow http for local testing
      return url;
    }

    // If it's a gs:// URL, log it and return null
    if (url.startsWith('gs://')) {
      dev.log('Invalid gs:// URL format detected: $url');
      dev.log('Skipping invalid image URL: $url');

      // Extract path components to potentially form a Firebase Storage HTTP URL
      try {
        // Format: gs://bucket-name/path/to/object
        final parts = url.substring(5).split('/');
        if (parts.length >= 2) {
          final bucket = parts[0];
          final path = parts.sublist(1).join('/');

          // Form a Firebase Storage HTTP URL
          final httpUrl =
              'https://firebasestorage.googleapis.com/v0/b/$bucket/o/${Uri.encodeComponent(path)}?alt=media';
          dev.log('Converted gs:// URL to HTTP URL: $httpUrl');
          return httpUrl;
        }
      } catch (e) {
        dev.log('Error converting gs:// URL to HTTP: $e');
      }

      // If conversion failed, return null
      return null;
    }

    // For file:// URLs, just return as is
    if (url.startsWith('file://')) {
      return url;
    }

    // If it doesn't have a protocol, try adding https://
    if (!url.contains('://')) {
      dev.log('URL missing protocol, attempting to add https://: $url');
      return 'https://$url';
    }

    // If we get here, it's some other unsupported protocol
    dev.log('Unsupported URL protocol: $url');
    return null;
  }

  /// Generates a cache-busting URL for an image
  String generateCacheBustingUrl(String? originalUrl) {
    // First validate and fix the URL
    final validUrl = validateAndFixUrl(originalUrl);
    if (validUrl == null) {
      return ''; // Return empty string for invalid URLs
    }

    // Add a timestamp query parameter to force refresh
    final timestamp = DateTime.now().millisecondsSinceEpoch;

    // Handle local file URLs differently - don't modify them
    if (validUrl.startsWith('file://')) {
      return validUrl;
    }

    // Add cache-busting parameter to URL
    return validUrl.contains('?')
        ? '$validUrl&t=$timestamp'
        : '$validUrl?t=$timestamp';
  }

  /// Creates a stable cache key for an image based on user ID and image URL
  /// This will remain consistent across rebuilds unless the URL or user ID changes
  String createStableImageKey(String userId, String? imageUrl) {
    if (imageUrl == null || imageUrl.isEmpty) {
      return 'profile_$userId';
    }

    // Use a stable hash for consistent caching
    return 'profile_${userId}_${imageUrl.hashCode}';
  }

  /// Remove a specific file from the cache
  Future<void> removeFileFromCache(String url) async {
    try {
      await DefaultCacheManager().removeFile(url);
      dev.log('Removed file from cache: $url');
    } catch (e) {
      dev.log('Error removing file from cache: $e');
    }
  }

  /// Unified profile image builder method for consistent image display across the app
  Widget buildProfileImage({
    required String userId,
    required String? imageUrl,
    required double size,
    Color? backgroundColor,
    Color? foregroundColor,
    FirebaseFirestore? firestore,
    bool forceRefresh = false,
  }) {
    // If user ID is null or empty, return default avatar image
    if (userId.isEmpty) {
      return _buildDefaultAvatar(size, backgroundColor, foregroundColor, null);
    }

    // If no image URL is provided or it's empty, go straight to Firestore/default avatar
    if (imageUrl == null || imageUrl.isEmpty) {
      // First try to get user data from Firestore as fallback
      final firestoreInstance = firestore ?? FirebaseFirestore.instance;

      return FutureBuilder<DocumentSnapshot>(
        future: firestoreInstance.collection('users').doc(userId).get(),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return _buildLoadingIndicator(size);
          }

          if (snapshot.hasData &&
              snapshot.data != null &&
              snapshot.data!.exists) {
            final userData = snapshot.data!.data() as Map<String, dynamic>?;
            String? userImageUrl = userData?['profileImageUrl'] as String?;

            // If we found a valid URL in Firestore, use it
            if (userImageUrl != null && userImageUrl.isNotEmpty) {
              // Validate the URL from Firestore
              String? validUserImageUrl = validateAndFixUrl(userImageUrl);

              if (validUserImageUrl != null) {
                String cacheKey =
                    'profile_${userId}_${validUserImageUrl.hashCode}';

                if (forceRefresh) {
                  // If force refresh is requested, add timestamp to cache key
                  cacheKey =
                      "${cacheKey}_${DateTime.now().millisecondsSinceEpoch}";
                  validUserImageUrl =
                      generateCacheBustingUrl(validUserImageUrl);
                }

                return RepaintBoundary(
                  child: CachedNetworkImage(
                    key: ValueKey(cacheKey),
                    imageUrl: validUserImageUrl,
                    imageBuilder: (context, imageProvider) =>
                        _buildCircleAvatar(
                      imageProvider: imageProvider,
                      size: size,
                    ),
                    placeholder: (context, url) => _buildLoadingIndicator(size),
                    errorWidget: (context, url, error) {
                      dev.log(
                          'Error loading profile image from Firestore: $error, URL: $url',
                          level: 1);
                      return _buildDefaultAvatar(
                          size, backgroundColor, foregroundColor, userId);
                    },
                    fadeInDuration: const Duration(milliseconds: 200),
                    fadeOutDuration: Duration.zero,
                    memCacheWidth: size.toInt() * 2,
                    memCacheHeight: size.toInt() * 2,
                    cacheKey: cacheKey,
                    useOldImageOnUrlChange: !forceRefresh,
                    maxWidthDiskCache: 500,
                    maxHeightDiskCache: 500,
                  ),
                );
              }
            }
          }

          // If we couldn't get a valid image URL, show default avatar
          return _buildDefaultAvatar(
              size, backgroundColor, foregroundColor, userId);
        },
      );
    }

    // Validate and fix the image URL if provided
    String? validImageUrl = validateAndFixUrl(imageUrl);

    if (validImageUrl != null) {
      // Use a stable key that only changes when the URL content changes
      // not on every rebuild or card scroll
      String stableKey = createStableImageKey(userId, validImageUrl);

      // Only create a dynamic key if forceRefresh is true
      String cacheKey = forceRefresh
          ? "${stableKey}_${DateTime.now().millisecondsSinceEpoch}"
          : stableKey;

      // Add cache busting to URL only if force refresh is requested
      final finalImageUrl =
          forceRefresh ? generateCacheBustingUrl(validImageUrl) : validImageUrl;

      // Use RepaintBoundary to isolate the image from parent widget rebuilds
      return RepaintBoundary(
        child: CachedNetworkImage(
          key: ValueKey(cacheKey), // Use ValueKey for more stable identity
          imageUrl: finalImageUrl,
          imageBuilder: (context, imageProvider) => _buildCircleAvatar(
            imageProvider: imageProvider,
            size: size,
          ),
          placeholder: (context, url) => _buildLoadingIndicator(size),
          errorWidget: (context, url, error) {
            dev.log('Error loading profile image: $error, URL: $url', level: 1);
            // If we fail to load the image, try from Firestore as fallback
            return _buildDefaultAvatar(
                size, backgroundColor, foregroundColor, userId);
          },
          fadeInDuration: const Duration(milliseconds: 200),
          fadeOutDuration: Duration.zero,
          // Optimize memory cache dimensions
          memCacheWidth: size.toInt() * 2,
          memCacheHeight: size.toInt() * 2,
          // Set an explicit cache key to control when image is refreshed
          cacheKey: cacheKey,
          // Use old image except when forceRefresh is true
          useOldImageOnUrlChange: !forceRefresh,
          // Use a longer cache duration
          maxWidthDiskCache: 500,
          maxHeightDiskCache: 500,
        ),
      );
    }

    // If the URL validation failed, return default avatar
    return _buildDefaultAvatar(size, backgroundColor, foregroundColor, userId);
  }

  /// Builds a default avatar with user initials or a person icon
  Widget _buildDefaultAvatar(double size, Color? backgroundColor,
      Color? foregroundColor, String? userId) {
    // Use material theme colors as defaults since we don't have BuildContext here
    final defaultBackgroundColor = Colors.red.shade700; // Similar to burgundy
    final defaultForegroundColor = Colors.amber; // Similar to gold

    if (userId != null && userId.isNotEmpty) {
      // Use first letter of user ID if we have it
      String initial = userId.substring(0, 1).toUpperCase();
      return CircleAvatar(
        radius: size / 2,
        backgroundColor: backgroundColor ?? defaultBackgroundColor,
        child: Text(
          initial,
          style: TextStyle(
            color: foregroundColor ?? defaultForegroundColor,
            fontSize: size * 0.3,
            fontWeight: FontWeight.bold,
          ),
        ),
      );
    }

    // Use person icon as fallback
    return CircleAvatar(
      radius: size / 2,
      backgroundColor: backgroundColor ?? defaultBackgroundColor,
      child: Icon(Icons.person,
          color: foregroundColor ?? defaultForegroundColor, size: size * 0.3),
    );
  }

  Widget _buildCircleAvatar({
    required ImageProvider imageProvider,
    required double size,
  }) {
    return CircleAvatar(
      radius: size / 2,
      backgroundImage: imageProvider,
    );
  }

  Widget _buildLoadingIndicator(double size) {
    return SizedBox(
      width: size,
      height: size,
      child: Center(
        child: CircularProgressIndicator(
          valueColor:
              AlwaysStoppedAnimation<Color>(Colors.amber), // Similar to gold
          strokeWidth: 2,
        ),
      ),
    );
  }

  /// Force clears the cache for a specific user's profile image
  Future<void> forceRefreshProfileImage(String userId, String? imageUrl) async {
    dev.log(
        'Force refreshing profile image for user $userId with URL: $imageUrl');

    try {
      // Create a stable key for this user that we'll use for cache identification
      // Important: Still create a key even if imageUrl is null
      String stableKey = createStableImageKey(userId, imageUrl);
      dev.log('Stable cache key: $stableKey');

      // Step 1: First, aggressively clear Flutter's built-in image cache
      dev.log('Clearing Flutter imageCache');
      imageCache.clear();
      imageCache.clearLiveImages();

      // Step 2: Try to clear CachedNetworkImage's internal cache
      // Even if imageUrl is null, try to evict with empty key to clear everything
      dev.log('Evicting from CachedNetworkImage cache');
      await CachedNetworkImage.evictFromCache('');

      // Step 3: If we have a valid URL, also clear that specific URL
      String? validUrl = imageUrl != null ? validateAndFixUrl(imageUrl) : null;
      if (validUrl != null &&
          (validUrl.startsWith('http://') || validUrl.startsWith('https://'))) {
        dev.log('Clearing valid URL from cache: $validUrl');

        // Clear the specific URL from CachedNetworkImage
        await CachedNetworkImage.evictFromCache(validUrl);

        // Clear from DefaultCacheManager
        try {
          dev.log('Removing from DefaultCacheManager');
          await DefaultCacheManager().removeFile(validUrl);

          // Clear any cache-busting variations that might exist
          final timestamp =
              DateTime.now().millisecondsSinceEpoch - 60000; // 1 minute ago
          final cacheBustUrl1 = validUrl.contains('?')
              ? '$validUrl&t=$timestamp'
              : '$validUrl?t=$timestamp';

          final cacheBustUrl2 = validUrl.contains('?')
              ? '$validUrl&_t=${DateTime.now().millisecondsSinceEpoch}'
              : '$validUrl?_t=${DateTime.now().millisecondsSinceEpoch}';

          dev.log('Removing cache-busting URL variations');
          await DefaultCacheManager().removeFile(cacheBustUrl1);
          await DefaultCacheManager().removeFile(cacheBustUrl2);
        } catch (e) {
          dev.log(
              'Non-critical error removing URL from DefaultCacheManager: $e');
        }
      } else if (validUrl != null && validUrl.startsWith('file://')) {
        // For local files, log but don't try to clear cache
        dev.log(
            'Profile image is a local file, no cache clearing needed: $validUrl');
      } else {
        // For null or invalid URLs, we already cleared the general cache above
        dev.log(
            'No valid image URL provided, using general cache clearing only');
      }

      // Step 4: Try to clear the entire DefaultCacheManager cache as a fallback
      try {
        dev.log('Purging all cache from DefaultCacheManager');
        final cacheManager = DefaultCacheManager();
        await cacheManager.emptyCache(); // More aggressive approach
      } catch (e) {
        dev.log('Error clearing cache manager: $e');
      }

      dev.log('Successfully refreshed profile image cache for user $userId');
    } catch (e) {
      dev.log('Error refreshing profile image cache: $e', error: e);
      // Don't rethrow - profile image cache clearing should never crash the app
    }
  }
}

/// Provider for the ImageCacheService
final imageCacheServiceProvider = Provider<ImageCacheService>((ref) {
  return ImageCacheService();
});
