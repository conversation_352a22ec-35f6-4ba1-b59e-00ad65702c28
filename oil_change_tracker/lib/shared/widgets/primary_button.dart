import 'package:flutter/material.dart';
import '../../core/theme/theme_extensions.dart';

/// A primary button widget with consistent styling across the app
class PrimaryButton extends StatelessWidget {
  /// The text to display on the button
  final String text;
  
  /// The action to perform when the button is pressed
  final VoidCallback onPressed;
  
  /// Optional icon to display before the text
  final IconData? icon;
  
  /// Whether to make the button take up the full width
  final bool fullWidth;
  
  /// Whether the button is in a loading state
  final bool isLoading;
  
  /// Creates a primary button with consistent styling
  const PrimaryButton({
    super.key,
    required this.text,
    required this.onPressed,
    this.icon,
    this.fullWidth = true,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    final buttonContent = Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        if (isLoading)
          SizedBox(
            width: 20,
            height: 20,
            child: CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              strokeWidth: 2,
            ),
          )
        else if (icon != null) ...[
          Icon(icon, color: Colors.white),
          const SizedBox(width: 8),
        ],
        Text(
          text,
          style: const TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
    
    return SizedBox(
      width: fullWidth ? double.infinity : null,
      child: ElevatedButton(
        onPressed: isLoading ? null : onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: context.secondaryAccentColor,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          minimumSize: const Size(0, 50),
        ),
        child: buttonContent,
      ),
    );
  }
} 