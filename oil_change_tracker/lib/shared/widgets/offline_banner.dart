import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import '../../core/services/connectivity_service.dart';
import '../../core/theme/theme_extensions.dart';
import '../../generated/app_localizations.dart';
import 'dart:developer' as dev;

/// A banner that displays when the app is offline
class OfflineBanner extends ConsumerStatefulWidget {
  const OfflineBanner({super.key});

  @override
  ConsumerState<OfflineBanner> createState() => _OfflineBannerState();
}

class _OfflineBannerState extends ConsumerState<OfflineBanner> with SingleTickerProviderStateMixin {
  late final AnimationController _controller;
  late final Animation<double> _animation;
  bool _dismissed = false;
  bool _wasOffline = false;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
    _animation = CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final connectivityStatus = ref.watch(connectivityStatusProvider);
    final l10n = S.of(context);
    
    return connectivityStatus.when(
      data: (status) {
        final bool isOffline = status == ConnectivityResult.none;
        
        // Auto-reset dismissal when we reconnect after being offline
        if (_wasOffline && !isOffline && _dismissed) {
          _dismissed = false;
          dev.log('Network reconnected - resetting banner dismiss state');
        }
        
        // Track offline state for reconnection detection
        _wasOffline = isOffline;
        
        if (!isOffline || _dismissed) {
          // Hide banner when connected or dismissed
          _controller.reverse();
          return AnimatedBuilder(
            animation: _animation,
            builder: (context, child) => SizeTransition(
              sizeFactor: _animation,
              axisAlignment: -1.0,
              child: const SizedBox(),
            ),
          );
        }
        
        // Show banner when offline
        _controller.forward();
        return AnimatedBuilder(
          animation: _animation,
          builder: (context, child) => SizeTransition(
            sizeFactor: _animation,
            axisAlignment: -1.0,
            child: Material(
              elevation: 4,
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
                color: context.isDarkMode ? context.secondaryAccentColor : context.accentColor,
                child: SafeArea(
                  bottom: false,
                  child: Row(
                    children: [
                      const Icon(
                        Icons.wifi_off,
                        color: Colors.white,
                        size: 18,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          l10n.offlineMode,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      InkWell(
                        onTap: () {
                          setState(() {
                            _dismissed = true;
                          });
                          _controller.reverse();
                        },
                        child: const Padding(
                          padding: EdgeInsets.all(4.0),
                          child: Icon(
                            Icons.close,
                            color: Colors.white70,
                            size: 18,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
      loading: () => const SizedBox.shrink(),
      error: (_, __) => const SizedBox.shrink(),
    );
  }
} 