import 'package:flutter/material.dart';
import '../../core/theme/theme_extensions.dart';

class LoadingOverlay extends StatelessWidget {
  final bool isLoading;
  final Widget child;
  final String? message;

  const LoadingOverlay({
    super.key,
    required this.isLoading,
    required this.child,
    this.message,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        child,
        if (isLoading)
          AnimatedOpacity(
            opacity: isLoading ? 1.0 : 0.0,
            duration: const Duration(milliseconds: 200),
            child: Container(
              color: context.isDarkMode
                  ? Colors.black.withOpacity(0.4) // Less opaque for dark mode
                  : Colors.black.withOpacity(0.25), // Even less opaque for light mode
              child: Center(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation<Color>(context.accentColor),
                    ),
                    if (message != null) ...[
                      const SizedBox(height: 16),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                        decoration: BoxDecoration(
                          color: context.containerBackgroundColor.withOpacity(0.7),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          message!,
                          style: TextStyle(
                            color: context.primaryTextColor,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ),
          ),
      ],
    );
  }
}

class LoadingOverlayController {
  final BuildContext context;
  bool _isVisible = false;
  OverlayEntry? _overlayEntry;

  LoadingOverlayController(this.context);

  void show({String? message}) {
    if (_isVisible) return;

    _overlayEntry = OverlayEntry(
      builder: (context) => LoadingOverlay(
        isLoading: true,
        message: message,
        child: Container(),
      ),
    );

    Overlay.of(context).insert(_overlayEntry!);
    _isVisible = true;
  }

  void hide() {
    if (!_isVisible) return;

    _overlayEntry?.remove();
    _overlayEntry = null;
    _isVisible = false;
  }

  bool get isVisible => _isVisible;
}