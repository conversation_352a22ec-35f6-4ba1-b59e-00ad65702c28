import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/providers/theme_provider.dart';
import '../../core/theme/theme_extensions.dart';
import '../../generated/app_localizations.dart';

/// A widget that toggles between light and dark theme modes
class ThemeToggle extends ConsumerWidget {
  const ThemeToggle({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final themeMode = ref.watch(themeModeProvider);
    final isDarkMode = themeMode == ThemeMode.dark;
    final s = S.of(context);

    return ListTile(
      leading: Icon(
        isDarkMode ? Icons.dark_mode : Icons.light_mode,
        color: context.accentColor,
      ),
      title: Text(
        isDarkMode ? s.darkTheme : s.lightTheme,
        style: Theme.of(context).textTheme.titleMedium,
      ),
      trailing: Switch(
        value: isDarkMode,
        activeColor: context.accentColor,
        onChanged: (value) {
          ref.read(themeModeProvider.notifier).setThemeMode(
                value ? AppThemeMode.dark : AppThemeMode.light,
              );
        },
      ),
    );
  }
} 