import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/theme/theme_extensions.dart';
import '../../core/services/connectivity_service.dart';
import '../../generated/app_localizations.dart';

/// A text widget that displays an error message for offline operations
class OfflineErrorText extends ConsumerWidget {
  final String errorMessage;
  final VoidCallback? onRetry;

  const OfflineErrorText({
    super.key, 
    required this.errorMessage, 
    this.onRetry,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isConnected = ref.watch(isConnectedProvider);
    final l10n = S.of(context);
    
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        SelectableText.rich(
          TextSpan(
            children: [
              WidgetSpan(
                alignment: PlaceholderAlignment.middle,
                child: Icon(
                  Icons.error_outline,
                  color: context.secondaryAccentColor,
                  size: 20,
                ),
              ),
              const TextSpan(text: ' '),
              TextSpan(
                text: errorMessage,
                style: TextStyle(
                  color: context.secondaryAccentColor,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
        if (!isConnected && onRetry != null) ...[
          const SizedBox(height: 12),
          OutlinedButton.icon(
            onPressed: onRetry,
            icon: const Icon(Icons.refresh, size: 16),
            label: Text(l10n.retry),
            style: OutlinedButton.styleFrom(
              foregroundColor: context.accentColor,
              side: BorderSide(color: context.accentColor),
            ),
          ),
        ],
      ],
    );
  }
} 