import 'package:flutter/material.dart';
import '../../core/theme/theme_extensions.dart';

class CustomDropdown<T> extends StatelessWidget {
  final String labelText;
  final List<T> items;
  final T? value;
  final void Function(T?)? onChanged;
  final String Function(T) itemToString;
  final String? Function(T?)? validator;
  final Color? fillColor;
  final Widget? prefixIcon;

  const CustomDropdown({
    super.key,
    required this.labelText,
    required this.items,
    required this.itemToString,
    this.value,
    this.onChanged,
    this.validator,
    this.fillColor,
    this.prefixIcon,
  });

  @override
  Widget build(BuildContext context) {
    return DropdownButtonFormField<T>(
      value: value,
      decoration: InputDecoration(
        labelText: labelText,
        labelStyle: TextStyle(color: context.accentColor),
        prefixIcon: prefixIcon,
        filled: fillColor != null,
        fillColor: fillColor,
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: context.accentColor),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: context.accentColor),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: context.secondaryAccentColor, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: context.secondaryAccentColor),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: context.secondaryAccentColor, width: 2),
        ),
      ),
      dropdownColor: context.containerBackgroundColor,
      style: TextStyle(color: context.primaryTextColor),
      icon: Icon(Icons.arrow_drop_down, color: context.accentColor),
      validator: validator,
      items: items.map<DropdownMenuItem<T>>((T item) {
        return DropdownMenuItem<T>(
          value: item,
          child: Text(
            itemToString(item),
            style: TextStyle(color: context.primaryTextColor),
          ),
        );
      }).toList(),
      onChanged: onChanged,
    );
  }
} 