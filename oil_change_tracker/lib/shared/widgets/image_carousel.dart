import 'dart:io';
import 'dart:async';
import 'package:flutter/material.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../../core/theme/theme_extensions.dart';
import '../services/image_cache_service.dart';
import 'dart:developer' as dev;
import '../../generated/app_localizations.dart';
import 'package:flutter/foundation.dart';

class ImageCarousel extends StatefulWidget {
  final List<String>? networkImages;
  final List<File>? localImages;
  final double height;
  final Function()? onAddImageTap;
  final Function(int)? onDeleteImageTap;
  final Function(int)? onPageChanged;
  final bool enableDelete;
  final bool enableAdd;
  final bool showIndicator;
  final bool autoPlay;
  final Duration autoPlayInterval;
  final BoxFit imageFit;
  final BorderRadius? borderRadius;

  const ImageCarousel({
    super.key,
    this.networkImages,
    this.localImages,
    this.height = 250,
    this.onAddImageTap,
    this.onDeleteImageTap,
    this.onPageChanged,
    this.enableDelete = true,
    this.enableAdd = true,
    this.showIndicator = true,
    this.autoPlay = true,
    this.autoPlayInterval = const Duration(seconds: 3),
    this.imageFit = BoxFit.cover,
    this.borderRadius,
  });

  @override
  State<ImageCarousel> createState() => _ImageCarouselState();
}

class _ImageCarouselState extends State<ImageCarousel>
    with SingleTickerProviderStateMixin {
  late PageController _pageController;
  int _currentPage = 0;
  bool _showControls = false;
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  Timer? _autoPlayTimer;
  final ImageCacheService _imageCacheService = ImageCacheService();

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 2000),
    )..repeat(reverse: true);

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.03,
    ).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeInOut,
      ),
    );

    // Setup auto-play if enabled and we have multiple images
    _setupAutoPlay();
  }

  void _setupAutoPlay() {
    if (widget.autoPlay && _hasMultipleImages) {
      _autoPlayTimer?.cancel();
      _autoPlayTimer = Timer.periodic(widget.autoPlayInterval, (timer) {
        if (_pageController.hasClients && _totalItems > 1) {
          if (_currentPage < _totalPages - 1) {
            _pageController.nextPage(
              duration: const Duration(milliseconds: 500),
              curve: Curves.easeInOut,
            );
          } else {
            _pageController.animateToPage(
              0,
              duration: const Duration(milliseconds: 500),
              curve: Curves.easeInOut,
            );
          }
        }
      });
    }
  }

  @override
  void didUpdateWidget(ImageCarousel oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Check if autoPlay settings changed or images changed
    if (widget.autoPlay != oldWidget.autoPlay ||
        widget.autoPlayInterval != oldWidget.autoPlayInterval ||
        widget.networkImages != oldWidget.networkImages ||
        widget.localImages != oldWidget.localImages) {
      _setupAutoPlay();
    }
  }

  @override
  void dispose() {
    _pageController.dispose();
    _animationController.dispose();
    _autoPlayTimer?.cancel();
    super.dispose();
  }

  /// Validates network image URL
  bool _isValidImageUrl(String? url) {
    if (url == null || url.isEmpty) {
      return false;
    }

    // HTTPS always allowed. HTTP only in non-release builds.
    if (url.startsWith('https://')) return true;
    if (url.startsWith('http://')) return !kReleaseMode;
    return false;
  }

  /// Gets a list of valid network images
  List<String> get _validNetworkImages {
    if (widget.networkImages == null) return [];

    final validImages = widget.networkImages!.where((url) {
      final isValid = _isValidImageUrl(url);
      if (!isValid) {
        dev.log('Invalid image URL skipped: $url');
      }
      return isValid;
    }).toList();

    return validImages;
  }

  /// Returns the total number of valid items in the carousel
  int get _totalItems {
    int count = 0;
    if (widget.networkImages != null) count += _validNetworkImages.length;
    if (widget.localImages != null) count += widget.localImages!.length;
    if (widget.enableAdd && widget.onAddImageTap != null) count += 1;
    return count;
  }

  int get _totalPages {
    int count = 0;
    if (widget.networkImages != null) count += _validNetworkImages.length;
    if (widget.localImages != null) count += widget.localImages!.length;
    return count;
  }

  bool get _hasImages {
    return (_validNetworkImages.isNotEmpty) ||
        (widget.localImages != null && widget.localImages!.isNotEmpty);
  }

  bool get _hasMultipleImages {
    int imageCount = 0;
    imageCount += _validNetworkImages.length;
    if (widget.localImages != null) imageCount += widget.localImages!.length;
    return imageCount > 1;
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        MouseRegion(
          onEnter: (_) {
            WidgetsBinding.instance.addPostFrameCallback((_) {
              if (mounted) {
                setState(() => _showControls = true);
              }
            });
          },
          onExit: (_) {
            WidgetsBinding.instance.addPostFrameCallback((_) {
              if (mounted) {
                setState(() => _showControls = false);
              }
            });
          },
          child: GestureDetector(
            onTap: () {
              WidgetsBinding.instance.addPostFrameCallback((_) {
                if (mounted) {
                  setState(() => _showControls = !_showControls);
                }
              });
            },
            child: Container(
              height: widget.height,
              decoration: BoxDecoration(
                color: context.containerBackgroundColor,
                borderRadius: widget.borderRadius ?? BorderRadius.circular(16),
                border: Border.all(
                  color: _hasImages
                      ? context.secondaryAccentColor.withOpacity(0.5)
                      : context.accentColor.withOpacity(0.5),
                  width: _hasImages ? 1.5 : 1.0,
                ),
                boxShadow: [
                  BoxShadow(
                    color: _hasImages
                        ? context.secondaryAccentColor.withOpacity(0.2)
                        : context.accentColor.withOpacity(0.2),
                    blurRadius: 10,
                    offset: const Offset(0, 5),
                  ),
                ],
              ),
              child: ClipRRect(
                borderRadius: widget.borderRadius ?? BorderRadius.circular(15),
                child: PageView.builder(
                  controller: _pageController,
                  itemCount: _totalItems,
                  onPageChanged: (index) {
                    WidgetsBinding.instance.addPostFrameCallback((_) {
                      if (mounted) {
                        setState(() => _currentPage = index);
                        if (widget.onPageChanged != null) {
                          widget.onPageChanged!(index);
                        }
                      }
                    });
                  },
                  itemBuilder: (context, index) {
                    // Network images come first
                    if (widget.networkImages != null) {
                      final validImages = _validNetworkImages;
                      if (index < validImages.length) {
                        return _buildNetworkImage(validImages[index], index);
                      }
                    }

                    // Local images are next
                    if (widget.localImages != null) {
                      final localIndex = index - (_validNetworkImages.length);
                      if (localIndex < widget.localImages!.length) {
                        return _buildLocalImage(
                            widget.localImages![localIndex], index);
                      }
                    }

                    // Add image placeholder is last
                    if (widget.onAddImageTap != null && widget.enableAdd) {
                      return _buildAddImagePlaceholder();
                    }

                    // Fallback - should not reach here
                    return Container(
                      color: context.containerBackgroundColor,
                      child: Center(
                        child: Icon(
                          Icons.image_not_supported,
                          color: context.accentColor,
                          size: 48,
                        ),
                      ),
                    );
                  },
                ),
              ),
            ),
          ),
        ),

        // Navigation Arrows (show on hover or tap)
        if (_showControls && _hasImages)
          Positioned.fill(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // Left arrow
                if (_currentPage > 0)
                  Padding(
                    padding: const EdgeInsets.only(left: 8.0),
                    child: Container(
                      decoration: const BoxDecoration(
                        color: Colors.black54,
                        shape: BoxShape.circle,
                      ),
                      child: IconButton(
                        icon: const Icon(Icons.arrow_back_ios,
                            color: Colors.white),
                        onPressed: () {
                          _pageController.previousPage(
                            duration: const Duration(milliseconds: 300),
                            curve: Curves.easeInOut,
                          );
                        },
                      ),
                    ),
                  ),

                // Right arrow
                if (_currentPage < _totalItems - 1)
                  Padding(
                    padding: const EdgeInsets.only(right: 8.0),
                    child: Container(
                      decoration: const BoxDecoration(
                        color: Colors.black54,
                        shape: BoxShape.circle,
                      ),
                      child: IconButton(
                        icon: const Icon(Icons.arrow_forward_ios,
                            color: Colors.white),
                        onPressed: () {
                          _pageController.nextPage(
                            duration: const Duration(milliseconds: 300),
                            curve: Curves.easeInOut,
                          );
                        },
                      ),
                    ),
                  ),
              ],
            ),
          ),

        // Delete button (top right)
        if (_showControls &&
            widget.enableDelete &&
            widget.onDeleteImageTap != null &&
            _hasImages &&
            _currentPage <
                (widget.networkImages?.length ?? 0) +
                    (widget.localImages?.length ?? 0))
          Positioned(
            top: 8,
            right: 8,
            child: Container(
              decoration: const BoxDecoration(
                color: Colors.black54,
                shape: BoxShape.circle,
              ),
              child: IconButton(
                icon: const Icon(Icons.delete, color: Colors.red),
                onPressed: () {
                  if (widget.onDeleteImageTap != null) {
                    widget.onDeleteImageTap!(_currentPage);
                  }
                },
              ),
            ),
          ),

        // Page indicator
        if (widget.showIndicator && _totalItems > 1)
          Positioned(
            bottom: 16,
            left: 0,
            right: 0,
            child: Center(
              child: SmoothPageIndicator(
                controller: _pageController,
                count: _totalItems,
                effect: WormEffect(
                  activeDotColor: context.secondaryAccentColor,
                  dotColor: Colors.white54,
                  dotHeight: 8,
                  dotWidth: 8,
                  type: WormType.thin,
                ),
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildNetworkImage(String imageUrl, int index) {
    // Ensure the URL has been validated and is proper HTTP/HTTPS
    String? validUrl = _imageCacheService.validateAndFixUrl(imageUrl);
    if (validUrl == null) {
      // Show placeholder for invalid URL
      return Container(
        color: context.containerBackgroundColor,
        child: Center(
          child: Icon(
            Icons.broken_image,
            color: context.secondaryAccentColor,
            size: 48,
          ),
        ),
      );
    }

    // Create a static cache key instead of using cache busting with timestamps
    // This will allow images to be cached properly and not reload with each slide
    final cacheKey = 'carousel_$imageUrl';

    return Stack(
      fit: StackFit.expand,
      children: [
        AnimatedBuilder(
          animation: _animationController,
          builder: (context, child) {
            return Transform.scale(
              scale: _scaleAnimation.value,
              child: child,
            );
          },
          child: CachedNetworkImage(
            imageUrl: validUrl,
            fit: widget.imageFit,
            cacheKey: cacheKey,
            // Use relatively long cached life to avoid reloading
            maxHeightDiskCache: 1200,
            maxWidthDiskCache: 1200,
            fadeInDuration: const Duration(milliseconds: 300),
            fadeOutDuration: Duration.zero,
            // Don't use old image when URL changes to fix cache issues
            useOldImageOnUrlChange: false,
            placeholder: (context, url) => Container(
              color: context.containerBackgroundColor,
              child: Center(
                child: CircularProgressIndicator(
                  valueColor:
                      AlwaysStoppedAnimation<Color>(context.accentColor),
                ),
              ),
            ),
            errorWidget: (context, url, error) {
              dev.log('Error loading image: $error, URL: $url', level: 1);
              return Container(
                color: context.containerBackgroundColor,
                child: Center(
                  child: Icon(
                    Icons.error,
                    color: context.secondaryAccentColor,
                    size: 48,
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildLocalImage(File imageFile, int index) {
    return Stack(
      fit: StackFit.expand,
      children: [
        AnimatedBuilder(
          animation: _animationController,
          builder: (context, child) {
            return Transform.scale(
              scale: _scaleAnimation.value,
              child: child,
            );
          },
          child: Image.file(
            imageFile,
            fit: widget.imageFit,
          ),
        ),
      ],
    );
  }

  Widget _buildAddImagePlaceholder() {
    return GestureDetector(
      onTap: widget.onAddImageTap,
      child: Container(
        color: context.containerBackgroundColor,
        child: AnimatedBuilder(
          animation: _animationController,
          builder: (context, child) {
            return Transform.scale(
              scale: 1.0 +
                  (_scaleAnimation.value - 1.0) * 0.3, // Subtler animation
              child: child,
            );
          },
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: context.accentColor.withOpacity(0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.add_photo_alternate,
                  color: context.accentColor,
                  size: 48,
                ),
              ),
              const SizedBox(height: 16),
              Text(
                S.of(context).addAnotherPhoto,
                style: TextStyle(
                  color: context.primaryTextColor,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: Text(
                  S.of(context).showCarFromDifferentAngles,
                  style: TextStyle(
                    color: context.secondaryTextColor,
                    fontSize: 14,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
