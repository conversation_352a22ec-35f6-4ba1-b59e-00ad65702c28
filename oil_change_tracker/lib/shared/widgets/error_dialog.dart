import 'package:flutter/material.dart';
import '../../generated/app_localizations.dart';
import '../../core/theme/theme_extensions.dart';

class ErrorDialog extends StatelessWidget {
  final String message;
  final VoidCallback? onRetry;

  const ErrorDialog({
    super.key,
    required this.message,
    this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    final l10n = S.of(context);
    
    return AlertDialog(
      backgroundColor: context.containerBackgroundColor,
      title: Text(
        l10n.error,
        style: TextStyle(color: context.primaryTextColor),
      ),
      content: Text(
        message,
        style: TextStyle(color: context.secondaryTextColor),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: Text(
            l10n.close,
            style: TextStyle(color: context.accentColor),
          ),
        ),
        if (onRetry != null)
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              onRetry!();
            },
            child: Text(
              l10n.retry,
              style: TextStyle(color: context.secondaryAccentColor),
            ),
          ),
      ],
    );
  }
}

Future<void> showErrorDialog(
  BuildContext context, {
  required String message,
  VoidCallback? onRetry,
}) async {
  return showDialog(
    context: context,
    builder: (context) => ErrorDialog(
      message: message,
      onRetry: onRetry,
    ),
  );
} 