import 'dart:io';

void main() {
  // The debug token from the app
  const String debugToken = "A86404A5-FB8C-4439-8AEA-B9106C90ABB0";
  
  print('\n');
  print('============================================================');
  print('  App Check Debug Token Registration Helper');
  print('============================================================');
  print('\n');
  print('To make App Check work in debug mode, you need to register');
  print('the exact debug token from your app in Firebase Console.');
  print('\n');
  print('STEP 1: Copy this EXACT token (no extra spaces):');
  print('         $debugToken');
  print('\n');
  print('STEP 2: Go to Firebase Console:');
  print('         https://console.firebase.google.com/');
  print('\n');
  print('STEP 3: Navigate to your project > Project Settings > App Check');
  print('\n');
  print('STEP 4: Click "Manage debug tokens"');
  print('\n');
  print('STEP 5: Delete any existing debug tokens first!');
  print('\n');
  print('STEP 6: Add a new debug token:');
  print('         - Enter any name (e.g. "AndroidDebug")');
  print('         - Paste the EXACT token value from step 1');
  print('         - Click "Create"');
  print('\n');
  print('STEP 7: Verify in your app by going to Profile > Admin section > "Verify App Check"');
  print('\n');
  print('TROUBLESHOOTING TIPS:');
  print(' - Make sure there are NO extra spaces in the token');
  print(' - Try uninstalling and reinstalling your app');
  print(' - Set App Check enforcement to "Optional" for testing');
  print(' - Wait 5-15 minutes if you hit rate limits');
  print('\n');
  print('============================================================');
  print('\n');
  
  // Copy to clipboard if possible
  try {
    if (Platform.isWindows) {
      Process.runSync('cmd', ['/c', 'echo', debugToken, '|', 'clip']);
      print('✅ Debug token copied to clipboard!');
    } else if (Platform.isMacOS) {
      Process.runSync('bash', ['-c', 'echo "$debugToken" | pbcopy']);
      print('✅ Debug token copied to clipboard!');
    } else if (Platform.isLinux) {
      Process.runSync('bash', ['-c', 'echo "$debugToken" | xclip -selection clipboard']);
      print('✅ Debug token copied to clipboard!');
    } else {
      print('Press Ctrl+C to copy the debug token manually.');
    }
  } catch (e) {
    print('Press Ctrl+C to copy the debug token manually.');
  }
  
  print('\n');
} 