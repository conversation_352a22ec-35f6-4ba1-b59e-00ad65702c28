/**
 * App Check Management Tool
 * 
 * This script helps diagnose and fix issues with Firebase App Check.
 * It requires Firebase Admin SDK and appropriate permissions.
 */

const admin = require('firebase-admin');
const fs = require('fs');
const readline = require('readline');

// Path to your service account key file
const SERVICE_ACCOUNT_PATH = './service-account-key.json';

// The debug token value from your application
const DEBUG_TOKEN = 'A86404A5-FB8C-4439-8AEA-B9106C90ABB0';

// Initialize the readline interface
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Check if service account exists
function checkServiceAccount() {
  try {
    if (!fs.existsSync(SERVICE_ACCOUNT_PATH)) {
      console.log('\x1b[31mError: Service account key file not found.\x1b[0m');
      console.log(`Please download a service account key from Firebase Console and save it as ${SERVICE_ACCOUNT_PATH}`);
      console.log('1. Go to Firebase Console > Project Settings > Service Accounts');
      console.log('2. Click "Generate new private key"');
      console.log('3. Save the downloaded file as service-account-key.json in this directory');
      return false;
    }
    return true;
  } catch (err) {
    console.error('Error checking service account:', err);
    return false;
  }
}

// Initialize Firebase Admin SDK
function initializeFirebase() {
  try {
    const serviceAccount = require(SERVICE_ACCOUNT_PATH);
    
    admin.initializeApp({
      credential: admin.credential.cert(serviceAccount)
    });
    
    console.log('\x1b[32m✓ Firebase Admin SDK initialized successfully\x1b[0m');
    return true;
  } catch (error) {
    console.error('\x1b[31mFailed to initialize Firebase Admin SDK:\x1b[0m', error.message);
    return false;
  }
}

// Get App Check status
async function getAppCheckStatus() {
  try {
    console.log('\nGetting App Check configuration...');
    
    // Note: The Firebase Admin SDK doesn't provide direct methods to manage app check
    // However, we can list projects and apps to ensure everything is set up correctly
    
    const projectId = admin.app().options.projectId;
    console.log(`\nProject ID: ${projectId}`);
    
    // Print current debug token from this script
    console.log('\nDebug token currently in use by your app:');
    console.log(`\x1b[33m${DEBUG_TOKEN}\x1b[0m`);
    
    console.log('\n\x1b[33mNote: The Firebase Admin SDK does not provide direct methods to manage App Check debug tokens.\x1b[0m');
    console.log('Please use the Firebase Console to manage debug tokens:');
    console.log('1. Go to Firebase Console > Project Settings > App Check');
    console.log('2. Click "Manage debug tokens"');
    console.log('3. Ensure that the debug token matches the one in your app: ' + DEBUG_TOKEN);
    
    return true;
  } catch (error) {
    console.error('\x1b[31mError getting App Check status:\x1b[0m', error);
    return false;
  }
}

// List Android apps in the project
async function listAndroidApps() {
  try {
    console.log('\nListing Android apps in your project...');
    
    const androidApps = await admin.projectManagement().listAndroidApps();
    
    if (androidApps.length === 0) {
      console.log('\x1b[33mNo Android apps found in this project.\x1b[0m');
      return;
    }
    
    console.log(`\nFound ${androidApps.length} Android apps:`);
    
    for (const app of androidApps) {
      const appDetails = await app.getMetadata();
      console.log(`\nApp: ${appDetails.displayName || 'Unnamed app'}`);
      console.log(`Package Name: ${appDetails.packageName}`);
      console.log(`App ID: ${app.appId}`);
    }
    
    console.log('\n\x1b[33mIMPORTANT: Make sure your app\'s package name matches one of these apps.\x1b[0m');
    console.log('App Check requires the package name to match exactly for token validation.');
    
    return true;
  } catch (error) {
    console.error('\x1b[31mError listing Android apps:\x1b[0m', error);
    return false;
  }
}

// Check project settings
async function checkProjectSettings() {
  try {
    console.log('\nChecking project settings...');
    
    const projectId = admin.app().options.projectId;
    
    console.log(`\nProject ID: ${projectId}`);
    console.log('\nIMPORTANT MANUAL CHECKS:');
    console.log('1. Go to Firebase Console > Project Settings > App Check');
    console.log('2. Make sure your app is listed under "Registered apps"');
    console.log('3. Verify that App Check is enabled for your app');
    console.log('4. For debugging: make sure the debug token is registered properly');
    console.log(`5. The debug token should exactly match: ${DEBUG_TOKEN}`);
    
    console.log('\nRECOMMENDED SETTINGS:');
    console.log('- While testing: set enforcement to "Optional" for all services');
    console.log('- For production: set enforcement to "Required" for sensitive services');
    
    return true;
  } catch (error) {
    console.error('\x1b[31mError checking project settings:\x1b[0m', error);
    return false;
  }
}

// Print diagnostic information
function printDiagnosticInfo() {
  console.log('\n========== DIAGNOSTIC INFORMATION ==========');
  console.log('\nApp Check Debug Token:');
  console.log(DEBUG_TOKEN);
  
  console.log('\nCommon Issues and Solutions:');
  console.log('1. "App attestation failed" error:');
  console.log('   → Your debug token is not properly registered in Firebase Console');
  console.log('   → Solution: Register the exact token in Firebase Console > Project Settings > App Check > Manage debug tokens');
  
  console.log('\n2. "Too many attempts" error:');
  console.log('   → You\'re hitting the rate limit for App Check token requests');
  console.log('   → Solution: Wait 5-15 minutes before trying again');
  
  console.log('\n3. Authentication works but functions fail with App Check errors:');
  console.log('   → Your functions have "App Check Required" but the token isn\'t being validated');
  console.log('   → Solution: Set functions to "App Check Optional" while testing');
  
  console.log('\n4. Debug token doesn\'t work:');
  console.log('   → Clear your app data/reinstall the app');
  console.log('   → Verify the token in Firebase Console exactly matches the app code');
  console.log('   → Make sure automatic token refresh is enabled');
  
  console.log('\n============================================');
}

// Main menu
function displayMenu() {
  console.log('\n========== APP CHECK MANAGEMENT TOOL ==========');
  console.log('1. Get App Check status');
  console.log('2. List Android apps in project');
  console.log('3. Check project settings');
  console.log('4. Print diagnostic information');
  console.log('5. Exit');
  console.log('=============================================');
  
  rl.question('Select an option (1-5): ', async (answer) => {
    switch (answer) {
      case '1':
        await getAppCheckStatus();
        displayMenu();
        break;
      case '2':
        await listAndroidApps();
        displayMenu();
        break;
      case '3':
        await checkProjectSettings();
        displayMenu();
        break;
      case '4':
        printDiagnosticInfo();
        displayMenu();
        break;
      case '5':
        console.log('Exiting...');
        rl.close();
        process.exit(0);
        break;
      default:
        console.log('Invalid option. Please try again.');
        displayMenu();
        break;
    }
  });
}

// Main function
async function main() {
  console.log('\n🔍 App Check Management Tool 🔍');
  
  if (!checkServiceAccount()) {
    rl.close();
    return;
  }
  
  if (!initializeFirebase()) {
    rl.close();
    return;
  }
  
  displayMenu();
}

// Run the main function
main().catch(error => {
  console.error('Error:', error);
  rl.close();
}); 