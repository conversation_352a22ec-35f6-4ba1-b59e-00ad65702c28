# Oil Change Tracker App - New Features PRD

## Overview
This PRD outlines three new features to enhance the Oil Change Tracker app:
1. Maintenance Documentation: Adding photo capture for receipts/invoices to maintenance records
2. Maintenance Editing: Allowing users to edit existing maintenance records
3. Insurance Documentation: Creating a separate tab for managing insurance documents

## 1. Maintenance Documentation Feature

### Purpose
Allow users to capture, attach, and view photos of receipts or invoices when recording or viewing maintenance activities.

### User Stories
- As a user, I want to take a photo of my maintenance receipt directly within the app when adding a new maintenance record
- As a user, I want to select an existing photo from my gallery to attach to a maintenance record
- As a user, I want to view the receipt photo when reviewing a maintenance record
- As a user, I want to enlarge/zoom the photo view for better readability

### Requirements
- Add a "Add Receipt Photo" button in the add maintenance screen
- Implement camera integration for capturing photos directly
- Implement gallery selection for choosing existing photos
- Store photos securely with the maintenance record
- Add photo viewing capability in maintenance details view
- Support for deleting or replacing photos
- Implement permission handling for camera and storage

## 2. Maintenance Editing Feature

### Purpose
Allow users to edit existing maintenance records to correct errors or add additional information.

### User Stories
- As a user, I want to edit the details of a previously recorded maintenance event
- As a user, I want to add a receipt photo to an existing maintenance record that didn't have one
- As a user, I want to update information like cost, date, or description of maintenance
- As a user, I want a confirmation before saving changes to ensure data integrity

### Requirements
- Add an "Edit" action for each maintenance record
- Create an edit screen that pre-populates with existing maintenance data
- Implement form validation similar to add maintenance flow
- Support for adding/changing photos for existing records
- Display a confirmation dialog before saving changes
- Update the maintenance history view after an edit is completed

## 3. Insurance Documentation Feature

### Purpose
Create a dedicated section for users to manage insurance-related documents, policies, and information.

### User Stories
- As a user, I want to store my insurance policy details in the app
- As a user, I want to capture and store photos of my insurance documents
- As a user, I want to track my policy renewal dates
- As a user, I want to view and manage all my insurance information in one place

### Requirements
- Create a new "Insurance" tab in the main navigation
- Design an insurance dashboard to display policy information
- Implement form for adding basic insurance details (provider, policy number, dates)
- Add photo capture capability for insurance documents
- Create a document viewer for insurance documents
- Implement renewal date tracking with optional reminders
- Allow editing and updating of insurance information

## Technical Considerations
- Use Firebase Storage for securely storing document images
- Implement image compression to reduce storage usage
- Ensure proper permission handling for camera access
- Consider offline access to previously viewed documents
- Implement proper UI components for image viewing with zooming capability

## Success Metrics
- User engagement with photo documentation features
- Reduction in manual data entry errors
- Increased user retention due to comprehensive vehicle documentation

## Out of Scope
- Document OCR for automatic data extraction
- Direct communication with insurance providers
- Claim filing workflow within the app 