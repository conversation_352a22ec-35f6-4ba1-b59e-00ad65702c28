import 'dart:io';
import 'dart:async';

// The debug token value from your application
const String debugToken = "A86404A5-FB8C-4439-8AEA-B9106C90ABB0";

Future<void> main() async {
  print("\n🔍 App Check Debug Token Verification Tool 🔍\n");
  print("This tool helps verify if your App Check debug token is correctly registered.\n");
  
  print("Debug Token: $debugToken");
  print("\nVerification Steps:");
  
  // Step 1: Check if Firebase CLI is installed
  print("\n1. Checking if Firebase CLI is installed...");
  try {
    final result = await Process.run('firebase', ['--version']);
    if (result.exitCode == 0) {
      print("✅ Firebase CLI is installed: ${result.stdout}");
    } else {
      print("⚠️ Firebase CLI is not installed or not in PATH.");
      print("Please install Firebase CLI with: npm install -g firebase-tools");
      return;
    }
  } catch (e) {
    print("❌ Error checking Firebase CLI: $e");
    print("Please install Firebase CLI with: npm install -g firebase-tools");
    return;
  }
  
  // Step 2: Check if logged in
  print("\n2. Checking Firebase login status...");
  try {
    final result = await Process.run('firebase', ['projects:list']);
    if (result.exitCode == 0 && !result.stdout.toString().contains("Error:")) {
      print("✅ Firebase CLI is logged in.");
    } else {
      print("⚠️ Not logged in to Firebase CLI.");
      print("Please login with: firebase login");
      return;
    }
  } catch (e) {
    print("❌ Error checking login status: $e");
    return;
  }
  
  // Step 3: Verification instructions
  print("\n3. Manual verification steps (Firebase CLI cannot directly verify app check tokens):");
  print("   a. Open Firebase Console: https://console.firebase.google.com/");
  print("   b. Navigate to your project");
  print("   c. Go to Project Settings > App Check");
  print("   d. Check if the following debug token is registered:");
  print("      $debugToken");
  print("\n   Note: If the token is not registered or is different, please register it now:");
  print("   1. Click 'Manage debug tokens'");
  print("   2. Delete any existing debug tokens");
  print("   3. Add a new debug token with any name and the exact value: $debugToken");
  
  // Step 4: Firebase Console instructions
  print("\n4. Important configurations to check in Firebase Console:");
  print("   ✓ Make sure App Check enforcement is properly configured:");
  print("   ✓ For Authentication: Required or Optional");
  print("   ✓ For Firestore: Required or Optional");
  print("   ✓ For Storage: Required or Optional");
  print("   ✓ For Functions: Optional during testing");
  
  print("\n5. Testing with your app:");
  print("   ✓ Run the app in debug mode");
  print("   ✓ Navigate to the Profile screen");
  print("   ✓ Use the 'Verify App Check' button in the admin section");
  print("   ✓ Check logs for detailed diagnostics");
  
  print("\n🔍 Verification complete. Please follow the manual steps above to ensure your debug token is properly registered.");
} 