/**
 * Debug Token Management Tool
 * 
 * This script helps manage Firebase App Check debug tokens.
 * It uses the Firebase CLI, which must be installed and authenticated.
 */

const { execSync } = require('child_process');
const fs = require('fs');
const readline = require('readline');

// The debug token value from your application
const DEBUG_TOKEN = 'A86404A5-FB8C-4439-8AEA-B9106C90ABB0';

// Initialize the readline interface
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Check if Firebase CLI is installed
function checkFirebaseCLI() {
  try {
    console.log('Checking if Firebase CLI is installed...');
    const version = execSync('firebase --version', { encoding: 'utf8' });
    console.log(`Firebase CLI version: ${version.trim()}`);
    return true;
  } catch (error) {
    console.error('\x1b[31mFirebase CLI is not installed or not in PATH.\x1b[0m');
    console.log('Please install Firebase CLI with: npm install -g firebase-tools');
    return false;
  }
}

// Check if logged in to Firebase
function checkFirebaseLogin() {
  try {
    console.log('Checking Firebase login status...');
    execSync('firebase projects:list', { encoding: 'utf8' });
    console.log('\x1b[32m✓ Logged in to Firebase CLI\x1b[0m');
    return true;
  } catch (error) {
    console.error('\x1b[31mNot logged in to Firebase CLI.\x1b[0m');
    console.log('Please login with: firebase login');
    return false;
  }
}

// List Firebase projects
function listProjects() {
  try {
    console.log('\nListing Firebase projects...');
    const projects = execSync('firebase projects:list', { encoding: 'utf8' });
    console.log(projects);
  } catch (error) {
    console.error('\x1b[31mError listing projects:\x1b[0m', error.message);
  }
}

// Print debug token info
function showDebugToken() {
  console.log('\n========== DEBUG TOKEN INFORMATION ==========');
  console.log('\nCurrent debug token in your app:');
  console.log(`\x1b[33m${DEBUG_TOKEN}\x1b[0m`);
  
  console.log('\nThis token should be registered in the Firebase Console:');
  console.log('1. Go to Firebase Console > Project Settings > App Check');
  console.log('2. Click "Manage debug tokens"');
  console.log('3. Add a new debug token with the exact value above');
  
  console.log('\nIMPORTANT: The token must match EXACTLY (no extra spaces)');
  console.log('============================================');
}

// Open Firebase Console
function openFirebaseConsole() {
  try {
    console.log('\nOpening Firebase Console in your browser...');
    
    const platform = process.platform;
    
    if (platform === 'win32') {
      execSync('start https://console.firebase.google.com/');
    } else if (platform === 'darwin') {
      execSync('open https://console.firebase.google.com/');
    } else {
      execSync('xdg-open https://console.firebase.google.com/');
    }
    
    console.log('\x1b[32m✓ Opened Firebase Console in browser\x1b[0m');
  } catch (error) {
    console.error('\x1b[31mError opening Firebase Console:\x1b[0m', error.message);
  }
}

// Generate install script for firebase-admin
function generateAdminSetupScript() {
  try {
    const scriptContent = `#!/bin/bash
echo "Setting up Firebase Admin SDK..."
mkdir -p admin-setup
cd admin-setup

# Initialize npm
npm init -y

# Install firebase-admin
npm install firebase-admin

# Create a sample script
cat > app-check-admin.js << 'EOF'
const admin = require('firebase-admin');
const fs = require('fs');

// Path to your service account key file
const SERVICE_ACCOUNT_PATH = './service-account-key.json';

// Check if service account exists
if (!fs.existsSync(SERVICE_ACCOUNT_PATH)) {
  console.error('Error: Service account key file not found.');
  console.log(\`Please download a service account key from Firebase Console and save it as \${SERVICE_ACCOUNT_PATH}\`);
  process.exit(1);
}

// Initialize Firebase Admin SDK
try {
  const serviceAccount = require(SERVICE_ACCOUNT_PATH);
  
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount)
  });
  
  console.log('Firebase Admin SDK initialized successfully');
} catch (error) {
  console.error('Failed to initialize Firebase Admin SDK:', error.message);
  process.exit(1);
}

// Print App Check debug information
async function printAppCheckInfo() {
  try {
    console.log('\\nApp Check Debug Information:');
    
    const projectId = admin.app().options.projectId;
    console.log(\`Project ID: \${projectId}\`);
    
    // List Android apps
    const androidApps = await admin.projectManagement().listAndroidApps();
    console.log(\`\\nFound \${androidApps.length} Android apps:\`);
    
    for (const app of androidApps) {
      const appDetails = await app.getMetadata();
      console.log(\`\\nApp: \${appDetails.displayName || 'Unnamed app'}\`);
      console.log(\`Package Name: \${appDetails.packageName}\`);
      console.log(\`App ID: \${app.appId}\`);
    }
    
    console.log('\\nNote: Debug tokens must be managed in the Firebase Console!');
  } catch (error) {
    console.error('Error getting app info:', error);
  }
}

// Run the function
printAppCheckInfo().then(() => {
  console.log('\\nDone!');
  process.exit(0);
}).catch(error => {
  console.error('Error:', error);
  process.exit(1);
});
EOF

echo "Setup complete! To use the script:"
echo "1. Download a service account key from Firebase Console"
echo "2. Save it as 'service-account-key.json' in the admin-setup directory"
echo "3. Run 'node app-check-admin.js'"
`;

    fs.writeFileSync('setup-admin-sdk.sh', scriptContent, { mode: 0o755 });
    console.log('\nCreated setup script: setup-admin-sdk.sh');
    console.log('Run this script to set up the Firebase Admin SDK.');
  } catch (error) {
    console.error('\x1b[31mError generating setup script:\x1b[0m', error.message);
  }
}

// Show manual instructions
function showManualInstructions() {
  console.log('\n========== MANUAL SETUP INSTRUCTIONS ==========');
  console.log('\nTo manually register your debug token in Firebase Console:');
  console.log('1. Go to Firebase Console > Project Settings > App Check');
  console.log('2. Click "Manage debug tokens"');
  console.log('3. Add a debug token with the following value:');
  console.log(`\x1b[33m${DEBUG_TOKEN}\x1b[0m`);
  
  console.log('\nTo verify App Check is working correctly:');
  console.log('1. Run your app in debug mode');
  console.log('2. Use the "Verify App Check" feature in the Profile screen');
  console.log('3. Check the app logs for detailed diagnostics');
  
  console.log('\nIf you encounter issues:');
  console.log('- Make sure the debug token in Firebase Console matches EXACTLY with the app');
  console.log('- Uninstall and reinstall the app to clear cached tokens');
  console.log('- Set App Check enforcement to "Optional" while testing');
  console.log('- Wait 5-15 minutes if you hit "Too many attempts" errors');
  
  console.log('\n================================================');
}

// Main menu
function displayMenu() {
  console.log('\n========== DEBUG TOKEN MANAGEMENT TOOL ==========');
  console.log('1. List Firebase projects');
  console.log('2. Show debug token information');
  console.log('3. Open Firebase Console in browser');
  console.log('4. Generate Firebase Admin SDK setup script');
  console.log('5. Show manual setup instructions');
  console.log('6. Exit');
  console.log('===============================================');
  
  rl.question('Select an option (1-6): ', answer => {
    switch (answer) {
      case '1':
        listProjects();
        displayMenu();
        break;
      case '2':
        showDebugToken();
        displayMenu();
        break;
      case '3':
        openFirebaseConsole();
        displayMenu();
        break;
      case '4':
        generateAdminSetupScript();
        displayMenu();
        break;
      case '5':
        showManualInstructions();
        displayMenu();
        break;
      case '6':
        console.log('Exiting...');
        rl.close();
        break;
      default:
        console.log('Invalid option. Please try again.');
        displayMenu();
        break;
    }
  });
}

// Main function
function main() {
  console.log('\n🔑 Debug Token Management Tool 🔑');
  
  if (!checkFirebaseCLI()) {
    rl.close();
    return;
  }
  
  if (!checkFirebaseLogin()) {
    rl.question('Do you want to login now? (y/n): ', answer => {
      if (answer.toLowerCase() === 'y') {
        try {
          console.log('Launching Firebase login...');
          execSync('firebase login', { stdio: 'inherit' });
          displayMenu();
        } catch (error) {
          console.error('\x1b[31mLogin failed:\x1b[0m', error.message);
          rl.close();
        }
      } else {
        console.log('Exiting...');
        rl.close();
      }
    });
    return;
  }
  
  displayMenu();
}

// Run the main function
main(); 