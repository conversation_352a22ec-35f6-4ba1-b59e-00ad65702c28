{"ar": ["privacyPolicyUse", "privacyPolicyUseText", "privacyPolicyStorage", "privacyPolicyStorageText", "privacyPolicyRights", "privacyPolicyRightsText", "privacyPolicyChildren", "privacyPolicyChildrenText", "privacyPolicyThirdParty", "privacyPolicyThirdPartyText", "privacyPolicyChanges", "privacyPolicyChangesText", "indemnification", "indemnificationText", "takingP<PERSON><PERSON>", "noPhotoTaken", "selectingPhoto", "noPhotoSelected", "networkError", "checkInternetConnection", "networkConnectionError", "networkConnectionLost"]}