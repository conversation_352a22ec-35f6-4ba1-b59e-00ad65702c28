# Oil Change Tracker App

## Voice Input & Subscription Features

This document outlines the voice input functionality and subscription model implemented in the Oil Change Tracker app.

### Voice Input Functionality

The voice input feature allows users to add oil changes, maintenance records, cars, and update mileage using natural voice commands. This feature is available to premium subscribers.

#### Key Components:

1. **Voice Input Service**: Handles speech recognition using the SpeechToText package.
2. **Voice Command Processor**: Extracts structured data from spoken commands.
3. **Voice Input Dialog**: UI for capturing and displaying voice commands.
4. **Voice Input FAB**: Floating action button for quick access to voice input.
5. **Voice Commands Help**: Widget showing example commands users can try.

#### Example Voice Commands:

- "Add oil change for my Toyota Camry at 50000 miles with synthetic 5W-30 oil"
- "Record brake service for my Honda at 60000 miles"
- "Add my 2018 Ford Focus with 45000 miles"
- "Update mileage for my Nissan to 35000 miles"

#### Implementation Details:

- Speech recognition is handled by the SpeechToText package
- Regular expressions are used to extract structured data from voice commands
- Voice commands are processed asynchronously to avoid UI blocking
- Subscription status is checked before allowing voice input
- Non-subscribers are shown a subscription promotion dialog

### Subscription Model

The app implements a tiered subscription model with three levels:

#### Subscription Tiers:

1. **Free**:
   - Basic oil change tracking
   - Ad-supported
   - Limited to 3 vehicles

2. **Premium** ($2.99/month or $24.99/year):
   - Voice input functionality
   - Ad-free experience
   - Unlimited vehicles
   - Enhanced analytics

3. **Family** ($4.99/month or $39.99/year):
   - All Premium features
   - Family sharing (up to 5 members)
   - Priority support

#### Implementation Details:

- Subscription state is managed through a Riverpod provider
- In-app purchases are handled using the in_app_purchase package
- Users can start a 7-day free trial of Premium
- Subscription status is stored in the user model
- Purchase restoration is supported

### Integration Points:

- Voice input FAB is added to key screens (dashboard, oil change history, car details)
- Subscription status check is performed before activating voice input
- Subscription promotion dialog is shown when non-subscribers try to use premium features
- Subscription management is accessible from the profile screen

### Future Improvements:

- Add more sophisticated natural language processing
- Support for more complex voice commands
- Multi-language support for voice commands
- Offline voice command processing 