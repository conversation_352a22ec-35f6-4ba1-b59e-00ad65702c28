import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:go_router/go_router.dart';

import 'package:oil_change_tracker/features/subscription/presentation/screens/welcome_subscription_screen.dart';
import 'package:oil_change_tracker/features/subscription/providers/subscription_provider.dart';
import 'package:oil_change_tracker/features/subscription/services/subscription_service.dart';
import 'package:oil_change_tracker/features/subscription/models/subscription_tier.dart';
import 'package:oil_change_tracker/core/providers/auth_providers.dart' as core_auth;
import 'package:oil_change_tracker/generated/app_localizations.dart';

import 'welcome_subscription_screen_test.mocks.dart';

@GenerateMocks([
  SubscriptionService,
  SubscriptionNotifier,
  GoRouter,
])
void main() {
  group('WelcomeSubscriptionScreen', () {
    late MockSubscriptionService mockSubscriptionService;
    late MockSubscriptionNotifier mockSubscriptionNotifier;
    late MockGoRouter mockGoRouter;

    setUp(() {
      mockSubscriptionService = MockSubscriptionService();
      mockSubscriptionNotifier = MockSubscriptionNotifier();
      mockGoRouter = MockGoRouter();
    });

    Widget createTestWidget() {
      return ProviderScope(
        overrides: [
          subscriptionServiceProvider.overrideWithValue(mockSubscriptionService),
          subscriptionProvider.overrideWith((ref) => mockSubscriptionNotifier),
          core_auth.isNewSignupProvider.overrideWith((ref) => StateController(false)),
          core_auth.welcomeSubscriptionShownProvider.overrideWith((ref) => 
            core_auth.WelcomeSubscriptionShownNotifier(MockStorageService())),
        ],
        child: MaterialApp.router(
          routerConfig: mockGoRouter,
          localizationsDelegates: S.localizationsDelegates,
          supportedLocales: S.supportedLocales,
          home: const WelcomeSubscriptionScreen(),
        ),
      );
    }

    testWidgets('should display welcome subscription screen with all elements', (tester) async {
      // Arrange
      when(mockSubscriptionService.refreshProducts()).thenAnswer((_) async {});
      when(mockSubscriptionService.getFormattedPrice(any, any))
          .thenAnswer((_) async => '2.99 EGP');

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('Welcome to Premium!'), findsOneWidget);
      expect(find.text('Start 7-Day Free Trial'), findsOneWidget);
      expect(find.text('Subscribe to Annual Plan'), findsOneWidget);
      expect(find.text('Continue with Free Version'), findsOneWidget);
      expect(find.text('Choose Your Plan'), findsOneWidget);
    });

    testWidgets('should show monthly plan when monthly is selected', (tester) async {
      // Arrange
      when(mockSubscriptionService.refreshProducts()).thenAnswer((_) async {});
      when(mockSubscriptionService.getFormattedPrice(any, true))
          .thenAnswer((_) async => '2.99 EGP');
      when(mockSubscriptionService.getFormattedPrice(any, false))
          .thenAnswer((_) async => '24.99 EGP');

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Tap on monthly plan
      await tester.tap(find.text('Monthly Premium'));
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('Subscribe to Monthly Plan'), findsOneWidget);
      expect(find.text('Monthly Selected'), findsOneWidget);
    });

    testWidgets('should call startFreeTrial when trial button is pressed', (tester) async {
      // Arrange
      when(mockSubscriptionService.refreshProducts()).thenAnswer((_) async {});
      when(mockSubscriptionService.getFormattedPrice(any, any))
          .thenAnswer((_) async => '2.99 EGP');
      when(mockSubscriptionNotifier.startFreeTrial(any))
          .thenAnswer((_) async => true);

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      await tester.tap(find.text('Start 7-Day Free Trial'));
      await tester.pumpAndSettle();

      // Assert
      verify(mockSubscriptionNotifier.startFreeTrial(SubscriptionTier.premium)).called(1);
    });

    testWidgets('should call purchase when subscribe button is pressed', (tester) async {
      // Arrange
      when(mockSubscriptionService.refreshProducts()).thenAnswer((_) async {});
      when(mockSubscriptionService.getFormattedPrice(any, any))
          .thenAnswer((_) async => '2.99 EGP');
      when(mockSubscriptionNotifier.purchase(any, monthly: any))
          .thenAnswer((_) async => true);

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      await tester.tap(find.text('Subscribe to Annual Plan'));
      await tester.pumpAndSettle();

      // Assert
      verify(mockSubscriptionNotifier.purchase(SubscriptionTier.premium, monthly: false)).called(1);
    });

    testWidgets('should disable buttons during loading', (tester) async {
      // Arrange
      when(mockSubscriptionService.refreshProducts()).thenAnswer((_) async {});
      when(mockSubscriptionService.getFormattedPrice(any, any))
          .thenAnswer((_) async => '2.99 EGP');
      when(mockSubscriptionNotifier.startFreeTrial(any))
          .thenAnswer((_) async {
            await Future.delayed(const Duration(seconds: 2));
            return true;
          });

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      await tester.tap(find.text('Start 7-Day Free Trial'));
      await tester.pump(); // Don't wait for completion

      // Assert
      final subscribeButton = find.text('Subscribe to Annual Plan');
      final continueButton = find.text('Continue with Free Version');
      
      expect(tester.widget<OutlinedButton>(find.ancestor(
        of: subscribeButton,
        matching: find.byType(OutlinedButton),
      )).onPressed, isNull);
      
      expect(tester.widget<TextButton>(find.ancestor(
        of: continueButton,
        matching: find.byType(TextButton),
      )).onPressed, isNull);
    });
  });
}

// Mock storage service for testing
class MockStorageService extends Mock {
  bool getBool(String key) => false;
  Future<void> setBool(String key, bool value) async {}
}
