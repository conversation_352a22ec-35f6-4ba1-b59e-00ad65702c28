# Voice Input & Subscription Plan for Oil Change Tracker

## Overview

This document outlines the implementation plan for adding voice input capabilities to the Oil Change Tracker app using Google's Speech-to-Text API, along with a subscription model to monetize this premium feature.

## 1. Voice Input Feature Implementation

### 1.1 Technical Requirements

#### Dependencies to Add
```yaml
# Voice Recognition
speech_to_text: ^6.6.0        # For on-device speech recognition
google_speech: ^2.2.0         # For Google Cloud Speech-to-Text API
dialog_flowtter: ^0.3.3       # For intent recognition and NLP

# For subscription management
in_app_purchase: ^3.1.11      # For handling in-app purchases
purchases_flutter: ^6.19.0    # RevenueCat integration (alternative option)
```

#### Google Cloud Setup
1. Enable Speech-to-Text API in Google Cloud Console
2. Create service account and generate API key
3. Configure API restrictions for Android app
4. Store API key securely using Firebase Remote Config

### 1.2 Voice Input Architecture

#### Components
1. **VoiceInputService**: Core service to handle speech recognition
2. **VoiceCommandProcessor**: Process recognized text into structured commands
3. **VoiceInputButton**: UI component for triggering voice input
4. **VoiceConfirmationDialog**: UI for confirming recognized commands
5. **VoiceInputProvider**: Riverpod provider to manage voice input state

#### Data Flow
1. User taps voice input button on dashboard
2. Voice recording starts with visual feedback
3. Speech is converted to text using Speech-to-Text API
4. Text is processed to extract intents and entities
5. Extracted data is mapped to appropriate form fields
6. User confirms or edits the extracted information
7. Data is submitted to the appropriate form

### 1.3 Voice Command Grammar

Define structured commands for different actions:

#### Oil Change Commands
- "Add oil change for [car] at [mileage] miles using [oil type] oil"
- "Record oil change for my [car make/model] with [oil quantity] liters of [oil type]"
- "I changed oil in my [car] yesterday with [filter type] filter"

#### Maintenance Commands
- "Add maintenance for [car] at [mileage] miles for [maintenance type]"
- "Record brake replacement for my [car] at [service provider]"
- "I did [maintenance type] on my [car] at [mileage] miles"

#### Car Addition Commands
- "Add a new [year] [make] [model] car with [mileage] miles"
- "Register my [year] [make] [model] with current mileage [mileage]"

### 1.4 UI Implementation

#### Dashboard Integration
- Add a floating action button with microphone icon
- Implement a voice input modal with animation
- Create a results confirmation screen

#### Voice Input Modal
- Animated microphone with sound level visualization
- Cancel button and timeout handling
- Clear instructions for user

#### Confirmation UI
- Display recognized text
- Show structured data in editable form
- Provide confirm/edit options

## 2. Subscription Plan

### 2.1 Subscription Tiers

#### Free Tier (Current)
- Basic oil change tracking
- Manual data entry
- Ad-supported experience
- Limited to 3 vehicles

#### Premium Tier ($2.99/month or $24.99/year)
- Voice input for quick data entry
- Ad-free experience
- Unlimited vehicles
- Enhanced analytics and insights
- Cloud backup and sync
- Priority support

#### Family Tier ($4.99/month or $39.99/year)
- All Premium features
- Share vehicle data with up to 5 family members
- Collaborative maintenance tracking
- Maintenance role assignments
- Family reminders and notifications

### 2.2 Subscription Implementation

#### Technical Components
1. **SubscriptionService**: Handle subscription purchase and verification
2. **SubscriptionProvider**: Riverpod provider for subscription state
3. **FeatureGateProvider**: Control access to premium features
4. **SubscriptionScreen**: UI for managing subscriptions
5. **TrialManager**: Handle free trial periods

#### User Flow
1. User encounters feature gate (voice input button)
2. Promotion explains benefits of subscription
3. User selects subscription tier
4. Payment processed through Google Play Billing
5. Features unlocked immediately upon successful purchase
6. Receipt verified with backend

### 2.3 Backend Changes

#### Firestore Updates
- Add `subscriptions` collection
- Store subscription status, type, expiration
- Track subscription history

#### Cloud Functions
- Implement webhook for subscription events
- Validate receipts with Google Play API
- Handle subscription lifecycle events

## 3. Implementation Plan

### Phase 1: Core Voice Input (2 weeks)
1. Set up Google Cloud Speech-to-Text API
2. Implement basic VoiceInputService
3. Create UI components for voice input
4. Test basic speech recognition

### Phase 2: Command Processing (2 weeks)
1. Implement VoiceCommandProcessor
2. Define and test command patterns
3. Create entity extraction logic
4. Map extracted data to form fields

### Phase 3: Subscription Backend (1 week)
1. Set up Google Play subscription products
2. Implement SubscriptionService
3. Create Firestore schema for subscriptions
4. Implement server-side validation

### Phase 4: Subscription UI (1 week)
1. Design subscription screens
2. Implement feature gates
3. Create subscription management UI
4. Implement trial functionality

### Phase 5: Testing & Refinement (2 weeks)
1. User testing of voice commands
2. Refine command recognition
3. Test subscription flow
4. Performance optimization

### Phase 6: Launch Preparation (1 week)
1. Documentation update
2. Marketing materials
3. App store listing update
4. Staged rollout

## 4. Technical Implementation Details

### 4.1 VoiceInputService

```dart
class VoiceInputService {
  final SpeechToText _speechToText = SpeechToText();
  final BehaviorSubject<VoiceInputState> _stateController = BehaviorSubject.seeded(VoiceInputState.idle);
  final SubscriptionService _subscriptionService;
  
  Stream<VoiceInputState> get stateStream => _stateController.stream;
  VoiceInputState get currentState => _stateController.value;
  
  Future<bool> initialize() async {
    final initialized = await _speechToText.initialize();
    return initialized;
  }
  
  Future<void> startListening() async {
    if (!await _subscriptionService.hasActiveSubscription()) {
      _stateController.add(VoiceInputState.requiresSubscription);
      return;
    }
    
    if (await _speechToText.hasPermission) {
      _stateController.add(VoiceInputState.listening);
      await _speechToText.listen(
        onResult: _onSpeechResult,
        listenFor: const Duration(seconds: 30),
        pauseFor: const Duration(seconds: 3),
        localeId: 'en_US',
      );
    } else {
      final hasPermission = await _speechToText.requestPermission();
      if (hasPermission) {
        await startListening();
      } else {
        _stateController.add(VoiceInputState.permissionDenied);
      }
    }
  }
  
  void stopListening() {
    _speechToText.stop();
    _stateController.add(VoiceInputState.processing);
  }
  
  void _onSpeechResult(SpeechRecognitionResult result) {
    if (result.finalResult) {
      _stateController.add(
        VoiceInputState.result(
          text: result.recognizedWords,
          confidence: result.confidence,
        ),
      );
    }
  }
}
```

### 4.2 Dashboard Integration

```dart
Widget _buildVoiceInputButton(BuildContext context) {
  return Consumer(
    builder: (context, ref, _) {
      final hasSubscription = ref.watch(subscriptionProvider).hasActiveSubscription;
      
      return FloatingActionButton(
        heroTag: 'voiceInputButton',
        backgroundColor: context.secondaryAccentColor,
        onPressed: () {
          if (hasSubscription) {
            _showVoiceInputModal(context);
          } else {
            _showSubscriptionPromotion(context);
          }
        },
        child: const Icon(Icons.mic, color: Colors.white),
      );
    },
  );
}
```

### 4.3 Subscription Provider

```dart
@riverpod
class Subscription extends _$Subscription {
  @override
  Future<SubscriptionState> build() async {
    final subscriptionService = ref.watch(subscriptionServiceProvider);
    return await subscriptionService.getCurrentSubscription();
  }
  
  Future<void> purchaseSubscription(SubscriptionTier tier) async {
    state = const AsyncValue.loading();
    try {
      final subscriptionService = ref.read(subscriptionServiceProvider);
      await subscriptionService.purchase(tier);
      state = AsyncValue.data(await subscriptionService.getCurrentSubscription());
    } catch (e, st) {
      state = AsyncValue.error(e, st);
    }
  }
  
  Future<void> restorePurchases() async {
    state = const AsyncValue.loading();
    try {
      final subscriptionService = ref.read(subscriptionServiceProvider);
      await subscriptionService.restorePurchases();
      state = AsyncValue.data(await subscriptionService.getCurrentSubscription());
    } catch (e, st) {
      state = AsyncValue.error(e, st);
    }
  }
}
```

## 5. Marketing & User Education

### 5.1 In-App Promotion
- Highlight voice input in onboarding
- Add "Pro Tips" cards on dashboard
- Create interactive demo of voice commands
- Show subscription benefits with clear value proposition

### 5.2 User Education
- Provide command examples in help section
- Create tutorial videos for voice input
- Implement contextual hints for first-time users
- Add command suggestions based on usage patterns

## 6. Success Metrics

### 6.1 Voice Input Metrics
- Voice command success rate
- Average time saved per entry
- Feature usage frequency
- User satisfaction ratings

### 6.2 Subscription Metrics
- Conversion rate from free to paid
- Monthly recurring revenue (MRR)
- Customer lifetime value (CLV)
- Churn rate
- Subscription tier distribution

## 7. Future Enhancements

### 7.1 Voice Feature Expansion
- Multi-language support
- Offline voice recognition
- Conversational follow-up questions
- Voice-based search and filtering

### 7.2 Subscription Enhancements
- Referral program
- Family sharing improvements
- Loyalty rewards for long-term subscribers
- Integration with vehicle service providers 