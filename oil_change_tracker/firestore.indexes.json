{"indexes": [{"collectionGroup": "cars", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "make", "order": "ASCENDING"}, {"fieldPath": "model", "order": "ASCENDING"}]}, {"collectionGroup": "maintenance", "queryScope": "COLLECTION", "fields": [{"fieldPath": "carId", "order": "ASCENDING"}, {"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "date", "order": "DESCENDING"}]}, {"collectionGroup": "oil_changes", "queryScope": "COLLECTION", "fields": [{"fieldPath": "carId", "order": "ASCENDING"}, {"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "date", "order": "ASCENDING"}]}, {"collectionGroup": "oil_changes", "queryScope": "COLLECTION", "fields": [{"fieldPath": "carId", "order": "ASCENDING"}, {"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "date", "order": "DESCENDING"}]}, {"collectionGroup": "oilChanges", "queryScope": "COLLECTION", "fields": [{"fieldPath": "carId", "order": "ASCENDING"}, {"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "date", "order": "DESCENDING"}]}], "fieldOverrides": []}