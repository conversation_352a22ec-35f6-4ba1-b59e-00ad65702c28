# Firebase App Check Implementation

This document explains how Firebase App Check is integrated in the Oil Change Tracker app and provides guidance for troubleshooting common issues.

## Debug Token

The app uses the following debug token in development mode:

```
A86404A5-FB8C-4439-8AEA-B9106C90ABB0
```

This token **must** be registered in the Firebase Console for App Check to work in debug/development environments.

## Registering the Debug Token

1. Go to the [Firebase Console](https://console.firebase.google.com/)
2. Select your project
3. Navigate to Project Settings
4. Select the "App Check" tab
5. Find your Android app in the list
6. Click the three dots menu (...) and select "Manage debug tokens"
7. Add the debug token shown above
8. Click "Save"

## App Check Test App

For diagnostics and troubleshooting, use the special test app:

```bash
# Run the test app
flutter run -t lib/test_app_check.dart
```

This launches a dedicated UI for testing App Check functionality and diagnosing issues.

## Common Issues and Solutions

### Permission Denied Errors

If you're seeing "Permission Denied" errors in Firestore operations:

1. Verify that the debug token is correctly registered
2. Make sure the debug token in the app code matches exactly what's in Firebase Console
3. Check if App Check enforcement is set to "Enforce" - consider changing to "Monitor" during development
4. Ensure your Firestore rules include access to the `_app_check_tests` collection:

```
// App Check test collection - required for App Check functionality
match /_app_check_tests/{docId} {
  allow read: if true;
}
```

### App Check Token Not Being Generated

1. Make sure Firebase is properly initialized before App Check
2. Verify that you're using `AndroidProvider.debug` in debug builds
3. Try clearing app data or reinstalling the app
4. Check if you have the latest Firebase SDK versions

## App Check Integration with Custom Backends

If using App Check with custom backend services, include the App Check token in HTTP requests:

```dart
// Example of adding App Check token to HTTP headers
Future<void> callCustomApi() async {
  try {
    final token = await FirebaseAppCheck.instance.getToken();
    if (token != null) {
      final headers = {
        'Content-Type': 'application/json',
        'X-Firebase-AppCheck': token,
      };
      
      // Use headers in your HTTP request
      // ...
    }
  } catch (e) {
    // Handle errors
  }
}
```

## References

- [Firebase App Check Documentation](https://firebase.google.com/docs/app-check)
- [Custom Resource Backend Integration](https://firebase.google.com/docs/app-check/custom-resource-backend)
- [App Check for Android](https://firebase.google.com/docs/app-check/android/debug-provider) 